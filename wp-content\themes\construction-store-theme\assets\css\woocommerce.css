/**
 * WooCommerce styles for Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

/* Product Badges */
.product-badges {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
    line-height: 1;
}

.badge-sale {
    background-color: var(--wp--preset--color--danger);
    color: var(--wp--preset--color--white);
}

.badge-new {
    background-color: var(--wp--preset--color--success);
    color: var(--wp--preset--color--white);
}

.badge-out-of-stock {
    background-color: var(--wp--preset--color--gray-600);
    color: var(--wp--preset--color--white);
}

/* Product Specifications */
.product-specs {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.product-specs .spec {
    font-size: 0.875rem;
    color: var(--wp--preset--color--gray-600);
    line-height: 1.4;
}

/* Quantity Calculator */
.quantity-calculator {
    margin: 20px 0;
    padding: 20px;
    background-color: var(--wp--preset--color--gray-100);
    border-radius: 8px;
    border: 1px solid var(--wp--preset--color--gray-200);
}

.quantity-calculator h4 {
    margin-bottom: 15px;
    color: var(--wp--preset--color--secondary);
}

.calculator-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.calculator-form label {
    font-weight: 600;
    color: var(--wp--preset--color--gray-800);
}

.calculator-form input {
    padding: 8px 12px;
    border: 1px solid var(--wp--preset--color--gray-300);
    border-radius: 4px;
    font-size: 1rem;
}

.calculation-result {
    margin-top: 15px;
    padding: 15px;
    background-color: var(--wp--preset--color--white);
    border-radius: 4px;
    border-left: 4px solid var(--wp--preset--color--primary);
}

/* Technical Specifications Tab */
.technical-specs {
    line-height: 1.6;
}

.technical-specs table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.technical-specs th,
.technical-specs td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--wp--preset--color--gray-200);
}

.technical-specs th {
    background-color: var(--wp--preset--color--gray-100);
    font-weight: 600;
}

/* Installation Guide Tab */
.installation-guide {
    line-height: 1.6;
}

.installation-guide ol,
.installation-guide ul {
    margin-left: 20px;
}

.installation-guide img {
    max-width: 100%;
    height: auto;
    margin: 15px 0;
    border-radius: 4px;
}

/* Breadcrumbs */
.woocommerce-breadcrumb {
    margin-bottom: 20px;
    font-size: 0.875rem;
    color: var(--wp--preset--color--gray-600);
}

.breadcrumb-separator {
    margin: 0 8px;
    color: var(--wp--preset--color--gray-400);
}

.breadcrumb-item:last-child {
    color: var(--wp--preset--color--gray-800);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quantity-calculator {
        margin: 15px 0;
        padding: 15px;
    }
    
    .calculator-form {
        gap: 8px;
    }
    
    .product-specs {
        margin-top: 6px;
        gap: 3px;
    }
    
    .product-specs .spec {
        font-size: 0.8125rem;
    }
    
    .badge {
        padding: 3px 6px;
        font-size: 0.6875rem;
    }
}
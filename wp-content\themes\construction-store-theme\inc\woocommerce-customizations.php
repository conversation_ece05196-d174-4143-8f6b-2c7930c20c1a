<?php
/**
 * WooCommerce customizations for Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Remove default WooCommerce actions and add custom ones
 */
function construction_store_woocommerce_init() {
    // Only run if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return;
    }
    
    // Remove default WooCommerce breadcrumbs (we'll use our own)
    remove_action('woocommerce_before_main_content', 'woocommerce_breadcrumb', 20);
    
    // Customize product loop
    remove_action('woocommerce_shop_loop_item_title', 'woocommerce_template_loop_product_title', 10);
    add_action('woocommerce_shop_loop_item_title', 'construction_store_loop_product_title', 10);
    
    // Add custom product badges
    add_action('woocommerce_before_shop_loop_item_title', 'construction_store_product_badges', 15);
    
    // Add technical specifications to product loop
    add_action('woocommerce_after_shop_loop_item_title', 'construction_store_loop_product_specs', 15);
}
add_action('init', 'construction_store_woocommerce_init');

/**
 * Custom product title for loop
 */
function construction_store_loop_product_title() {
    echo '<h3 class="woocommerce-loop-product__title">' . get_the_title() . '</h3>';
}

/**
 * Add product badges (sale, new, etc.)
 */
function construction_store_product_badges() {
    global $product;
    
    echo '<div class="product-badges">';
    
    // Sale badge with percentage
    if ($product->is_on_sale()) {
        $percentage = '';
        if ($product->get_regular_price() && $product->get_sale_price()) {
            $percentage = round((($product->get_regular_price() - $product->get_sale_price()) / $product->get_regular_price()) * 100);
            $percentage = '-' . $percentage . '%';
        }
        echo '<span class="badge badge-sale">' . ($percentage ? $percentage : __('Oferta', 'construction-store-theme')) . '</span>';
    }
    
    // New product badge (products created in last 30 days)
    $created_date = strtotime($product->get_date_created());
    $thirty_days_ago = strtotime('-30 days');
    if ($created_date > $thirty_days_ago) {
        echo '<span class="badge badge-new">' . __('Novo', 'construction-store-theme') . '</span>';
    }
    
    // Featured product badge
    if ($product->is_featured()) {
        echo '<span class="badge badge-featured">' . __('Destaque', 'construction-store-theme') . '</span>';
    }
    
    // Promotion badge (custom meta field)
    $promotion_text = get_post_meta($product->get_id(), '_construction_promotion_badge', true);
    if ($promotion_text) {
        echo '<span class="badge badge-promotion">' . esc_html($promotion_text) . '</span>';
    }
    
    // Out of stock badge
    if (!$product->is_in_stock()) {
        echo '<span class="badge badge-out-of-stock">' . __('Fora de Estoque', 'construction-store-theme') . '</span>';
    }
    
    // Low stock badge
    $stock_quantity = $product->get_stock_quantity();
    if ($stock_quantity && $stock_quantity <= 5 && $product->is_in_stock()) {
        echo '<span class="badge badge-low-stock">' . __('Últimas Unidades', 'construction-store-theme') . '</span>';
    }
    
    echo '</div>';
    
    // Add quick view button
    echo '<button class="quick-view-button" title="' . __('Visualização Rápida', 'construction-store-theme') . '">';
    echo '<span class="dashicons dashicons-visibility"></span>';
    echo '</button>';
}

/**
 * Add technical specifications to product loop
 */
function construction_store_loop_product_specs() {
    global $product;
    
    $specs = array();
    
    // Get custom fields for technical specifications
    $dimensions = get_post_meta($product->get_id(), '_construction_dimensions', true);
    $material = get_post_meta($product->get_id(), '_construction_material', true);
    $application = get_post_meta($product->get_id(), '_construction_application', true);
    $weight = $product->get_weight();
    
    if ($dimensions) {
        $specs['dimensions'] = $dimensions;
    }
    
    if ($material) {
        $specs['material'] = $material;
    }
    
    if ($weight) {
        $specs['weight'] = $weight . ' kg';
    }
    
    if ($application) {
        $specs['application'] = $application;
    }
    
    if (!empty($specs)) {
        echo '<div class="product-specs-preview">';
        foreach ($specs as $type => $value) {
            $label = construction_store_get_spec_label($type);
            echo '<span class="spec-item ' . esc_attr($type) . '" data-spec="' . esc_attr($type) . '">' . esc_html($label . ': ' . $value) . '</span>';
        }
        echo '</div>';
    }
}

/**
 * Get specification label
 */
function construction_store_get_spec_label($type) {
    $labels = array(
        'dimensions' => __('Dimensões', 'construction-store-theme'),
        'material' => __('Material', 'construction-store-theme'),
        'weight' => __('Peso', 'construction-store-theme'),
        'application' => __('Aplicação', 'construction-store-theme'),
        'coverage_area' => __('Rendimento', 'construction-store-theme'),
        'brand' => __('Marca', 'construction-store-theme')
    );
    
    return isset($labels[$type]) ? $labels[$type] : ucfirst($type);
}

/**
 * Customize WooCommerce checkout fields
 */
function construction_store_checkout_fields($fields) {
    // Add company field to billing
    $fields['billing']['billing_company']['required'] = false;
    $fields['billing']['billing_company']['placeholder'] = __('Nome da empresa (opcional)', 'construction-store-theme');
    
    // Add CNPJ field for Brazilian businesses
    $fields['billing']['billing_cnpj'] = array(
        'label' => __('CNPJ', 'construction-store-theme'),
        'placeholder' => __('00.000.000/0000-00', 'construction-store-theme'),
        'required' => false,
        'class' => array('form-row-wide'),
        'priority' => 35
    );
    
    return $fields;
}
add_filter('woocommerce_checkout_fields', 'construction_store_checkout_fields');

/**
 * Add custom product data tabs
 */
function construction_store_product_tabs($tabs) {
    // Add technical specifications tab
    $tabs['technical_specs'] = array(
        'title' => __('Especificações Técnicas', 'construction-store-theme'),
        'priority' => 15,
        'callback' => 'construction_store_technical_specs_tab_content'
    );
    
    // Add installation guide tab
    $tabs['installation_guide'] = array(
        'title' => __('Guia de Instalação', 'construction-store-theme'),
        'priority' => 20,
        'callback' => 'construction_store_installation_guide_tab_content'
    );
    
    return $tabs;
}
add_filter('woocommerce_product_tabs', 'construction_store_product_tabs');

/**
 * Technical specifications tab content
 */
function construction_store_technical_specs_tab_content() {
    global $product;
    
    $specs = get_post_meta($product->get_id(), '_construction_technical_specs', true);
    
    if ($specs) {
        echo '<div class="technical-specs">';
        echo wp_kses_post($specs);
        echo '</div>';
    } else {
        echo '<p>' . __('Especificações técnicas não disponíveis para este produto.', 'construction-store-theme') . '</p>';
    }
}

/**
 * Installation guide tab content
 */
function construction_store_installation_guide_tab_content() {
    global $product;
    
    $guide = get_post_meta($product->get_id(), '_construction_installation_guide', true);
    
    if ($guide) {
        echo '<div class="installation-guide">';
        echo wp_kses_post($guide);
        echo '</div>';
    } else {
        echo '<p>' . __('Guia de instalação não disponível para este produto.', 'construction-store-theme') . '</p>';
    }
}

/**
 * Customize WooCommerce breadcrumbs
 */
function construction_store_woocommerce_breadcrumbs() {
    if (function_exists('woocommerce_breadcrumb')) {
        woocommerce_breadcrumb(array(
            'delimiter' => ' <span class="breadcrumb-separator">/</span> ',
            'wrap_before' => '<nav class="woocommerce-breadcrumb breadcrumb" aria-label="' . __('Navegação', 'construction-store-theme') . '">',
            'wrap_after' => '</nav>',
            'before' => '<span class="breadcrumb-item">',
            'after' => '</span>',
            'home' => __('Início', 'construction-store-theme'),
        ));
    }
}

/**
 * Add quantity calculator to applicable products
 */
function construction_store_add_quantity_calculator() {
    global $product;
    
    // Check if product has coverage area (paints, cement, flooring, etc.)
    $coverage_area = get_post_meta($product->get_id(), '_construction_coverage_area', true);
    
    if ($coverage_area) {
        echo '<div class="quantity-calculator">';
        echo '<h4>' . __('Calculadora de Quantidade', 'construction-store-theme') . '</h4>';
        echo '<div class="calculator-form">';
        echo '<label for="area-input">' . __('Área a ser coberta (m²):', 'construction-store-theme') . '</label>';
        echo '<input type="number" id="area-input" step="0.1" min="0" placeholder="0.0">';
        echo '<button type="button" id="calculate-quantity" class="button">' . __('Calcular', 'construction-store-theme') . '</button>';
        echo '<div class="calculation-result" style="display: none;"></div>';
        echo '</div>';
        echo '</div>';
    }
}
add_action('woocommerce_single_product_summary', 'construction_store_add_quantity_calculator', 25);

/**
 * Add custom product meta fields
 */
function construction_store_add_product_meta_fields() {
    add_meta_box(
        'construction_product_specs',
        __('Especificações de Construção', 'construction-store-theme'),
        'construction_store_product_specs_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'construction_store_add_product_meta_fields');

/**
 * Product specifications meta box callback
 */
function construction_store_product_specs_callback($post) {
    wp_nonce_field('construction_store_product_specs', 'construction_store_product_specs_nonce');
    
    $dimensions = get_post_meta($post->ID, '_construction_dimensions', true);
    $material = get_post_meta($post->ID, '_construction_material', true);
    $application = get_post_meta($post->ID, '_construction_application', true);
    $coverage_area = get_post_meta($post->ID, '_construction_coverage_area', true);
    $brand = get_post_meta($post->ID, '_construction_brand', true);
    $promotion_badge = get_post_meta($post->ID, '_construction_promotion_badge', true);
    $technical_specs = get_post_meta($post->ID, '_construction_technical_specs', true);
    $installation_guide = get_post_meta($post->ID, '_construction_installation_guide', true);
    
    ?>
    <table class="form-table">
        <tr>
            <th><label for="construction_dimensions"><?php _e('Dimensões', 'construction-store-theme'); ?></label></th>
            <td><input type="text" id="construction_dimensions" name="construction_dimensions" value="<?php echo esc_attr($dimensions); ?>" placeholder="Ex: 10 x 20 x 5 cm" /></td>
        </tr>
        <tr>
            <th><label for="construction_material"><?php _e('Material', 'construction-store-theme'); ?></label></th>
            <td><input type="text" id="construction_material" name="construction_material" value="<?php echo esc_attr($material); ?>" placeholder="Ex: Aço inoxidável" /></td>
        </tr>
        <tr>
            <th><label for="construction_application"><?php _e('Aplicação', 'construction-store-theme'); ?></label></th>
            <td><input type="text" id="construction_application" name="construction_application" value="<?php echo esc_attr($application); ?>" placeholder="Ex: Uso residencial" /></td>
        </tr>
        <tr>
            <th><label for="construction_coverage_area"><?php _e('Rendimento (m²)', 'construction-store-theme'); ?></label></th>
            <td><input type="number" id="construction_coverage_area" name="construction_coverage_area" value="<?php echo esc_attr($coverage_area); ?>" step="0.1" placeholder="Ex: 12.5" /></td>
        </tr>
        <tr>
            <th><label for="construction_brand"><?php _e('Marca', 'construction-store-theme'); ?></label></th>
            <td><input type="text" id="construction_brand" name="construction_brand" value="<?php echo esc_attr($brand); ?>" placeholder="Ex: Bosch" /></td>
        </tr>
        <tr>
            <th><label for="construction_promotion_badge"><?php _e('Badge Promocional', 'construction-store-theme'); ?></label></th>
            <td><input type="text" id="construction_promotion_badge" name="construction_promotion_badge" value="<?php echo esc_attr($promotion_badge); ?>" placeholder="Ex: Lançamento" /></td>
        </tr>
        <tr>
            <th><label for="construction_technical_specs"><?php _e('Especificações Técnicas', 'construction-store-theme'); ?></label></th>
            <td><?php wp_editor($technical_specs, 'construction_technical_specs', array('textarea_rows' => 5)); ?></td>
        </tr>
        <tr>
            <th><label for="construction_installation_guide"><?php _e('Guia de Instalação', 'construction-store-theme'); ?></label></th>
            <td><?php wp_editor($installation_guide, 'construction_installation_guide', array('textarea_rows' => 5)); ?></td>
        </tr>
    </table>
    <?php
}

/**
 * Save custom product meta fields
 */
function construction_store_save_product_meta_fields($post_id) {
    if (!isset($_POST['construction_store_product_specs_nonce']) || 
        !wp_verify_nonce($_POST['construction_store_product_specs_nonce'], 'construction_store_product_specs')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    $fields = array(
        'construction_dimensions',
        'construction_material',
        'construction_application',
        'construction_coverage_area',
        'construction_brand',
        'construction_promotion_badge',
        'construction_technical_specs',
        'construction_installation_guide'
    );
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'construction_store_save_product_meta_fields');

/**
 * Add material calculator to product pages
 */
function construction_store_add_material_calculator() {
    global $product;
    
    // Check if product has coverage area or is calculable material
    $coverage_area = get_post_meta($product->get_id(), '_construction_coverage_area', true);
    $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'slugs'));
    
    // Define calculable categories
    $calculable_categories = array('tintas', 'cimento', 'argamassa', 'pisos', 'azulejos', 'materiais-basicos');
    
    $is_calculable = $coverage_area || array_intersect($product_categories, $calculable_categories);
    
    if ($is_calculable) {
        // Determine product type for calculator
        $product_type = construction_store_get_calculator_product_type($product_categories, $product);
        
        echo '<div class="product-calculator-section">';
        echo '<h3>' . __('Calculadora de Quantidade', 'construction-store-theme') . '</h3>';
        echo '<p>' . __('Calcule a quantidade necessária para seu projeto:', 'construction-store-theme') . '</p>';
        
        // Use shortcode with product-specific parameters
        $shortcode_atts = array(
            'product_type' => $product_type,
            'coverage' => $coverage_area,
            'title' => sprintf(__('Calculadora para %s', 'construction-store-theme'), $product->get_name())
        );
        
        $shortcode = '[material_calculator';
        foreach ($shortcode_atts as $key => $value) {
            if ($value) {
                $shortcode .= ' ' . $key . '="' . esc_attr($value) . '"';
            }
        }
        $shortcode .= ']';
        
        echo do_shortcode($shortcode);
        echo '</div>';
        
        // Add JavaScript for cart integration
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calculator = document.getElementById('materialCalculator');
            const addToCartBtn = calculator ? calculator.querySelector('#addToCartButton') : null;
            
            if (addToCartBtn) {
                addToCartBtn.addEventListener('click', function() {
                    if (window.materialCalculator && window.materialCalculator.currentCalculation) {
                        const calculation = window.materialCalculator.currentCalculation;
                        construction_store_add_calculated_to_cart(calculation);
                    }
                });
            }
        });
        
        function construction_store_add_calculated_to_cart(calculation) {
            const productId = <?php echo $product->get_id(); ?>;
            const quantity = calculation.recommendedQuantity;
            
            // Show loading state
            const addToCartBtn = document.getElementById('addToCartButton');
            const originalText = addToCartBtn.textContent;
            addToCartBtn.textContent = '<?php _e('Adicionando...', 'construction-store-theme'); ?>';
            addToCartBtn.disabled = true;
            
            // Prepare form data
            const formData = new FormData();
            formData.append('action', 'add_calculated_to_cart');
            formData.append('product_id', productId);
            formData.append('quantity', quantity);
            formData.append('calculation_data', JSON.stringify(calculation));
            formData.append('nonce', '<?php echo wp_create_nonce('add_calculated_to_cart'); ?>');
            
            // Send AJAX request
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const message = document.createElement('div');
                    message.className = 'woocommerce-message';
                    message.innerHTML = data.data.message;
                    
                    const calculator = document.getElementById('materialCalculator');
                    calculator.parentNode.insertBefore(message, calculator);
                    
                    // Update cart count if available
                    if (data.data.cart_count) {
                        const cartCount = document.querySelector('.cart-count');
                        if (cartCount) {
                            cartCount.textContent = data.data.cart_count;
                        }
                    }
                    
                    // Reset calculator
                    window.materialCalculator.resetCalculator();
                    
                    // Scroll to message
                    message.scrollIntoView({ behavior: 'smooth' });
                    
                    // Remove message after 5 seconds
                    setTimeout(() => {
                        message.remove();
                    }, 5000);
                } else {
                    alert(data.data.message || '<?php _e('Erro ao adicionar produto ao carrinho', 'construction-store-theme'); ?>');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('<?php _e('Erro ao adicionar produto ao carrinho', 'construction-store-theme'); ?>');
            })
            .finally(() => {
                // Reset button
                addToCartBtn.textContent = originalText;
                addToCartBtn.disabled = false;
            });
        }
        </script>
        <?php
    }
}
add_action('woocommerce_single_product_summary', 'construction_store_add_material_calculator', 25);

/**
 * Determine calculator product type based on categories
 */
function construction_store_get_calculator_product_type($categories, $product) {
    $category_mapping = array(
        'tintas' => 'paint',
        'cimento' => 'cement',
        'argamassa' => 'mortar',
        'pisos' => 'flooring',
        'azulejos' => 'tiles'
    );
    
    foreach ($categories as $category) {
        if (isset($category_mapping[$category])) {
            return $category_mapping[$category];
        }
    }
    
    // Default based on product name or meta
    $product_name = strtolower($product->get_name());
    if (strpos($product_name, 'tinta') !== false) return 'paint';
    if (strpos($product_name, 'cimento') !== false) return 'cement';
    if (strpos($product_name, 'argamassa') !== false) return 'mortar';
    if (strpos($product_name, 'piso') !== false) return 'flooring';
    if (strpos($product_name, 'azulejo') !== false) return 'tiles';
    
    return 'paint'; // Default
}

/**
 * AJAX handler for adding calculated quantity to cart
 */
function construction_store_add_calculated_to_cart() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'add_calculated_to_cart')) {
        wp_send_json_error(array('message' => __('Erro de segurança', 'construction-store-theme')));
    }
    
    $product_id = intval($_POST['product_id']);
    $quantity = floatval($_POST['quantity']);
    $calculation_data = json_decode(stripslashes($_POST['calculation_data']), true);
    
    if (!$product_id || !$quantity) {
        wp_send_json_error(array('message' => __('Dados inválidos', 'construction-store-theme')));
    }
    
    // Get product
    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error(array('message' => __('Produto não encontrado', 'construction-store-theme')));
    }
    
    // Check stock
    if (!$product->is_in_stock()) {
        wp_send_json_error(array('message' => __('Produto fora de estoque', 'construction-store-theme')));
    }
    
    if ($product->managing_stock() && $product->get_stock_quantity() < $quantity) {
        wp_send_json_error(array('message' => sprintf(
            __('Apenas %d unidades disponíveis', 'construction-store-theme'),
            $product->get_stock_quantity()
        )));
    }
    
    // Add to cart
    $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity);
    
    if ($cart_item_key) {
        // Add calculation data to cart item
        WC()->cart->cart_contents[$cart_item_key]['calculation_data'] = $calculation_data;
        
        // Get cart count
        $cart_count = WC()->cart->get_cart_contents_count();
        
        // Success message
        $message = sprintf(
            __('%s foi adicionado ao seu carrinho. Quantidade calculada: %s %s', 'construction-store-theme'),
            $product->get_name(),
            $calculation_data['recommendedQuantity'],
            $calculation_data['unit']
        );
        
        wp_send_json_success(array(
            'message' => $message,
            'cart_count' => $cart_count,
            'cart_url' => wc_get_cart_url()
        ));
    } else {
        wp_send_json_error(array('message' => __('Erro ao adicionar produto ao carrinho', 'construction-store-theme')));
    }
}
add_action('wp_ajax_add_calculated_to_cart', 'construction_store_add_calculated_to_cart');
add_action('wp_ajax_nopriv_add_calculated_to_cart', 'construction_store_add_calculated_to_cart');

/**
 * Display calculation data in cart
 */
function construction_store_display_cart_calculation_data($item_data, $cart_item) {
    if (isset($cart_item['calculation_data'])) {
        $calculation = $cart_item['calculation_data'];
        
        $item_data[] = array(
            'key' => __('Cálculo', 'construction-store-theme'),
            'value' => sprintf(
                __('Área: %s m² | Rendimento: %s m²/%s | Margem: %s', 'construction-store-theme'),
                $calculation['totalArea'] ?? $calculation['area'],
                $calculation['coverage'],
                $calculation['unit'],
                $calculation['safetyMargin']
            )
        );
        
        if (isset($calculation['rooms']) && !empty($calculation['rooms'])) {
            $rooms_text = array();
            foreach ($calculation['rooms'] as $room) {
                $rooms_text[] = $room['name'] . ': ' . $room['area'] . 'm²';
            }
            
            $item_data[] = array(
                'key' => __('Ambientes', 'construction-store-theme'),
                'value' => implode(', ', $rooms_text)
            );
        }
    }
    
    return $item_data;
}
add_filter('woocommerce_get_item_data', 'construction_store_display_cart_calculation_data', 10, 2);

/**
 * Save calculation data to order items
 */
function construction_store_save_calculation_data_to_order($item, $cart_item_key, $values, $order) {
    if (isset($values['calculation_data'])) {
        $item->add_meta_data('_calculation_data', $values['calculation_data']);
    }
}
add_action('woocommerce_checkout_create_order_line_item', 'construction_store_save_calculation_data_to_order', 10, 4);

/**
 * Display calculation data in order items
 */
function construction_store_display_order_calculation_data($item_id, $item, $order, $plain_text) {
    $calculation_data = $item->get_meta('_calculation_data');
    
    if ($calculation_data) {
        echo '<div class="calculation-details">';
        echo '<strong>' . __('Detalhes do Cálculo:', 'construction-store-theme') . '</strong><br>';
        echo sprintf(
            __('Área: %s m² | Rendimento: %s m²/%s | Margem: %s', 'construction-store-theme'),
            $calculation_data['totalArea'] ?? $calculation_data['area'],
            $calculation_data['coverage'],
            $calculation_data['unit'],
            $calculation_data['safetyMargin']
        );
        
        if (isset($calculation_data['rooms']) && !empty($calculation_data['rooms'])) {
            echo '<br><strong>' . __('Ambientes:', 'construction-store-theme') . '</strong> ';
            $rooms_text = array();
            foreach ($calculation_data['rooms'] as $room) {
                $rooms_text[] = $room['name'] . ': ' . $room['area'] . 'm²';
            }
            echo implode(', ', $rooms_text);
        }
        echo '</div>';
    }
}
add_action('woocommerce_order_item_meta_end', 'construction_store_display_order_calculation_data', 10, 4);

/**
 * Add safety margin suggestions
 */
function construction_store_add_safety_margin_info() {
    global $product;
    
    $coverage_area = get_post_meta($product->get_id(), '_construction_coverage_area', true);
    
    if ($coverage_area) {
        echo '<div class="safety-margin-info">';
        echo '<h4>' . __('💡 Dicas Importantes', 'construction-store-theme') . '</h4>';
        echo '<ul>';
        echo '<li>' . __('Nossa calculadora já inclui uma margem de segurança recomendada', 'construction-store-theme') . '</li>';
        echo '<li>' . __('Para superfícies irregulares, considere aumentar a margem em 5-10%', 'construction-store-theme') . '</li>';
        echo '<li>' . __('Sempre consulte as instruções do fabricante para rendimento específico', 'construction-store-theme') . '</li>';
        echo '<li>' . __('Em caso de dúvidas, nossos especialistas estão disponíveis para ajudar', 'construction-store-theme') . '</li>';
        echo '</ul>';
        echo '</div>';
    }
}
add_action('woocommerce_single_product_summary', 'construction_store_add_safety_margin_info', 30);

/**
 * Customize WooCommerce messages
 */
function construction_store_woocommerce_messages() {
    // Customize add to cart message
    add_filter('wc_add_to_cart_message_html', function($message, $products) {
        return str_replace('foi adicionado', 'foi adicionado ao seu carrinho', $message);
    }, 10, 2);
}
add_action('init', 'construction_store_woocommerce_messages');
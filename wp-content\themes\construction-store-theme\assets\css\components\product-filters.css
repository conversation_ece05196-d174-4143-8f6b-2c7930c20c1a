/**
 * Product Filters Component Styles
 * Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

/* Archive Content Layout */
.archive-content {
    display: flex;
    gap: 32px;
    margin-top: 24px;
}

/* Product Filters Sidebar */
.product-filters-sidebar {
    flex: 0 0 280px;
    background: var(--wp--preset--color--white);
    border: 1px solid var(--wp--preset--color--gray-200);
    border-radius: 8px;
    padding: 24px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.product-filters-sidebar h3 {
    margin: 0 0 20px 0;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--wp--preset--color--secondary);
    border-bottom: 2px solid var(--wp--preset--color--primary);
    padding-bottom: 8px;
}

/* Filter Groups */
.filter-group {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--wp--preset--color--gray-200);
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.filter-group h4 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--wp--preset--color--gray-800);
}

/* Filter Options */
.filter-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--wp--preset--color--gray-700);
    cursor: pointer;
    padding: 4px 0;
    transition: color 0.2s ease;
}

.filter-options label:hover {
    color: var(--wp--preset--color--primary);
}

.filter-options input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--wp--preset--color--primary);
    cursor: pointer;
}

/* Brand Filter */
.brand-filter select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--wp--preset--color--gray-300);
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: var(--wp--preset--color--white);
    cursor: pointer;
}

.brand-filter select:focus {
    outline: none;
    border-color: var(--wp--preset--color--primary);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

/* Price Filter */
.price-filter {
    padding: 12px 0;
}

.price-range {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.price-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--wp--preset--color--gray-200);
    outline: none;
    cursor: pointer;
}

.price-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--wp--preset--color--primary);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.price-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--wp--preset--color--primary);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.price-display {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--wp--preset--color--gray-800);
    text-align: center;
    padding: 8px 12px;
    background: var(--wp--preset--color--gray-100);
    border-radius: 4px;
}

/* Specifications Filter */
.specs-filter {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.filter-section h5 {
    margin: 0 0 8px 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--wp--preset--color--gray-700);
}

/* Clear Filters Button */
.clear-filters-btn {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--wp--preset--color--gray-200);
}

.clear-filters-btn .wp-block-button__link {
    width: 100%;
    text-align: center;
    background-color: var(--wp--preset--color--gray-600);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.clear-filters-btn .wp-block-button__link:hover {
    background-color: var(--wp--preset--color--gray-800);
}

/* Products Main Area */
.products-main-area {
    flex: 1;
    min-width: 0; /* Prevent flex item from overflowing */
}

/* Products Toolbar */
.products-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--wp--preset--color--white);
    border: 1px solid var(--wp--preset--color--gray-200);
    border-radius: 8px;
}

/* View Toggle Buttons */
.view-options {
    display: flex;
    gap: 4px;
    border: 1px solid var(--wp--preset--color--gray-300);
    border-radius: 4px;
    overflow: hidden;
}

.view-toggle {
    background: var(--wp--preset--color--white);
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    color: var(--wp--preset--color--gray-600);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-toggle:hover {
    background: var(--wp--preset--color--gray-100);
    color: var(--wp--preset--color--gray-800);
}

.view-toggle.active {
    background: var(--wp--preset--color--primary);
    color: white;
}

.view-toggle .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Products Sorting */
.products-sorting select {
    padding: 8px 12px;
    border: 1px solid var(--wp--preset--color--gray-300);
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: var(--wp--preset--color--white);
    cursor: pointer;
}

.products-sorting select:focus {
    outline: none;
    border-color: var(--wp--preset--color--primary);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

/* Filter Active States */
.filter-options label.active {
    color: var(--wp--preset--color--primary);
    font-weight: 600;
}

.filter-options input[type="checkbox"]:checked + span {
    color: var(--wp--preset--color--primary);
    font-weight: 600;
}

/* Filter Count Badges */
.filter-count {
    display: inline-block;
    background: var(--wp--preset--color--gray-200);
    color: var(--wp--preset--color--gray-600);
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 4px;
    min-width: 18px;
    text-align: center;
}

.filter-count.active {
    background: var(--wp--preset--color--primary);
    color: white;
}

/* Mobile Filters Toggle */
.mobile-filters-toggle {
    display: none;
    background: var(--wp--preset--color--primary);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 20px;
    width: 100%;
}

.mobile-filters-toggle:hover {
    background: var(--wp--preset--color--primary-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .archive-content {
        gap: 24px;
    }
    
    .product-filters-sidebar {
        flex: 0 0 240px;
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .archive-content {
        flex-direction: column;
        gap: 0;
    }
    
    .mobile-filters-toggle {
        display: block;
    }
    
    .product-filters-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        background: var(--wp--preset--color--white);
        border: none;
        border-radius: 0;
        padding: 20px;
        overflow-y: auto;
        transition: left 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .product-filters-sidebar.active {
        left: 0;
    }
    
    .products-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .view-options {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .product-filters-sidebar {
        width: 100%;
        left: -100%;
    }
    
    .products-toolbar {
        padding: 12px 16px;
    }
    
    .filter-group {
        margin-bottom: 20px;
        padding-bottom: 16px;
    }
    
    .filter-options {
        gap: 6px;
    }
    
    .filter-options label {
        font-size: 0.8125rem;
    }
}

/* Filter Overlay for Mobile */
.filters-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.filters-overlay.active {
    display: block;
}

/* Close Button for Mobile Filters */
.mobile-filters-close {
    display: none;
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--wp--preset--color--gray-600);
    z-index: 1001;
}

@media (max-width: 768px) {
    .mobile-filters-close {
        display: block;
    }
}

/* Loading States */
.filter-group.loading {
    opacity: 0.6;
    pointer-events: none;
}

.filter-group.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 10px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    border: 2px solid var(--wp--preset--color--gray-300);
    border-top-color: var(--wp--preset--color--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
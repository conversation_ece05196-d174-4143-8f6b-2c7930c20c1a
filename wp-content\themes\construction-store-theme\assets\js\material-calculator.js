/**
 * Material Quantity Calculator
 * Calculates material quantities based on area and product coverage data
 */

class MaterialCalculator {
    constructor() {
        this.productTypes = {
            paint: {
                name: '<PERSON><PERSON>',
                unit: 'litros',
                defaultCoverage: 12, // m² per liter
                safetyMargin: 0.1,
                minQuantity: 1
            },
            cement: {
                name: '<PERSON><PERSON><PERSON>',
                unit: 'sacos',
                defaultCoverage: 4, // m² per 50kg bag
                safetyMargin: 0.15,
                minQuantity: 1
            },
            flooring: {
                name: '<PERSON>so',
                unit: 'm²',
                defaultCoverage: 1, // 1m² per m²
                safetyMargin: 0.1,
                minQuantity: 1
            },
            tiles: {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                unit: 'm²',
                defaultCoverage: 1,
                safetyMargin: 0.15,
                minQuantity: 1
            },
            mortar: {
                name: '<PERSON>rga<PERSON><PERSON>',
                unit: 'sacos',
                defaultCoverage: 5, // m² per 20kg bag
                safetyMargin: 0.1,
                minQuantity: 1
            }
        };
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.createCalculatorInterface();
    }

    /**
     * Calculate material quantity based on area and product type
     * @param {number} area - Area in square meters
     * @param {string} productType - Type of product (paint, cement, etc.)
     * @param {number} customCoverage - Custom coverage if different from default
     * @param {number} customSafetyMargin - Custom safety margin
     * @returns {Object} Calculation results
     */
    calculateQuantity(area, productType, customCoverage = null, customSafetyMargin = null) {
        if (!this.productTypes[productType]) {
            throw new Error(`Tipo de produto não suportado: ${productType}`);
        }

        const product = this.productTypes[productType];
        const coverage = customCoverage || product.defaultCoverage;
        const safetyMargin = customSafetyMargin !== null ? customSafetyMargin : product.safetyMargin;

        // Basic calculation
        const baseQuantity = area / coverage;
        const roundedBase = Math.ceil(baseQuantity);
        
        // Add safety margin
        const safetyQuantity = Math.ceil(baseQuantity * (1 + safetyMargin));
        const finalQuantity = Math.max(safetyQuantity, product.minQuantity);
        
        // Calculate waste
        const wasteQuantity = finalQuantity - roundedBase;
        const wastePercentage = ((wasteQuantity / roundedBase) * 100).toFixed(1);

        return {
            area: area,
            productType: productType,
            productName: product.name,
            unit: product.unit,
            coverage: coverage,
            baseQuantity: baseQuantity.toFixed(2),
            roundedBase: roundedBase,
            recommendedQuantity: finalQuantity,
            wasteQuantity: wasteQuantity,
            wastePercentage: wastePercentage,
            safetyMargin: (safetyMargin * 100).toFixed(0) + '%'
        };
    }

    /**
     * Calculate for multiple rooms/areas
     * @param {Array} areas - Array of area objects {name, area}
     * @param {string} productType - Type of product
     * @param {number} customCoverage - Custom coverage
     * @returns {Object} Combined calculation results
     */
    calculateMultipleAreas(areas, productType, customCoverage = null) {
        const totalArea = areas.reduce((sum, room) => sum + room.area, 0);
        const calculation = this.calculateQuantity(totalArea, productType, customCoverage);
        
        calculation.rooms = areas;
        calculation.totalArea = totalArea;
        
        return calculation;
    }

    /**
     * Validate input values
     * @param {number} area - Area to validate
     * @param {string} productType - Product type to validate
     * @returns {Array} Array of error messages
     */
    validateInput(area, productType) {
        const errors = [];

        if (!area || area <= 0) {
            errors.push('Área deve ser maior que zero');
        }

        if (area > 10000) {
            errors.push('Para áreas muito grandes (>10.000m²), consulte nossos especialistas');
        }

        if (!productType || !this.productTypes[productType]) {
            errors.push('Selecione um tipo de produto válido');
        }

        return errors;
    }

    /**
     * Create calculator user interface
     */
    createCalculatorInterface() {
        const calculatorHTML = `
            <div class="material-calculator" id="materialCalculator">
                <div class="calculator-header">
                    <h3>Calculadora de Materiais</h3>
                    <p>Calcule a quantidade necessária para seu projeto</p>
                </div>
                
                <div class="calculator-form">
                    <div class="form-group">
                        <label for="productType">Tipo de Material:</label>
                        <select id="productType" class="form-control">
                            <option value="">Selecione o material</option>
                            ${Object.keys(this.productTypes).map(type => 
                                `<option value="${type}">${this.productTypes[type].name}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="calculation-method">
                        <div class="method-tabs">
                            <button type="button" class="tab-button active" data-method="single">Área Única</button>
                            <button type="button" class="tab-button" data-method="multiple">Múltiplas Áreas</button>
                        </div>

                        <div class="method-content" id="singleAreaMethod">
                            <div class="form-group">
                                <label for="singleArea">Área Total (m²):</label>
                                <input type="number" id="singleArea" class="form-control" min="0" step="0.01" placeholder="Ex: 25.5">
                            </div>
                        </div>

                        <div class="method-content hidden" id="multipleAreasMethod">
                            <div class="areas-container" id="areasContainer">
                                <div class="area-input">
                                    <input type="text" placeholder="Nome do ambiente" class="area-name">
                                    <input type="number" placeholder="Área (m²)" class="area-value" min="0" step="0.01">
                                    <button type="button" class="remove-area">×</button>
                                </div>
                            </div>
                            <button type="button" id="addArea" class="btn-secondary">+ Adicionar Ambiente</button>
                        </div>
                    </div>

                    <div class="advanced-options">
                        <button type="button" class="toggle-advanced">Opções Avançadas</button>
                        <div class="advanced-content hidden">
                            <div class="form-group">
                                <label for="customCoverage">Rendimento Personalizado:</label>
                                <input type="number" id="customCoverage" class="form-control" min="0" step="0.1" placeholder="m² por unidade">
                            </div>
                            <div class="form-group">
                                <label for="customSafetyMargin">Margem de Segurança (%):</label>
                                <input type="number" id="customSafetyMargin" class="form-control" min="0" max="50" step="1" placeholder="10">
                            </div>
                        </div>
                    </div>

                    <button type="button" id="calculateButton" class="btn-primary">Calcular Quantidade</button>
                </div>

                <div class="calculator-results hidden" id="calculatorResults">
                    <div class="results-header">
                        <h4>Resultado do Cálculo</h4>
                    </div>
                    <div class="results-content" id="resultsContent">
                        <!-- Results will be populated here -->
                    </div>
                    <div class="results-actions">
                        <button type="button" id="addToCartButton" class="btn-primary">Adicionar ao Carrinho</button>
                        <button type="button" id="newCalculationButton" class="btn-secondary">Nova Calculação</button>
                    </div>
                </div>

                <div class="calculator-errors hidden" id="calculatorErrors">
                    <div class="error-content" id="errorContent">
                        <!-- Errors will be populated here -->
                    </div>
                </div>
            </div>
        `;

        // Store the HTML for later use
        this.calculatorHTML = calculatorHTML;
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        document.addEventListener('DOMContentLoaded', () => {
            this.attachEventListeners();
        });
    }

    /**
     * Attach event listeners to calculator elements
     */
    attachEventListeners() {
        const calculator = document.getElementById('materialCalculator');
        if (!calculator) return;

        // Tab switching
        calculator.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-button')) {
                this.switchCalculationMethod(e.target.dataset.method);
            }
        });

        // Add area button
        const addAreaBtn = calculator.querySelector('#addArea');
        if (addAreaBtn) {
            addAreaBtn.addEventListener('click', () => this.addAreaInput());
        }

        // Remove area buttons
        calculator.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-area')) {
                this.removeAreaInput(e.target.closest('.area-input'));
            }
        });

        // Advanced options toggle
        const advancedToggle = calculator.querySelector('.toggle-advanced');
        if (advancedToggle) {
            advancedToggle.addEventListener('click', () => this.toggleAdvancedOptions());
        }

        // Calculate button
        const calculateBtn = calculator.querySelector('#calculateButton');
        if (calculateBtn) {
            calculateBtn.addEventListener('click', () => this.performCalculation());
        }

        // Add to cart button
        const addToCartBtn = calculator.querySelector('#addToCartButton');
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', () => this.addToCart());
        }

        // New calculation button
        const newCalcBtn = calculator.querySelector('#newCalculationButton');
        if (newCalcBtn) {
            newCalcBtn.addEventListener('click', () => this.resetCalculator());
        }
    }

    /**
     * Switch between single and multiple area calculation methods
     */
    switchCalculationMethod(method) {
        const tabs = document.querySelectorAll('.tab-button');
        const contents = document.querySelectorAll('.method-content');

        tabs.forEach(tab => tab.classList.remove('active'));
        contents.forEach(content => content.classList.add('hidden'));

        document.querySelector(`[data-method="${method}"]`).classList.add('active');
        document.getElementById(`${method}AreaMethod`).classList.remove('hidden');
    }

    /**
     * Add new area input for multiple areas calculation
     */
    addAreaInput() {
        const container = document.getElementById('areasContainer');
        const areaInput = document.createElement('div');
        areaInput.className = 'area-input';
        areaInput.innerHTML = `
            <input type="text" placeholder="Nome do ambiente" class="area-name">
            <input type="number" placeholder="Área (m²)" class="area-value" min="0" step="0.01">
            <button type="button" class="remove-area">×</button>
        `;
        container.appendChild(areaInput);
    }

    /**
     * Remove area input
     */
    removeAreaInput(areaInput) {
        const container = document.getElementById('areasContainer');
        if (container.children.length > 1) {
            areaInput.remove();
        }
    }

    /**
     * Toggle advanced options visibility
     */
    toggleAdvancedOptions() {
        const content = document.querySelector('.advanced-content');
        const toggle = document.querySelector('.toggle-advanced');
        
        content.classList.toggle('hidden');
        toggle.textContent = content.classList.contains('hidden') ? 
            'Opções Avançadas' : 'Ocultar Opções Avançadas';
    }

    /**
     * Perform the calculation based on current form values
     */
    performCalculation() {
        this.hideErrors();
        this.hideResults();

        try {
            const formData = this.getFormData();
            const errors = this.validateFormData(formData);

            if (errors.length > 0) {
                this.showErrors(errors);
                return;
            }

            let calculation;
            if (formData.method === 'single') {
                calculation = this.calculateQuantity(
                    formData.area,
                    formData.productType,
                    formData.customCoverage,
                    formData.customSafetyMargin
                );
            } else {
                calculation = this.calculateMultipleAreas(
                    formData.areas,
                    formData.productType,
                    formData.customCoverage
                );
            }

            this.showResults(calculation);
            this.currentCalculation = calculation;

        } catch (error) {
            this.showErrors([error.message]);
        }
    }

    /**
     * Get form data from calculator interface
     */
    getFormData() {
        const productType = document.getElementById('productType').value;
        const activeMethod = document.querySelector('.tab-button.active').dataset.method;
        
        const customCoverage = document.getElementById('customCoverage').value;
        const customSafetyMargin = document.getElementById('customSafetyMargin').value;

        const formData = {
            productType: productType,
            method: activeMethod,
            customCoverage: customCoverage ? parseFloat(customCoverage) : null,
            customSafetyMargin: customSafetyMargin ? parseFloat(customSafetyMargin) / 100 : null
        };

        if (activeMethod === 'single') {
            formData.area = parseFloat(document.getElementById('singleArea').value) || 0;
        } else {
            const areaInputs = document.querySelectorAll('.area-input');
            formData.areas = Array.from(areaInputs).map(input => ({
                name: input.querySelector('.area-name').value || 'Ambiente',
                area: parseFloat(input.querySelector('.area-value').value) || 0
            })).filter(area => area.area > 0);
        }

        return formData;
    }

    /**
     * Validate form data
     */
    validateFormData(formData) {
        const errors = [];

        if (!formData.productType) {
            errors.push('Selecione o tipo de material');
        }

        if (formData.method === 'single') {
            if (!formData.area || formData.area <= 0) {
                errors.push('Informe uma área válida');
            }
        } else {
            if (!formData.areas || formData.areas.length === 0) {
                errors.push('Informe pelo menos uma área válida');
            }
        }

        return errors;
    }

    /**
     * Display calculation results
     */
    showResults(calculation) {
        const resultsContainer = document.getElementById('calculatorResults');
        const resultsContent = document.getElementById('resultsContent');

        let resultsHTML = `
            <div class="result-summary">
                <div class="result-item">
                    <span class="label">Material:</span>
                    <span class="value">${calculation.productName}</span>
                </div>
                <div class="result-item">
                    <span class="label">Área Total:</span>
                    <span class="value">${calculation.totalArea || calculation.area} m²</span>
                </div>
                <div class="result-item">
                    <span class="label">Rendimento:</span>
                    <span class="value">${calculation.coverage} m²/${calculation.unit}</span>
                </div>
            </div>

            <div class="result-calculation">
                <div class="calc-step">
                    <span class="step-label">Quantidade Base:</span>
                    <span class="step-value">${calculation.roundedBase} ${calculation.unit}</span>
                </div>
                <div class="calc-step recommended">
                    <span class="step-label">Quantidade Recomendada:</span>
                    <span class="step-value">${calculation.recommendedQuantity} ${calculation.unit}</span>
                </div>
                <div class="calc-step">
                    <span class="step-label">Margem de Segurança:</span>
                    <span class="step-value">${calculation.safetyMargin} (+${calculation.wasteQuantity} ${calculation.unit})</span>
                </div>
            </div>
        `;

        if (calculation.rooms) {
            resultsHTML += `
                <div class="result-breakdown">
                    <h5>Detalhamento por Ambiente:</h5>
                    <ul class="rooms-list">
                        ${calculation.rooms.map(room => 
                            `<li>${room.name}: ${room.area} m²</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
        }

        resultsContent.innerHTML = resultsHTML;
        resultsContainer.classList.remove('hidden');
    }

    /**
     * Display errors
     */
    showErrors(errors) {
        const errorsContainer = document.getElementById('calculatorErrors');
        const errorContent = document.getElementById('errorContent');

        errorContent.innerHTML = `
            <ul class="error-list">
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        `;

        errorsContainer.classList.remove('hidden');
    }

    /**
     * Hide errors
     */
    hideErrors() {
        document.getElementById('calculatorErrors').classList.add('hidden');
    }

    /**
     * Hide results
     */
    hideResults() {
        document.getElementById('calculatorResults').classList.add('hidden');
    }

    /**
     * Reset calculator to initial state
     */
    resetCalculator() {
        // Reset form
        document.getElementById('productType').value = '';
        document.getElementById('singleArea').value = '';
        document.getElementById('customCoverage').value = '';
        document.getElementById('customSafetyMargin').value = '';

        // Reset multiple areas
        const container = document.getElementById('areasContainer');
        container.innerHTML = `
            <div class="area-input">
                <input type="text" placeholder="Nome do ambiente" class="area-name">
                <input type="number" placeholder="Área (m²)" class="area-value" min="0" step="0.01">
                <button type="button" class="remove-area">×</button>
            </div>
        `;

        // Hide results and errors
        this.hideResults();
        this.hideErrors();

        // Reset to single area method
        this.switchCalculationMethod('single');

        // Hide advanced options
        document.querySelector('.advanced-content').classList.add('hidden');
        document.querySelector('.toggle-advanced').textContent = 'Opções Avançadas';

        this.currentCalculation = null;
    }

    /**
     * Add calculated quantity to cart (WooCommerce integration)
     */
    addToCart() {
        if (!this.currentCalculation) {
            alert('Nenhum cálculo disponível para adicionar ao carrinho');
            return;
        }

        // Check if we're on a product page with WooCommerce integration
        if (typeof construction_store_add_calculated_to_cart === 'function') {
            // Use the WooCommerce integration function
            construction_store_add_calculated_to_cart(this.currentCalculation);
        } else {
            // Fallback for standalone calculator
            this.showCartFallback();
        }
    }

    /**
     * Show fallback cart information when not on product page
     */
    showCartFallback() {
        const calculation = this.currentCalculation;
        
        const message = `
            <div class="calculator-cart-info">
                <h4>Resultado do Cálculo:</h4>
                <p><strong>Material:</strong> ${calculation.productName}</p>
                <p><strong>Quantidade Recomendada:</strong> ${calculation.recommendedQuantity} ${calculation.unit}</p>
                <p><strong>Área Total:</strong> ${calculation.totalArea || calculation.area} m²</p>
                <p><strong>Margem de Segurança:</strong> ${calculation.safetyMargin}</p>
                ${calculation.rooms ? `
                    <p><strong>Ambientes:</strong></p>
                    <ul>
                        ${calculation.rooms.map(room => `<li>${room.name}: ${room.area} m²</li>`).join('')}
                    </ul>
                ` : ''}
                <p class="calculator-note">
                    <em>Anote essas informações e procure por produtos compatíveis em nossa loja.</em>
                </p>
            </div>
        `;
        
        // Create modal or alert with the information
        const modal = document.createElement('div');
        modal.className = 'calculator-modal';
        modal.innerHTML = `
            <div class="calculator-modal-content">
                <span class="calculator-modal-close">&times;</span>
                ${message}
                <div class="calculator-modal-actions">
                    <button type="button" class="btn-primary" onclick="window.print()">Imprimir</button>
                    <button type="button" class="btn-secondary calculator-modal-close">Fechar</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add modal styles if not already present
        if (!document.getElementById('calculator-modal-styles')) {
            const styles = document.createElement('style');
            styles.id = 'calculator-modal-styles';
            styles.textContent = `
                .calculator-modal {
                    display: block;
                    position: fixed;
                    z-index: 10000;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                }
                .calculator-modal-content {
                    background-color: white;
                    margin: 5% auto;
                    padding: 20px;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 600px;
                    position: relative;
                }
                .calculator-modal-close {
                    position: absolute;
                    right: 15px;
                    top: 15px;
                    font-size: 28px;
                    font-weight: bold;
                    cursor: pointer;
                    background: none;
                    border: none;
                }
                .calculator-modal-close:hover {
                    color: var(--color-danger);
                }
                .calculator-modal-actions {
                    margin-top: 20px;
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }
                .calculator-cart-info ul {
                    margin: 10px 0;
                    padding-left: 20px;
                }
                .calculator-note {
                    background: var(--color-gray-100);
                    padding: 10px;
                    border-radius: 4px;
                    margin-top: 15px;
                }
            `;
            document.head.appendChild(styles);
        }
        
        // Close modal functionality
        const closeButtons = modal.querySelectorAll('.calculator-modal-close');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                modal.remove();
            });
        });
        
        // Close on outside click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * Get calculator HTML for embedding in pages
     */
    getCalculatorHTML() {
        return this.calculatorHTML;
    }

    /**
     * Initialize calculator on a specific element
     */
    initializeOn(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.innerHTML = this.calculatorHTML;
            this.attachEventListeners();
        }
    }
}

// Initialize calculator when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.materialCalculator = new MaterialCalculator();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MaterialCalculator;
}
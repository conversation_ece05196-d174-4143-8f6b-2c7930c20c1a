{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 3, "title": "Construction Store Theme", "settings": {"appearanceTools": true, "useRootPaddingAwareAlignments": true, "color": {"custom": true, "customDuotone": true, "customGradient": true, "defaultDuotones": false, "defaultGradients": false, "defaultPalette": false, "duotone": [], "gradients": [{"slug": "primary-gradient", "gradient": "linear-gradient(135deg, var(--wp--preset--color--primary) 0%, var(--wp--preset--color--primary-dark) 100%)", "name": "Gradiente Primário"}, {"slug": "secondary-gradient", "gradient": "linear-gradient(135deg, var(--wp--preset--color--secondary) 0%, var(--wp--preset--color--secondary-light) 100%)", "name": "Gradiente Secundário"}], "palette": [{"slug": "primary", "color": "#FF6B35", "name": "<PERSON><PERSON>"}, {"slug": "primary-dark", "color": "#E55A2B", "name": "<PERSON><PERSON>"}, {"slug": "primary-light", "color": "#FF8A5C", "name": "<PERSON><PERSON>"}, {"slug": "secondary", "color": "#2C3E50", "name": "Azul Escuro"}, {"slug": "secondary-light", "color": "#34495E", "name": "A<PERSON>l <PERSON>"}, {"slug": "gray-100", "color": "#F8F9FA", "name": "Cinza Muito <PERSON>"}, {"slug": "gray-200", "color": "#E9ECEF", "name": "Cinza Claro"}, {"slug": "gray-300", "color": "#DEE2E6", "name": "Cinza Médio <PERSON>"}, {"slug": "gray-600", "color": "#6C757D", "name": "Cinza Médio"}, {"slug": "gray-800", "color": "#343A40", "name": "Cinza Escuro"}, {"slug": "gray-900", "color": "#212529", "name": "Cinza Muito <PERSON>"}, {"slug": "success", "color": "#28A745", "name": "Verde Sucesso"}, {"slug": "warning", "color": "#FFC107", "name": "<PERSON><PERSON>"}, {"slug": "danger", "color": "#DC3545", "name": "Vermelho <PERSON>"}, {"slug": "info", "color": "#17A2B8", "name": "Azul Informação"}, {"slug": "white", "color": "#FFFFFF", "name": "Branco"}, {"slug": "black", "color": "#000000", "name": "Preto"}]}, "typography": {"customFontSize": true, "fontStyle": true, "fontWeight": true, "letterSpacing": true, "lineHeight": true, "textDecoration": true, "textTransform": true, "dropCap": false, "fontSizes": [{"slug": "x-small", "size": "0.75rem", "name": "Extra Pequeno"}, {"slug": "small", "size": "0.875rem", "name": "Pequeno"}, {"slug": "medium", "size": "1rem", "name": "Médio"}, {"slug": "large", "size": "1.125rem", "name": "Grande"}, {"slug": "x-large", "size": "1.25rem", "name": "Extra Grande"}, {"slug": "xx-large", "size": "1.5rem", "name": "<PERSON><PERSON>"}, {"slug": "xxx-large", "size": "1.875rem", "name": "Gigante"}, {"slug": "huge", "size": "2.25rem", "name": "<PERSON><PERSON><PERSON>"}], "fontFamilies": [{"slug": "primary", "fontFamily": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif", "name": "<PERSON><PERSON> Principal"}, {"slug": "secondary", "fontFamily": "'Roboto Slab', Georgia, 'Times New Roman', Times, serif", "name": "Fonte Secundária"}, {"slug": "monospace", "fontFamily": "'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace", "name": "Monoespaçada"}]}, "spacing": {"customSpacingSize": true, "spacingScale": {"operator": "*", "increment": 1.5, "steps": 7, "mediumStep": 1.5, "unit": "rem"}, "spacingSizes": [{"slug": "10", "size": "0.25rem", "name": "1"}, {"slug": "20", "size": "0.5rem", "name": "2"}, {"slug": "30", "size": "0.75rem", "name": "3"}, {"slug": "40", "size": "1rem", "name": "4"}, {"slug": "50", "size": "1.25rem", "name": "5"}, {"slug": "60", "size": "1.5rem", "name": "6"}, {"slug": "70", "size": "2rem", "name": "8"}, {"slug": "80", "size": "2.5rem", "name": "10"}, {"slug": "90", "size": "3rem", "name": "12"}, {"slug": "100", "size": "4rem", "name": "16"}, {"slug": "110", "size": "5rem", "name": "20"}], "units": ["%", "px", "em", "rem", "vh", "vw"]}, "layout": {"contentSize": "1200px", "wideSize": "1400px"}, "border": {"color": true, "radius": true, "style": true, "width": true}, "shadow": {"defaultPresets": false, "presets": [{"slug": "small", "shadow": "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)", "name": "Sombra Pequena"}, {"slug": "medium", "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "name": "Sombra Média"}, {"slug": "large", "shadow": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "name": "Sombra Grande"}]}}, "styles": {"color": {"background": "var(--wp--preset--color--white)", "text": "var(--wp--preset--color--gray-800)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--medium)", "lineHeight": "1.5"}, "spacing": {"blockGap": "var(--wp--preset--spacing--40)"}, "elements": {"button": {"color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--white)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--medium)", "fontWeight": "600"}, "spacing": {"padding": {"top": "var(--wp--preset--spacing--30)", "right": "var(--wp--preset--spacing--50)", "bottom": "var(--wp--preset--spacing--30)", "left": "var(--wp--preset--spacing--50)"}}, "border": {"radius": "0.375rem"}, ":hover": {"color": {"background": "var(--wp--preset--color--primary-dark)"}}}, "link": {"color": {"text": "var(--wp--preset--color--primary)"}, ":hover": {"color": {"text": "var(--wp--preset--color--primary-dark)"}}}, "h1": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--huge)", "fontWeight": "700", "lineHeight": "1.25"}, "color": {"text": "var(--wp--preset--color--gray-900)"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--50)"}}}, "h2": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--xxx-large)", "fontWeight": "600", "lineHeight": "1.3"}, "color": {"text": "var(--wp--preset--color--gray-900)"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--40)"}}}, "h3": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--xx-large)", "fontWeight": "600", "lineHeight": "1.4"}, "color": {"text": "var(--wp--preset--color--gray-900)"}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--40)"}}}, "h4": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--x-large)", "fontWeight": "600", "lineHeight": "1.4"}, "color": {"text": "var(--wp--preset--color--gray-900)"}}, "h5": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--large)", "fontWeight": "600", "lineHeight": "1.4"}, "color": {"text": "var(--wp--preset--color--gray-900)"}}, "h6": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--medium)", "fontWeight": "600", "lineHeight": "1.4"}, "color": {"text": "var(--wp--preset--color--gray-900)"}}}, "blocks": {"core/site-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--xx-large)", "fontWeight": "700"}, "color": {"text": "var(--wp--preset--color--secondary)"}}, "core/navigation": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--medium)", "fontWeight": "500"}}, "core/post-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--primary)", "fontSize": "var(--wp--preset--font-size--xxx-large)", "fontWeight": "600"}}, "core/paragraph": {"typography": {"lineHeight": "1.6"}}}}, "templateParts": [{"name": "header", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "area": "header"}, {"name": "footer", "title": "Rodapé", "area": "footer"}, {"name": "navigation", "title": "Navegação", "area": "uncategorized"}], "customTemplates": [{"name": "front-page", "title": "Página Inicial", "postTypes": ["page"]}, {"name": "single-product", "title": "Produto Individual", "postTypes": ["product"]}, {"name": "archive-product", "title": "Arquivo de Produtos", "postTypes": ["product"]}, {"name": "page-checkout", "title": "Página de Checkout", "postTypes": ["page"]}]}
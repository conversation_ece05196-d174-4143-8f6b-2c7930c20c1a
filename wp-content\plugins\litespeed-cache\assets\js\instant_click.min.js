let _chromiumMajorVersionInUserAgent=null,_speculationRulesType,_allowQueryString,_allowExternalLinks,_useWhitelist,_delayOnHover=65,_lastTouchstartEvent,_mouseoverTimer,_preloadedList=new Set;function init(){let e=document.createElement("link").relList,t=e.supports("prefetch")&&e.supports("modulepreload");if(!t)return;let n="instantVaryAccept"in document.body.dataset||"Shopify"in window,r=navigator.userAgent.indexOf("Chrome/");if(r>-1&&(_chromiumMajorVersionInUserAgent=parseInt(navigator.userAgent.substring(r+7))),n&&_chromiumMajorVersionInUserAgent&&_chromiumMajorVersionInUserAgent<110)return;if(_speculationRulesType="none",HTMLScriptElement.supports&&HTMLScriptElement.supports("speculationrules")){let s=document.body.dataset.instantSpecrules;"prerender"==s?_speculationRulesType="prerender":"no"!=s&&(_speculationRulesType="prefetch")}let i="instantMousedownShortcut"in document.body.dataset;_allowQueryString="instantAllowQueryString"in document.body.dataset,_allowExternalLinks="instantAllowExternalLinks"in document.body.dataset,_useWhitelist="instantWhitelist"in document.body.dataset;let o=!1,a=!1,l=!1;if("instantIntensity"in document.body.dataset){let u=document.body.dataset.instantIntensity;if("mousedown"!=u||i||(o=!0),"mousedown-only"!=u||i||(o=!0,a=!0),"viewport"==u){let c=document.documentElement.clientWidth*document.documentElement.clientHeight<45e4,d=navigator.connection&&navigator.connection.saveData,p=navigator.connection&&navigator.connection.effectiveType&&navigator.connection.effectiveType.includes("2g");!c||d||p||(l=!0)}"viewport-all"==u&&(l=!0);let h=parseInt(u);isNaN(h)||(_delayOnHover=h)}let m={capture:!0,passive:!0};if(a?document.addEventListener("touchstart",touchstartEmptyListener,m):document.addEventListener("touchstart",touchstartListener,m),o||document.addEventListener("mouseover",mouseoverListener,m),o&&document.addEventListener("mousedown",mousedownListener,m),i&&document.addEventListener("mousedown",mousedownShortcutListener,m),l){let f=window.requestIdleCallback;f||(f=e=>{e()}),f(function e(){let t=new IntersectionObserver(e=>{e.forEach(e=>{if(e.isIntersecting){let n=e.target;t.unobserve(n),preload(n.href)}})});document.querySelectorAll("a").forEach(e=>{isPreloadable(e)&&t.observe(e)})},{timeout:1500})}}function touchstartListener(e){_lastTouchstartEvent=e;let t=e.target.closest("a");isPreloadable(t)&&preload(t.href,"high")}function touchstartEmptyListener(e){_lastTouchstartEvent=e}function mouseoverListener(e){if(isEventLikelyTriggeredByTouch(e)||!("closest"in e.target))return;let t=e.target.closest("a");isPreloadable(t)&&(t.addEventListener("mouseout",mouseoutListener,{passive:!0}),_mouseoverTimer=setTimeout(()=>{preload(t.href,"high"),_mouseoverTimer=null},_delayOnHover))}function mousedownListener(e){if(isEventLikelyTriggeredByTouch(e))return;let t=e.target.closest("a");isPreloadable(t)&&preload(t.href,"high")}function mouseoutListener(e){(!e.relatedTarget||e.target.closest("a")!=e.relatedTarget.closest("a"))&&_mouseoverTimer&&(clearTimeout(_mouseoverTimer),_mouseoverTimer=null)}function mousedownShortcutListener(e){if(isEventLikelyTriggeredByTouch(e))return;let t=e.target.closest("a");if(e.which>1||e.metaKey||e.ctrlKey||!t)return;t.addEventListener("click",function(e){1337!=e.detail&&e.preventDefault()},{capture:!0,passive:!1,once:!0});let n=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1,detail:1337});t.dispatchEvent(n)}function isEventLikelyTriggeredByTouch(e){if(!_lastTouchstartEvent||!e||e.target!=_lastTouchstartEvent.target)return!1;let t=e.timeStamp,n=t-_lastTouchstartEvent.timeStamp;return n<2500}function isPreloadable(e){if(e&&e.href&&(!_useWhitelist||"instant"in e.dataset)&&(e.origin==location.origin||(_allowExternalLinks||"instant"in e.dataset)&&_chromiumMajorVersionInUserAgent)){if(["http:","https:"].includes(e.protocol)&&("http:"!=e.protocol||"https:"!=location.protocol)&&(_allowQueryString||!e.search||"instant"in e.dataset)&&(!e.hash||e.pathname+e.search!=location.pathname+location.search)&&!("noInstant"in e.dataset))return!0}}function preload(e,t="auto"){!_preloadedList.has(e)&&("none"!=_speculationRulesType?preloadUsingSpeculationRules(e):preloadUsingLinkElement(e,t),_preloadedList.add(e))}function preloadUsingSpeculationRules(e){let t=document.createElement("script");t.type="speculationrules",t.textContent=JSON.stringify({[_speculationRulesType]:[{source:"list",urls:[e]}]}),document.head.appendChild(t)}function preloadUsingLinkElement(e,t="auto"){let n=document.createElement("link");n.rel="prefetch",n.href=e,n.fetchPriority=t,n.as="document",document.head.appendChild(n)}init();

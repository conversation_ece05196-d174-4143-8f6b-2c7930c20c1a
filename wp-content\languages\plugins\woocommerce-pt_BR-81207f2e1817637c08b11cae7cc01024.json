{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Only show children of current category": ["Mostrar apenas filhos da categoria atual"], "This will affect product category pages": ["<PERSON><PERSON> as páginas de categorias de produtos"], "Category images are hidden.": ["Imagens de categoria são ocultas."], "Category images are visible.": ["Imagens de categoria são visíveis."], "Show category images": ["Mostrar imagens da categoria"], "Show empty categories": ["Mostrar categorias vazias"], "Display style": ["Estilo de exibição"], "List Settings": ["Lista de configurações"], "Show product count": ["Mostrar contagem de produtos"], "Product Categories List": ["Lista de categorias de produtos"], "This block displays the product categories for your store. To use it you first need to create a product and assign it to a category.": ["Este bloco exibe as categorias de produtos para sua loja. Para usá-lo, primeiro você precisa criar um produto e atribuí-lo a uma categoria."], "List": ["Lista"], "Show hierarchy": ["<PERSON><PERSON><PERSON> hierar<PERSON>"], "Dropdown": ["Suspenso"], "Content": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/product-categories.js"}}
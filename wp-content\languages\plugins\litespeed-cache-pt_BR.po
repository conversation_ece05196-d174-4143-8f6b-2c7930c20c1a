# Translation of Plugins - LiteSpeed Cache - Stable (latest release) in Portuguese (Brazil)
# This file is distributed under the same license as the Plugins - LiteSpeed Cache - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-19 18:02:51+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Plugins - LiteSpeed Cache - Stable (latest release)\n"

#: tpl/toolbox/purge.tpl.php:287
msgid "e.g. Use %1$s or %2$s."
msgstr "ex.: Use %1$s ou %2$s."

#: tpl/toolbox/log_viewer.tpl.php:100 tpl/toolbox/report.tpl.php:54
msgid "Click to copy"
msgstr "Clique para copiar"

#: tpl/inc/admin_footer.php:11
msgid "Rate %1$s on %2$s"
msgstr "Classifique o %1$s no %2$s"

#: tpl/cdn/cf.tpl.php:73
msgid "Clear %s cache when \"Purge All\" is run."
msgstr "Limpar o cache do %s quando “Limpar tudo” for executado."

#: tpl/cache/settings_inc.login_cookie.tpl.php:102
msgid "SYNTAX: alphanumeric and \"_\". No spaces and case sensitive."
msgstr "SINTAXE: alfanumérico e “_”. Sem espaços e diferencia maiúsculas de minúsculas."

#: tpl/cache/settings_inc.login_cookie.tpl.php:26
msgid "SYNTAX: alphanumeric and \"_\". No spaces and case sensitive. MUST BE UNIQUE FROM OTHER WEB APPLICATIONS."
msgstr "SINTAXE: alfanumérico e “_”. Sem espaços e diferencia maiúsculas de minúsculas. DEVE SER ÚNICO EM OUTROS APLICATIVOS DA WEB."

#: tpl/banner/score.php:120
msgid "Submit a ticket"
msgstr "Enviar um ticket"

#: src/lang.cls.php:246
msgid "Clear Cloudflare cache"
msgstr "Limpar cache do Cloudflare"

#: src/cloud.cls.php:167 src/cloud.cls.php:250
msgid "QUIC.cloud's access to your WP REST API seems to be blocked."
msgstr "Parece que o acesso do QUIC.cloud à API REST do WP está bloqueado."

#: tpl/toolbox/log_viewer.tpl.php:96
msgid "Copy Log"
msgstr "Copiar registro"

#: tpl/page_optm/settings_tuning_css.tpl.php:140
msgid "Selectors must exist in the CSS. Parent classes in the HTML will not work."
msgstr "Os seletores precisam existir no CSS. As classes principais no HTML não irão funcionar."

#: tpl/page_optm/settings_tuning_css.tpl.php:133
msgid "List the CSS selectors whose styles should always be included in CCSS."
msgstr "Liste os seletores CSS, cujos estilos devem sempre ser incluídos no CCSS."

#: tpl/page_optm/settings_tuning_css.tpl.php:57
msgid "List the CSS selectors whose styles should always be included in UCSS."
msgstr "Liste os seletores CSS, cujos estilos devem sempre ser incluídos no UCSS."

#: tpl/img_optm/summary.tpl.php:69 tpl/page_optm/settings_css.tpl.php:156
#: tpl/page_optm/settings_css.tpl.php:300 tpl/page_optm/settings_vpi.tpl.php:99
msgid "Available after %d second(s)"
msgstr "Disponível após %d segundos"

#: tpl/dash/dashboard.tpl.php:346
msgid "Enable QUIC.cloud Services"
msgstr "Ativar os serviços do QUIC.cloud"

#: tpl/dash/dashboard.tpl.php:183
msgid "The features below are provided by"
msgstr "Os recursos abaixo, são fornecidos por"

#: tpl/dash/dashboard.tpl.php:154
msgid "Do not show this again"
msgstr "Não mostrar isso novamente"

#: tpl/dash/dashboard.tpl.php:147
msgid "Free monthly quota available. Can also be used anonymously (no email required)."
msgstr "Cota mensal gratuita disponível. Também pode ser usado anonimamente (não é necessário e-mail)."

#: tpl/cdn/cf.tpl.php:11
msgid "Cloudflare Settings"
msgstr "Configurações do Cloudflare"

#: src/tool.cls.php:36 src/tool.cls.php:47
msgid "Failed to detect IP"
msgstr "Falha ao detectar o IP"

#: src/lang.cls.php:171
msgid "CCSS Selector Allowlist"
msgstr "Lista de permissões do seletor de CCSS"

#: tpl/toolbox/settings-debug.tpl.php:51
msgid "Outputs to a series of files in the %s directory."
msgstr "Gera uma série de arquivos no diretório %s."

#: tpl/toolbox/report.tpl.php:79
msgid "Attach PHP info to report. Check this box to insert relevant data from %s."
msgstr "Anexar informações do PHP ao relatório. Marque esta caixa para inserir dados relevantes de %s."

#: tpl/toolbox/report.tpl.php:55
msgid "Last Report Date"
msgstr "Data do último relatório"

#: tpl/toolbox/report.tpl.php:54
msgid "Last Report Number"
msgstr "Número do último relatório"

#: tpl/toolbox/report.tpl.php:32
msgid "Regenerate and Send a New Report"
msgstr "Gerar novamente e enviar um novo relatório"

#: tpl/img_optm/summary.tpl.php:356
msgid "This will reset the %1$s. If you changed WebP/AVIF settings and want to generate %2$s for the previously optimized images, use this action."
msgstr "Esta ação irá redefinir a %1$s. Se você alterou as configurações de WebP/AVIF e quer gerar %2$s para as imagens otimizadas anteriormente, use esta ação."

#: tpl/img_optm/settings.media_webp.tpl.php:26 tpl/img_optm/summary.tpl.php:352
msgid "Soft Reset Optimization Counter"
msgstr "Contador de otimização de redefinição suave"

#: tpl/img_optm/settings.media_webp.tpl.php:26
msgid "When switching formats, please %1$s or %2$s to apply this new choice to previously optimized images."
msgstr "Ao alternar os formatos, %1$s ou %2$s para aplicar esta nova opção às imagens otimizadas anteriormente."

#: tpl/img_optm/settings.media_webp.tpl.php:23
msgid "%1$s is a %2$s paid feature."
msgstr "%1$s é um recurso pago %2$s."

#: tpl/general/online.tpl.php:148
msgid "Remove QUIC.cloud integration from this site. Note: QUIC.cloud data will be preserved so you can re-enable services at any time. If you want to fully remove your site from QUIC.cloud, delete the domain through the QUIC.cloud Dashboard first."
msgstr "Remova a integração do QUIC.cloud deste site. Observação: os dados do QUIC.cloud serão preservados, para que você possa reativar os serviços a qualquer momento. Se você quiser remover totalmente seu site do QUIC.cloud, exclua primeiro o domínio através do painel do QUIC.cloud."

#: tpl/general/online.tpl.php:147
msgid "Disconnect from QUIC.cloud"
msgstr "Desconectar do QUIC.cloud"

#: tpl/general/online.tpl.php:147
msgid "Are you sure you want to disconnect from QUIC.cloud? This will not remove any data from the QUIC.cloud dashboard."
msgstr "Tem certeza de que deseja se desconectar do QUIC.cloud? Isso não irá remover nenhum dado do painel do QUIC.cloud."

#: tpl/general/online.tpl.php:137
msgid "not available for anonymous users"
msgstr "não disponível para usuários anônimos"

#: tpl/general/online.tpl.php:131
msgid "Your site is connected and using QUIC.cloud Online Services as an <strong>anonymous user</strong>. The CDN function and certain features of optimization services are not available for anonymous users. Link to QUIC.cloud to use the CDN and all available Online Services features."
msgstr "Seu site está conectado e usando os serviços on-line do QUIC.cloud como um <strong>usuário anônimo</strong>. A função de CDN e determinados recursos dos serviços de otimização, não estão disponíveis para usuários anônimos. Vincule-se ao QUIC.cloud, para usar o CDN e todos os recursos disponíveis dos serviços on-line."

#: tpl/general/online.tpl.php:130
msgid "QUIC.cloud Integration Enabled with limitations"
msgstr "Integração com o QUIC.cloud ativada com limitações"

#: tpl/general/online.tpl.php:113
msgid "Your site is connected and ready to use QUIC.cloud Online Services."
msgstr "Seu site está conectado e pronto para usar os serviços on-line do QUIC.cloud."

#: tpl/general/online.tpl.php:112
msgid "QUIC.cloud Integration Enabled"
msgstr "Integração com o QUIC.cloud ativada"

#: tpl/general/online.tpl.php:100
msgid "In order to use most QUIC.cloud services, you need quota. QUIC.cloud gives you free quota every month, but if you need more, you can purchase it."
msgstr "Para usar a maioria dos serviços do QUIC.cloud, você precisa de uma cota. O QUIC.cloud oferece cota gratuita todos os meses, mas se você precisar de mais, pode comprá-la."

#: tpl/general/online.tpl.php:91
msgid "Offers optional <strong>built-in DNS service</strong> to simplify CDN onboarding."
msgstr "Oferece um <strong>serviço de DNS integrado</strong> opcional para simplificar a integração com o CDN."

#: tpl/general/online.tpl.php:90
msgid "Provides <strong>security at the CDN level</strong>, protecting your server from attack."
msgstr "Oferece <strong>segurança a nível do CDN</strong>, protegendo seu servidor contra ataques."

#: tpl/general/online.tpl.php:89
msgid "Delivers global coverage with a growing <strong>network of 80+ PoPs</strong>."
msgstr "Oferece cobertura global com uma <strong>rede crescente de mais de 80 PoPs</strong>."

#: tpl/general/online.tpl.php:88
msgid "Caches your entire site, including dynamic content and <strong>ESI blocks</strong>."
msgstr "Armazena em cache todo o seu site, inclusive o conteúdo dinâmico e os blocos <strong>ESI</strong>."

#: tpl/general/online.tpl.php:84
msgid "Content Delivery Network"
msgstr "Rede de distribuição de conteúdo (CDN)"

#: tpl/general/online.tpl.php:75
msgid "<strong>Viewport Images (VPI)</strong> provides a well-polished fully-loaded view above the fold."
msgstr "As <strong>imagens da janela de visualização (VPI)</strong> fornecem uma visualização completamente carregada e bem polida acima da dobra."

#: tpl/general/online.tpl.php:74
msgid "<strong>Low Quality Image Placeholder (LQIP)</strong> gives your imagery a more pleasing look as it lazy loads."
msgstr "O <strong>Espaço reservado para imagens de baixa qualidade (LQIP)</strong> dá às suas imagens uma aparência mais agradável à medida que elas são carregadas tardiamente."

#: tpl/general/online.tpl.php:73
msgid "<strong>Unique CSS (UCSS)</strong> removes unused style definitions for a speedier page load overall."
msgstr "O <strong>CSS único (UCSS)</strong> remove definições de estilo não usadas, para acelerar o carregamento geral da página."

#: tpl/general/online.tpl.php:72
msgid "<strong>Critical CSS (CCSS)</strong> loads visible above-the-fold content faster and with full styling."
msgstr "O <strong>CSS crítico (CCSS)</strong> carrega o conteúdo visível acima da dobra mais rapidamente e com estilo completo."

#: tpl/general/online.tpl.php:70
msgid "QUIC.cloud's Page Optimization services address CSS bloat, and improve the user experience during page load, which can lead to improved page speed scores."
msgstr "Os serviços de otimização de páginas do QUIC.cloud corrigem o inchaço do CSS e melhoram a experiência do usuário durante o carregamento da página, o que pode levar a melhores pontuações de velocidade da página."

#: tpl/general/online.tpl.php:67
msgid "Processing for PNG, JPG, and WebP image formats is free. AVIF is available for a fee."
msgstr "O processamento dos formatos de imagem PNG, JPG e WebP é gratuito. O AVIF está disponível mediante uma taxa."

#: tpl/general/online.tpl.php:65
msgid "Optionally creates next-generation WebP or AVIF image files."
msgstr "Opcionalmente, cria arquivos de imagens WebP ou AVIF de última geração."

#: tpl/general/online.tpl.php:64
msgid "Processes your uploaded PNG and JPG images to produce smaller versions that don't sacrifice quality."
msgstr "Processa suas imagens PNG e JPG enviadas, para produzir versões menores sem sacrificar a qualidade."

#: tpl/general/online.tpl.php:62
msgid "QUIC.cloud's Image Optimization service does the following:"
msgstr "O serviço de otimização de imagens do QUIC.cloud faz o seguinte:"

#: tpl/general/online.tpl.php:58
msgid "<strong>Page Optimization</strong> streamlines page styles and visual elements for faster loading."
msgstr "A <strong>Otimização de páginas</strong> otimiza os estilos e os elementos visuais da página, para um carregamento mais rápido."

#: tpl/general/online.tpl.php:57
msgid "<strong>Image Optimization</strong> gives you smaller image file sizes that transmit faster."
msgstr "A <strong>Otimização de imagens</strong> permite tamanhos de arquivos de imagem menores, que são enviados mais rapidamente."

#: tpl/general/online.tpl.php:55
msgid "QUIC.cloud's Online Services improve your site in the following ways:"
msgstr "Os serviços on-line do QUIC.cloud melhoram seu site das seguintes maneiras:"

#: tpl/general/online.tpl.php:45
msgid "Speed up your WordPress site even further with QUIC.cloud Online Services and CDN."
msgstr "Acelere ainda mais seu site WordPress com os serviços on-line e CDN do QUIC.cloud."

#: tpl/general/online.tpl.php:44
msgid "QUIC.cloud Integration Disabled"
msgstr "Integração com o QUIC.cloud desativada"

#: tpl/general/online.tpl.php:15
msgid "QUIC.cloud Online Services"
msgstr "Serviços on-line do QUIC.cloud"

#: tpl/general/entry.tpl.php:8 tpl/general/online.tpl.php:54
msgid "Online Services"
msgstr "Serviços on-line"

#: tpl/db_optm/manage.tpl.php:182
msgid "Autoload"
msgstr "Carregamento automático"

#: tpl/dash/dashboard.tpl.php:866
msgid "Refresh QUIC.cloud status"
msgstr "Atualizar o status do QUIC.cloud"

#: tpl/dash/dashboard.tpl.php:440 tpl/dash/dashboard.tpl.php:508
msgid "Refresh"
msgstr "Atualizar"

#: tpl/dash/dashboard.tpl.php:415
msgid "You must be using one of the following products in order to measure Page Load Time:"
msgstr "Você precisa estar usando um dos seguintes produtos, para medir o tempo de carregamento da página:"

#: tpl/dash/dashboard.tpl.php:174
msgid "Refresh Usage"
msgstr "Atualizar o uso"

#: tpl/dash/dashboard.tpl.php:122 tpl/dash/dashboard.tpl.php:883
msgid "News"
msgstr "Notícias"

#: tpl/crawler/summary.tpl.php:21
msgid "You need to set the %s in Settings first before using the crawler"
msgstr "Você precisa definir o %s em “Configurações” primeiro, antes de usar o rastreador"

#: tpl/crawler/settings.tpl.php:114
msgid "You must set %1$s to %2$s before using this feature."
msgstr "Você precisa definir %1$s como %2$s antes de usar este recurso."

#: tpl/crawler/settings.tpl.php:105 tpl/crawler/summary.tpl.php:184
msgid "You must set %s before using this feature."
msgstr "Você precisa definir %s antes de usar este recurso."

#: tpl/cdn/qc.tpl.php:110 tpl/cdn/qc.tpl.php:113
msgid "My QUIC.cloud Dashboard"
msgstr "Meu painel do QUIC.cloud"

#: tpl/cdn/qc.tpl.php:107
msgid "You are currently using services as an anonymous user. To manage your QUIC.cloud options, use the button below to create an account and link to the QUIC.cloud Dashboard."
msgstr "No momento, você está usando os serviços como um usuário anônimo. Para gerenciar suas opções do QUIC.cloud, use o botão abaixo, para criar uma conta e acessar o painel do QUIC.cloud."

#: tpl/cdn/qc.tpl.php:104 tpl/cdn/qc.tpl.php:112
msgid "To manage your QUIC.cloud options, go to QUIC.cloud Dashboard."
msgstr "Para gerenciar suas opções do QUIC.cloud, acesse o painel do QUIC.cloud."

#: tpl/cdn/qc.tpl.php:100
msgid "To manage your QUIC.cloud options, please contact your hosting provider."
msgstr "Para gerenciar suas opções do QUIC.cloud, fale com seu provedor de hospedagem."

#: tpl/cdn/qc.tpl.php:98
msgid "To manage your QUIC.cloud options, go to your hosting provider's portal."
msgstr "Para gerenciar suas opções do QUIC.cloud, acesse o portal do seu provedor de hospedagem."

#: tpl/cdn/qc.tpl.php:80
msgid "QUIC.cloud CDN Options"
msgstr "Opções de CDN do QUIC.cloud"

#: tpl/cdn/qc.tpl.php:66
msgid "Best available WordPress performance, globally fast TTFB, easy setup, and <a %s>more</a>!"
msgstr "O melhor desempenho disponível para WordPress, TTFB globalmente rápido, configuração fácil e <a %s>muito mais</a>!"

#: tpl/cdn/qc.tpl.php:64
msgid "no matter where they live."
msgstr "não importa onde moram."

#: tpl/cdn/qc.tpl.php:63
msgid "Content Delivery Network Service"
msgstr "Serviço de rede de distribuição de conteúdo (CDN)"

#: tpl/cdn/qc.tpl.php:51 tpl/dash/dashboard.tpl.php:842
msgid "Enable QUIC.cloud CDN"
msgstr "Ativar o CDN do QUIC.cloud"

#: tpl/cdn/qc.tpl.php:49
msgid "Link & Enable QUIC.cloud CDN"
msgstr "Vincular e ativar o CDN do QUIC.cloud"

#: tpl/cdn/qc.tpl.php:45
msgid "QUIC.cloud CDN is <strong>not available</strong> for anonymous (unlinked) users."
msgstr "O CDN do QUIC.cloud não está <strong>disponível</strong> para usuários anônimos (não vinculados)."

#: tpl/cdn/qc.tpl.php:43
msgid "QUIC.cloud CDN is currently <strong>fully disabled</strong>."
msgstr "No momento, o CDN do QUIC.cloud está <strong>completamente desativado</strong>."

#: tpl/cdn/qc.tpl.php:36 tpl/dash/dashboard.tpl.php:160
msgid "Learn More about QUIC.cloud"
msgstr "Saiba mais sobre o QUIC.cloud"

#: tpl/cdn/qc.tpl.php:35 tpl/dash/dashboard.tpl.php:158
#: tpl/general/online.tpl.php:19
msgid "QUIC.cloud provides CDN and online optimization services, and is not required. You may use many features of this plugin without QUIC.cloud."
msgstr "O QUIC.cloud fornece CDN e serviços de otimização on-line, e não é obrigatório. Você pode usar muitos recursos deste plugin sem o QUIC.cloud."

#: tpl/cdn/qc.tpl.php:33 tpl/dash/dashboard.tpl.php:152
#: tpl/general/online.tpl.php:49 tpl/general/online.tpl.php:105
msgid "Enable QUIC.cloud services"
msgstr "Ativar os serviços do QUIC.cloud"

#: tpl/cdn/qc.tpl.php:32 tpl/general/online.tpl.php:46
#: tpl/general/online.tpl.php:132
msgid "Free monthly quota available."
msgstr "Cota mensal gratuita disponível."

#: tpl/cdn/qc.tpl.php:31 tpl/dash/dashboard.tpl.php:145
msgid "Speed up your WordPress site even further with <strong>QUIC.cloud Online Services and CDN</strong>."
msgstr "Acelere ainda mais seu site WordPress com os <strong>serviços on-line e CDN do QUIC.cloud</strong>."

#: tpl/cdn/qc.tpl.php:30 tpl/dash/dashboard.tpl.php:142
msgid "Accelerate, Optimize, Protect"
msgstr "Acelere, otimize e proteja"

#: tpl/cdn/qc.tpl.php:25
msgid "Check the status of your most important settings and the health of your CDN setup here."
msgstr "Verifique o status das suas configurações mais importantes e a integridade da configuração do CDN aqui."

#: tpl/cdn/qc.tpl.php:23
msgid "QUIC.cloud CDN Status Overview"
msgstr "Visão geral do status do CDN do QUIC.cloud"

#: tpl/cdn/qc.tpl.php:20 tpl/dash/dashboard.tpl.php:865
msgid "Refresh Status"
msgstr "Atualizar status"

#: tpl/cdn/entry.tpl.php:10
msgid "Other Static CDN"
msgstr "Outro CDN estático"

#: tpl/banner/new_version.php:113 tpl/banner/score.php:139
#: tpl/banner/slack.php:48
msgid "Dismiss this notice."
msgstr "Dispensar esta notificação."

#: tpl/banner/cloud_promo.tpl.php:35
msgid "Send to twitter to get %s bonus"
msgstr "Enviar ao Twitter para receber um bônus de %s"

#: tpl/banner/cloud_promo.tpl.php:26
msgid "Spread the love and earn %s credits to use in our QUIC.cloud online services."
msgstr "Espalhe o amor e ganhe %s créditos para usar em nossos serviços on-line do QUIC.cloud."

#: src/media.cls.php:388
msgid "No backup of unoptimized AVIF file exists."
msgstr "Não existe nenhum backup do arquivo AVIF não otimizado."

#: src/media.cls.php:380
msgid "AVIF saved %s"
msgstr "%s economizado em AVIF"

#: src/media.cls.php:374
msgid "AVIF file reduced by %1$s (%2$s)"
msgstr "Arquivo AVIF reduzido em %1$s (%2$s)"

#: src/media.cls.php:365
msgid "Currently using original (unoptimized) version of AVIF file."
msgstr "No momento, usando a versão original (não otimizada) do arquivo AVIF."

#: src/media.cls.php:358
msgid "Currently using optimized version of AVIF file."
msgstr "No momento, usando a versão otimizada do arquivo AVIF."

#: src/lang.cls.php:213
msgid "WebP/AVIF For Extra srcset"
msgstr "WebP/AVIF para srcset adicional"

#: src/lang.cls.php:209
msgid "Next-Gen Image Format"
msgstr "Formato de imagem de última geração"

#: src/img-optm.cls.php:2039
msgid "Enabled AVIF file successfully."
msgstr "Arquivo AVIF ativado."

#: src/img-optm.cls.php:2034
msgid "Disabled AVIF file successfully."
msgstr "Arquivo AVIF desativado."

#: src/img-optm.cls.php:1388
msgid "Reset image optimization counter successfully."
msgstr "Contador de otimização de imagens redefinido."

#: src/file.cls.php:132
msgid "Filename is empty!"
msgstr "O nome do arquivo está vazio!"

#: src/error.cls.php:57
msgid "You will need to finish %s setup to use the online services."
msgstr "Você precisa concluir a configuração do %s para usar os serviços on-line."

#: src/cloud.cls.php:1992
msgid "Sync QUIC.cloud status successfully."
msgstr "Status do QUIC.cloud sincronizado."

#: src/cloud.cls.php:1940
msgid "Linked to QUIC.cloud preview environment, for testing purpose only."
msgstr "Vinculado ao ambiente de pré-visualização do QUIC.cloud, apenas para fins de teste."

#: src/cloud.cls.php:1674
msgid "Click here to proceed."
msgstr "Clique aqui para continuar."

#: src/cloud.cls.php:1673
msgid "Site not recognized. QUIC.cloud deactivated automatically. Please reactivate your QUIC.cloud account."
msgstr "Site não reconhecido. O QUIC.cloud foi desativado automaticamente. Reative sua conta do QUIC.cloud."

#: src/cloud.cls.php:708
msgid "Reset %s activation successfully."
msgstr "A ativação do %s foi redefinida."

#: src/cloud.cls.php:602 src/cloud.cls.php:640 src/cloud.cls.php:680
msgid "Congratulations, %s successfully set this domain up for the online services with CDN service."
msgstr "Parabéns! O %s definiu este domínio para os serviços on-line com o serviço de CDN."

#: src/cloud.cls.php:597
msgid "Congratulations, %s successfully set this domain up for the online services."
msgstr "Parabéns! O %s configurou este domínio para os serviços on-line."

#: src/cloud.cls.php:595
msgid "Congratulations, %s successfully set this domain up for the anonymous online services."
msgstr "Parabéns! O %s configurou este domínio para os serviços on-line anônimos."

#: src/cloud.cls.php:573
msgid "%s activation data expired."
msgstr "Os dados de ativação do %s expiraram."

#: src/cloud.cls.php:566
msgid "Failed to parse %s activation status."
msgstr "Falha ao analisar o status de ativação do %s."

#: src/cloud.cls.php:559
msgid "Failed to validate %s activation data."
msgstr "Falha ao validar os dados de ativação do %s."

#: src/cloud.cls.php:300
msgid "Cert or key file does not exist."
msgstr "O arquivo de certificado ou chave não existe."

#: src/cloud.cls.php:282 src/cloud.cls.php:328 src/cloud.cls.php:355
#: src/cloud.cls.php:371 src/cloud.cls.php:390 src/cloud.cls.php:408
msgid "You need to activate QC first."
msgstr "Você precisa ativar o QC primeiro."

#: src/cloud.cls.php:235 src/cloud.cls.php:290
msgid "You need to set the %1$s first. Please use the command %2$s to set."
msgstr "Você precisa definir o %1$s primeiro. Use o comando %2$s para definir."

#: src/cloud.cls.php:177 src/cloud.cls.php:260
msgid "Failed to get echo data from WPAPI"
msgstr "Falha ao coletar os dados de eco da WPAPI"

#: src/admin-settings.cls.php:92
msgid "The user with id %s has editor access, which is not allowed for the role simulator."
msgstr "O usuário com o ID %s tem acesso de editor, o que não é permitido para o simulador de funções."

#: src/error.cls.php:75
msgid "You have used all of your quota left for current service this month."
msgstr "Você usou toda a sua cota restante para o serviço atual neste mês."

#: src/error.cls.php:67 src/error.cls.php:80
msgid "Learn more or purchase additional quota."
msgstr "Saiba mais ou compre cotas adicionais."

#: src/error.cls.php:62
msgid "You have used all of your daily quota for today."
msgstr "Você usou toda a sua cota diária de hoje."

#: tpl/page_optm/settings_html.tpl.php:100
msgid "If comment to be kept is like: %1$s write: %2$s"
msgstr "Se o comentário a ser mantido for do tipo: %1$s escrever: %2$s"

#: tpl/page_optm/settings_html.tpl.php:98
msgid "When minifying HTML do not discard comments that match a specified pattern."
msgstr "Ao minificar o HTML, não descartar comentários que correspondam a um determinado padrão."

#: tpl/cache/settings-advanced.tpl.php:39
msgid "Specify an AJAX action in POST/GET and the number of seconds to cache that request, separated by a space."
msgstr "Especifique uma ação AJAX em POST/GET e o número de segundos para armazenar em cache esta solicitação, separados por um espaço."

#: src/lang.cls.php:151
msgid "HTML Keep Comments"
msgstr "Manter comentários em HTML"

#: src/lang.cls.php:99
msgid "AJAX Cache TTL"
msgstr "TTL do cache AJAX"

#: src/error.cls.php:92
msgid "You have images waiting to be pulled. Please wait for the automatic pull to complete, or pull them down manually now."
msgstr "Você tem imagens aguardando para serem extraídas. Aguarde a conclusão da extração automática ou extraia-as manualmente agora."

#: tpl/db_optm/manage.tpl.php:18
msgid "Clean all orphaned post meta records"
msgstr "Limpar todos os registros de metadados de posts órfãos"

#: tpl/db_optm/manage.tpl.php:17
msgid "Orphaned Post Meta"
msgstr "Metadados de posts órfãos"

#: tpl/dash/dashboard.tpl.php:852
msgid "Globally fast TTFB, easy setup, and <a %s>more</a>!"
msgstr "TTFB globalmente rápido, configuração fácil e <a %s>mais</a>!"

#: tpl/dash/dashboard.tpl.php:849
msgid "Best available WordPress performance"
msgstr "O melhor desempenho disponível para WordPress"

#: src/db-optm.cls.php:204
msgid "Clean orphaned post meta successfully."
msgstr "Metadados de posts órfãos limpos."

#: tpl/img_optm/summary.tpl.php:317
msgid "Last Pulled"
msgstr "Última extração"

#: tpl/cache/settings_inc.login_cookie.tpl.php:104
msgid "You can list the 3rd party vary cookies here."
msgstr "Você pode listar os cookies de variação de terceiros aqui."

#: src/lang.cls.php:226
msgid "Vary Cookies"
msgstr "Cookies de variação"

#: tpl/page_optm/settings_html.tpl.php:67
msgid "Preconnecting speeds up future loads from a given origin."
msgstr "Pré-conectar acelera os carregamentos futuros a partir de uma origem específica."

#: thirdparty/woocommerce.content.tpl.php:71
msgid "If your theme does not use JS to update the mini cart, you must enable this option to display the correct cart contents."
msgstr "Se o seu tema não usa JavaScript para atualizar o mini carrinho, você deve ativar esta opção para exibir o conteúdo correto do carrinho."

#: thirdparty/woocommerce.content.tpl.php:70
msgid "Generate a separate vary cache copy for the mini cart when the cart is not empty."
msgstr "Gerar uma cópia de cache variável separada para o mini carrinho quando o carrinho não estiver vazio."

#: thirdparty/woocommerce.content.tpl.php:62
msgid "Vary for Mini Cart"
msgstr "Variável para o mini carrinho"

#: src/lang.cls.php:161
msgid "DNS Preconnect"
msgstr "Pré-conexão de DNS"

#: src/doc.cls.php:39
msgid "This setting is %1$s for certain qualifying requests due to %2$s!"
msgstr "Essa configuração está %1$s para determinadas solicitações qualificadas devido a %2$s!"

#: tpl/page_optm/settings_tuning.tpl.php:34
msgid "Listed JS files or inline JS code will be delayed."
msgstr "Os arquivos JS listados ou o código JS incorporado serão atrasados."

#: tpl/crawler/map.tpl.php:45
msgid "URL Search"
msgstr "Pesquisa de URL"

#: src/lang.cls.php:163
msgid "JS Delayed Includes"
msgstr "Inclusões de JS atrasados"

#: src/cloud.cls.php:1459
msgid "Your domain_key has been temporarily blocklisted to prevent abuse. You may contact support at QUIC.cloud to learn more."
msgstr "Sua domain_key foi temporariamente incluída em uma lista de bloqueios para evitar abusos. Entre em contato com o suporte em QUIC.cloud para saber mais."

#: src/cloud.cls.php:1454
msgid "Cloud server refused the current request due to unpulled images. Please pull the images first."
msgstr "O servidor em nuvem recusou a solicitação atual devido a imagens não recuperadas. Obtenha as imagens primeiro."

#: tpl/crawler/summary.tpl.php:96
msgid "Current server load"
msgstr "Carga atual do servidor"

#: src/object-cache.cls.php:484
msgid "Redis encountered a fatal error: %1$s (code: %2$d)"
msgstr "O Redis encontrou um erro fatal: %1$s (código: %2$d)"

#: src/img-optm.cls.php:876
msgid "Started async image optimization request"
msgstr "Iniciada a solicitação de otimização de imagem assíncrona"

#: src/crawler.cls.php:229
msgid "Started async crawling"
msgstr "Rastreamento assíncrono iniciado"

#: src/conf.cls.php:508
msgid "Saving option failed. IPv4 only for %s."
msgstr "Falha ao salvar a opção. Apenas IPv4 para %s."

#: src/cloud.cls.php:1466
msgid "Cloud server refused the current request due to rate limiting. Please try again later."
msgstr "O servidor em nuvem recusou a solicitação atual devido a limitação de taxa. Tente novamente mais tarde."

#: tpl/img_optm/summary.tpl.php:290
msgid "Maximum image post id"
msgstr "ID máximo do post da imagem"

#: tpl/img_optm/summary.tpl.php:289 tpl/img_optm/summary.tpl.php:356
msgid "Current image post id position"
msgstr "Posição atual do ID do post da imagem"

#: src/lang.cls.php:26
msgid "Images ready to request"
msgstr "Imagens prontas para solicitar"

#: tpl/dash/dashboard.tpl.php:378 tpl/general/online.tpl.php:24
#: tpl/img_optm/summary.tpl.php:46 tpl/img_optm/summary.tpl.php:48
#: tpl/page_optm/settings_css.tpl.php:106
#: tpl/page_optm/settings_css.tpl.php:250
#: tpl/page_optm/settings_media.tpl.php:184
#: tpl/page_optm/settings_vpi.tpl.php:53
msgid "Redetect"
msgstr "Redetectar"

#: tpl/dash/dashboard.tpl.php:265
msgid "PAYG balance and usage not included in above quota calculation."
msgstr "Saldo e uso PAYG não incluídos no cálculo de cota acima."

#: tpl/dash/dashboard.tpl.php:265
msgid "PAYG used this month"
msgstr "PAYG usado este mês"

#. translators: %1$s: Socket name, %2$s: Host field title, %3$s: Example socket
#. path
#. translators: %1$s: Socket name, %2$s: Port field title, %3$s: Port value
#: tpl/cache/settings_inc.object.tpl.php:107
#: tpl/cache/settings_inc.object.tpl.php:146
msgid "If you are using a %1$s socket, %2$s should be set to %3$s"
msgstr "Se você estiver usando um soquete %1$s, %2$s deve ser definido como %3$s"

#: src/root.cls.php:197
msgid "All QUIC.cloud service queues have been cleared."
msgstr "Todas as filas de serviço QUIC.cloud foram limpas."

#. translators: %s: The type of the given cache key.
#: src/object.lib.php:519
msgid "Cache key must be integer or non-empty string, %s given."
msgstr "A chave de cache deve ser um número inteiro ou uma string não vazia, foi fornecido %s."

#: src/object.lib.php:517
msgid "Cache key must not be an empty string."
msgstr "A chave de cache não deve ser uma string vazia."

#: src/lang.cls.php:172
msgid "JS Deferred / Delayed Excludes"
msgstr "Exclusões de JS adiado/atrasado"

#: src/doc.cls.php:168
msgid "The queue is processed asynchronously. It may take time."
msgstr "A fila é processada de forma assíncrona. Isso pode levar algum tempo."

#: src/cloud.cls.php:1185
msgid "In order to use QC services, need a real domain name, cannot use an IP."
msgstr "Para usar os serviços do QC, é necessário um nome de domínio real; não é possível usar um IP."

#: tpl/presets/standard.tpl.php:191
msgid "Restore Settings"
msgstr "Restaurar configurações"

#: tpl/presets/standard.tpl.php:189
msgid "This will restore the backup settings created %1$s before applying the %2$s preset. Any changes made since then will be lost. Do you want to continue?"
msgstr "Isso irá restaurar as configurações de backup criadas %1$s antes de aplicar a predefinição %2$s. Quaisquer alterações feitas desde então serão perdidas. Deseja continuar?"

#: tpl/presets/standard.tpl.php:185
msgid "Backup created %1$s before applying the %2$s preset"
msgstr "Backup criado %1$s antes de aplicar a predefinição %2$s"

#: tpl/presets/standard.tpl.php:174
msgid "Applied the %1$s preset %2$s"
msgstr "Aplicada a predefinição %1$s %2$s"

#: tpl/presets/standard.tpl.php:171
msgid "Restored backup settings %1$s"
msgstr "Configurações de backup restauradas %1$s"

#: tpl/presets/standard.tpl.php:169
msgid "Error: Failed to apply the settings %1$s"
msgstr "Erro: Falha ao aplicar as configurações %1$s"

#: tpl/presets/standard.tpl.php:159
msgid "History"
msgstr "Histórico"

#: tpl/presets/standard.tpl.php:148
msgid "unknown"
msgstr "desconhecido"

#: tpl/presets/standard.tpl.php:129
msgid "Apply Preset"
msgstr "Aplicar predefinição"

#: tpl/presets/standard.tpl.php:127
msgid "This will back up your current settings and replace them with the %1$s preset settings. Do you want to continue?"
msgstr "Isso fará backup das suas configurações atuais e as substituirá pelas configurações predefinidas do %1$s. Deseja continuar?"

#: tpl/presets/standard.tpl.php:117
msgid "Who should use this preset?"
msgstr "Quem deve usar esta predefinição?"

#: tpl/presets/standard.tpl.php:92
msgid "Use an official LiteSpeed-designed Preset to configure your site in one click. Try no-risk caching essentials, extreme optimization, or something in between."
msgstr "Use uma predefinição oficial projetada pelo LiteSpeed para configurar seu site com um único clique. Experimente o armazenamento em cache \"Essenciais\" sem risco, ou a otimização \"Extremo\" ou algo intermediário."

#: tpl/presets/standard.tpl.php:88
msgid "LiteSpeed Cache Standard Presets"
msgstr "Predefinições padrão do LiteSpeed Cache"

#: tpl/presets/standard.tpl.php:81
msgid "A Domain Key is required to use this preset. Enables the maximum level of optimizations for improved page speed scores."
msgstr "É necessário ter uma chave de domínio para usar esta predefinição. Ativa o nível máximo de otimizações para melhores pontuações de velocidade da página."

#: tpl/presets/standard.tpl.php:80
msgid "This preset almost certainly will require testing and exclusions for some CSS, JS and Lazy Loaded images. Pay special attention to logos, or HTML-based slider images."
msgstr "Esta predefinição quase certamente exigirá testes e exclusões para alguns CSS, JS e imagens de carregamento tardio. Preste atenção especial aos logos ou imagens de controle deslizante (sliders) baseados em HTML."

#: tpl/presets/standard.tpl.php:77
msgid "Inline CSS added to Combine"
msgstr "CSS embutido adicionado para combinar"

#: tpl/presets/standard.tpl.php:76
msgid "Inline JS added to Combine"
msgstr "JS embutido adicionado para combinar"

#: tpl/presets/standard.tpl.php:75
msgid "JS Delayed"
msgstr "JS atrasado"

#: tpl/presets/standard.tpl.php:74
msgid "Viewport Image Generation"
msgstr "Geração de imagens da janela de visualização (viewport)"

#: tpl/presets/standard.tpl.php:73
msgid "Lazy Load for Images"
msgstr "Carregamento tardio para imagens"

#: tpl/presets/standard.tpl.php:72
msgid "Everything in Aggressive, Plus"
msgstr "Tudo do Agressivo, e mais"

#: tpl/presets/standard.tpl.php:70
msgid "Extreme"
msgstr "Extremo"

#: tpl/presets/standard.tpl.php:64
msgid "This preset might work out of the box for some websites, but be sure to test! Some CSS or JS exclusions may be necessary in Page Optimization > Tuning."
msgstr "Esta predefinição pode funcionar imediatamente em alguns sites, mas certifique-se de testar! Algumas exclusões de CSS ou JS podem ser necessárias em Otimização de página > Ajuste."

#: tpl/presets/standard.tpl.php:61
msgid "Lazy Load for Iframes"
msgstr "Carregamento tardio para iframes"

#: tpl/presets/standard.tpl.php:60
msgid "Removed Unused CSS for Users"
msgstr "Remoção de CSS não usado para usuários"

#: tpl/presets/standard.tpl.php:59
msgid "Asynchronous CSS Loading with Critical CSS"
msgstr "Carregamento assíncrono de CSS com CSS crítico"

#: tpl/presets/standard.tpl.php:58
msgid "CSS & JS Combine"
msgstr "Combinação de CSS e JS"

#: tpl/presets/standard.tpl.php:57
msgid "Everything in Advanced, Plus"
msgstr "Tudo do Avançado, e mais"

#: tpl/presets/standard.tpl.php:55
msgid "Aggressive"
msgstr "Agressivo"

#: tpl/presets/standard.tpl.php:50 tpl/presets/standard.tpl.php:65
msgid "A Domain Key is required to use this preset. Includes many optimizations known to improve page speed scores."
msgstr "É necessária uma chave de domínio para usar esta predefinição. Inclui várias otimizações conhecidas por melhorar as pontuações de velocidade da página."

#: tpl/presets/standard.tpl.php:49
msgid "This preset is good for most websites, and is unlikely to cause conflicts. Any CSS or JS conflicts may be resolved with Page Optimization > Tuning tools."
msgstr "Esta predefinição é adequada para a maioria dos sites e é improvável que cause conflitos. Quaisquer conflitos de CSS ou JS podem ser resolvidos com as ferramentas em Otimização de página > Ajuste."

#: tpl/presets/standard.tpl.php:44
msgid "Remove Query Strings from Static Files"
msgstr "Remover strings de consulta de arquivos estáticos"

#: tpl/presets/standard.tpl.php:42
msgid "DNS Prefetch for static files"
msgstr "Pré-busca de DNS para arquivos estáticos"

#: tpl/presets/standard.tpl.php:41
msgid "JS Defer for both external and inline JS"
msgstr "Adiar JS para JS externo e embutido"

#: tpl/presets/standard.tpl.php:39
msgid "CSS, JS and HTML Minification"
msgstr "Minificação de CSS, JS e HTML"

#: tpl/presets/standard.tpl.php:38
msgid "Guest Mode and Guest Optimization"
msgstr "Modo de visitante e otimização de visitantes"

#: tpl/presets/standard.tpl.php:37
msgid "Everything in Basic, Plus"
msgstr "Tudo do Básico, e mais"

#: tpl/presets/standard.tpl.php:35
msgid "Advanced (Recommended)"
msgstr "Avançado (recomendado)"

#: tpl/presets/standard.tpl.php:30
msgid "A Domain Key is required to use this preset. Includes optimizations known to improve site score in page speed measurement tools."
msgstr "É necessária uma chave de domínio para usar esta predefinição. Inclui otimizações conhecidas por melhorar a pontuação do site em ferramentas de medição de velocidade da página."

#: tpl/presets/standard.tpl.php:29
msgid "This low-risk preset introduces basic optimizations for speed and user experience. Appropriate for enthusiastic beginners."
msgstr "Esta predefinição de baixo risco apresenta otimizações básicas para velocidade e experiência do usuário. Apropriada para iniciantes entusiastas."

#: tpl/presets/standard.tpl.php:26
msgid "Mobile Cache"
msgstr "Cache para dispositivos móveis"

#: tpl/presets/standard.tpl.php:24
msgid "Everything in Essentials, Plus"
msgstr "Tudo do Essenciais e mais"

#: tpl/presets/standard.tpl.php:17
msgid "A Domain Key is not required to use this preset. Only basic caching features are enabled."
msgstr "Não é necessária uma chave de domínio para usar esta predefinição. Apenas recursos básicos de cache são ativados."

#: tpl/presets/standard.tpl.php:16
msgid "This no-risk preset is appropriate for all websites. Good for new users, simple websites, or cache-oriented development."
msgstr "Esta predefinição sem risco é adequada para todos os tipos de sites. É indicada para novos usuários, sites simples ou desenvolvimento orientado para cache."

#: tpl/presets/standard.tpl.php:12
msgid "Higher TTL"
msgstr "TTL mais alto"

#: tpl/presets/standard.tpl.php:11
msgid "Default Cache"
msgstr "Cache padrão"

#: tpl/presets/standard.tpl.php:9
msgid "Essentials"
msgstr "Essenciais"

#: tpl/presets/entry.tpl.php:15
msgid "LiteSpeed Cache Configuration Presets"
msgstr "Predefinições de configuração do LiteSpeed Cache"

#: tpl/presets/entry.tpl.php:7
msgid "Standard Presets"
msgstr "Predefinições padrão"

#: tpl/page_optm/settings_tuning_css.tpl.php:42
msgid "Listed CSS files will be excluded from UCSS and saved to inline."
msgstr "Os arquivos CSS listados serão excluídos do UCSS e salvos de forma embutida."

#: src/lang.cls.php:144
msgid "UCSS File Excludes and Inline"
msgstr "Exclusões de arquivo UCSS e embutido"

#: src/lang.cls.php:143
msgid "UCSS Selector Allowlist"
msgstr "Lista de permissões do seletor UCSS"

#: src/admin-display.cls.php:124
msgid "Presets"
msgstr "Predefinições"

#: tpl/dash/dashboard.tpl.php:313
msgid "Partner Benefits Provided by"
msgstr "Benefícios para parceiros fornecidos por"

#: tpl/toolbox/log_viewer.tpl.php:133
msgid "LiteSpeed Logs"
msgstr "Registros do LiteSpeed"

#: tpl/toolbox/log_viewer.tpl.php:21
msgid "Crawler Log"
msgstr "Registro de rastreamento"

#: tpl/toolbox/log_viewer.tpl.php:16
msgid "Purge Log"
msgstr "Limpar registro"

#: tpl/toolbox/settings-debug.tpl.php:157
msgid "Prevent writing log entries that include listed strings."
msgstr "Impedir a gravação de entradas de registro que incluam strings (cadeias de caracteres) listadas."

#: tpl/toolbox/settings-debug.tpl.php:19
msgid "View Site Before Cache"
msgstr "Ver site antes do cache"

#: tpl/toolbox/settings-debug.tpl.php:15
msgid "View Site Before Optimization"
msgstr "Ver site antes da otimização"

#: tpl/toolbox/settings-debug.tpl.php:11
msgid "Debug Helpers"
msgstr "Auxiliares de depuração"

#: tpl/page_optm/settings_vpi.tpl.php:121
msgid "Enable Viewport Images auto generation cron."
msgstr "Ativar a geração automática de imagens na janela de visualização (viewport) via cron."

#: tpl/page_optm/settings_vpi.tpl.php:32
msgid "This enables the page's initial screenful of imagery to be fully displayed without delay."
msgstr "Isso permite que o conjunto inicial de imagens da página seja totalmente exibido sem atrasos."

#: tpl/page_optm/settings_vpi.tpl.php:31
msgid "The Viewport Images service detects which images appear above the fold, and excludes them from lazy load."
msgstr "O serviço de imagens na janela de visualização (viewport) detecta quais imagens aparecem acima da dobra e as exclui do carregamento tardio (lazy load)."

#: tpl/page_optm/settings_vpi.tpl.php:30
msgid "When you use Lazy Load, it will delay the loading of all images on a page."
msgstr "Quando você usa o \"Carregamento tardio\", ele atrasará o carregamento de todas as imagens em uma página."

#: tpl/page_optm/settings_media.tpl.php:250
msgid "Use %1$s to bypass remote image dimension check when %2$s is ON."
msgstr "Use %1$s para ignorar a verificação das dimensões de imagem remotas quando %2$s estiver ATIVADO."

#: tpl/page_optm/entry.tpl.php:11
msgid "VPI"
msgstr "VPI"

#: tpl/general/settings.tpl.php:63 tpl/page_optm/settings_media.tpl.php:244
#: tpl/page_optm/settings_vpi.tpl.php:37
msgid "%s must be turned ON for this setting to work."
msgstr "%s deve estar ATIVADO para que esta configuração funcione."

#: tpl/dash/dashboard.tpl.php:744
msgid "Viewport Image"
msgstr "Imagem da janela de visualização (viewport)"

#: tpl/crawler/blacklist.tpl.php:62
msgid "Filter %s available to disable blocklist."
msgstr "Filtro %s disponível para desativar a lista de bloqueio."

#: tpl/crawler/blacklist.tpl.php:59
msgid "PHP Constant %s available to disable blocklist."
msgstr "Constante PHP %s disponível para desativar a lista de bloqueio."

#: thirdparty/litespeed-check.cls.php:74 thirdparty/litespeed-check.cls.php:122
msgid "Please consider disabling the following detected plugins, as they may conflict with LiteSpeed Cache:"
msgstr "Considere desativar os seguintes plugins detectados, pois eles podem entrar em conflito com o LiteSpeed Cache:"

#: src/metabox.cls.php:63
msgid "LiteSpeed Options"
msgstr "Opções do LiteSpeed"

#: src/metabox.cls.php:34
msgid "Mobile"
msgstr "Dispositivo móvel"

#: src/metabox.cls.php:32
msgid "Disable VPI"
msgstr "Desativar VPI"

#: src/metabox.cls.php:31
msgid "Disable Image Lazyload"
msgstr "Desativar carregamento lento de imagens"

#: src/metabox.cls.php:30
msgid "Disable Cache"
msgstr "Desativar cache"

#: src/lang.cls.php:263
msgid "Debug String Excludes"
msgstr "Exclusões de string de depuração"

#: src/lang.cls.php:204
msgid "Viewport Images Cron"
msgstr "Cron de imagens da janela de visualização (viewport)"

#: src/lang.cls.php:203 src/metabox.cls.php:33 src/metabox.cls.php:34
#: tpl/page_optm/settings_vpi.tpl.php:15
msgid "Viewport Images"
msgstr "Imagens da janela de visualização (viewport)"

#: src/lang.cls.php:54
msgid "Alias is in use by another QUIC.cloud account."
msgstr "O alias está sendo usado por outra conta QUIC.cloud."

#: src/lang.cls.php:52
msgid "Unable to automatically add %1$s as a Domain Alias for main %2$s domain."
msgstr "Não é possível adicionar automaticamente %1$s como um alias de domínio para o domínio principal %2$s."

#: src/lang.cls.php:47
msgid "Unable to automatically add %1$s as a Domain Alias for main %2$s domain, due to potential CDN conflict."
msgstr "Não é possível adicionar automaticamente %1$s como um alias de domínio para o domínio principal %2$s, devido a um possível conflito com o CDN."

#: src/error.cls.php:212
msgid "You cannot remove this DNS zone, because it is still in use. Please update the domain's nameservers, then try to delete this zone again, otherwise your site will become inaccessible."
msgstr "Você não pode remover esta zona DNS, pois ela ainda está em uso. Atualize os servidores de nomes do domínio e tente excluir esta zona novamente, caso contrário, seu site ficará inacessível."

#: src/error.cls.php:115
msgid "The site is not a valid alias on QUIC.cloud."
msgstr "O site não é um alias válido no QUIC.cloud."

#: tpl/page_optm/settings_localization.tpl.php:132
msgid "Please thoroughly test each JS file you add to ensure it functions as expected."
msgstr "Teste minuciosamente cada arquivo JS que você adicionar para garantir que funcione conforme o esperado."

#: tpl/page_optm/settings_localization.tpl.php:99
msgid "Please thoroughly test all items in %s to ensure they function as expected."
msgstr "Testar minuciosamente todos os itens em %s para garantir que funcionem conforme o esperado."

#: tpl/page_optm/settings_tuning_css.tpl.php:90
msgid "Use %1$s to bypass UCSS for the pages which page type is %2$s."
msgstr "Use %1$s para ignorar o UCSS para as páginas cujo tipo de página seja %2$s."

#: tpl/page_optm/settings_tuning_css.tpl.php:89
msgid "Use %1$s to generate one single UCSS for the pages which page type is %2$s while other page types still per URL."
msgstr "Use %1$s para gerar um único UCSS para as páginas cujo tipo de página seja %2$s, enquanto os outros tipos de página continuam por URL."

#: tpl/page_optm/settings_css.tpl.php:82
msgid "Filter %s available for UCSS per page type generation."
msgstr "Filtro %s disponível para geração de UCSS por tipo de página."

#: tpl/general/settings_inc.guest.tpl.php:36
#: tpl/general/settings_inc.guest.tpl.php:39
msgid "Guest Mode failed to test."
msgstr "O modo visitante falhou no teste."

#: tpl/general/settings_inc.guest.tpl.php:33
msgid "Guest Mode passed testing."
msgstr "O modo visitante passou no teste."

#: tpl/general/settings_inc.guest.tpl.php:26
msgid "Testing"
msgstr "Testando"

#: tpl/general/settings_inc.guest.tpl.php:25
msgid "Guest Mode testing result"
msgstr "Resultado do teste do modo visitante"

#: tpl/crawler/blacklist.tpl.php:65
msgid "Not blocklisted"
msgstr "Não está na lista de bloqueio"

#: tpl/cache/settings_inc.cache_mobile.tpl.php:25
msgid "Learn more about when this is needed"
msgstr "Saiba mais sobre quando isso é necessário"

#: src/purge.cls.php:344
msgid "Cleaned all localized resource entries."
msgstr "Todas as entradas de recursos localizadas foram limpas."

#: tpl/dash/dashboard.tpl.php:110 tpl/dash/dashboard.tpl.php:815
msgid "<b>Last crawled:</b> %d item(s)"
msgstr "<b>Último rastreamento:</b> %d item(ns)"

#: tpl/toolbox/entry.tpl.php:15
msgid "View .htaccess"
msgstr "Ver .htaccess"

#: tpl/toolbox/edit_htaccess.tpl.php:60 tpl/toolbox/edit_htaccess.tpl.php:78
msgid "You can use this code %1$s in %2$s to specify the htaccess file path."
msgstr "Você pode usar este código %1$s em %2$s para especificar o caminho do arquivo .htaccess."

#: tpl/toolbox/edit_htaccess.tpl.php:59 tpl/toolbox/edit_htaccess.tpl.php:77
msgid "PHP Constant %s is supported."
msgstr "A constante %s do PHP é suportada."

#: tpl/toolbox/edit_htaccess.tpl.php:55 tpl/toolbox/edit_htaccess.tpl.php:73
msgid "Default path is"
msgstr "O caminho padrão é"

#: tpl/toolbox/edit_htaccess.tpl.php:43
msgid ".htaccess Path"
msgstr "Caminho do .htaccess"

#: tpl/general/settings.tpl.php:40
msgid "Please read all warnings before enabling this option."
msgstr "Leia todos os alertas antes de ativar esta opção."

#: tpl/toolbox/purge.tpl.php:72
msgid "This will delete all generated unique CSS files"
msgstr "Isso irá excluir todos os arquivos de CSS únicos gerados"

#: tpl/toolbox/beta_test.tpl.php:57
msgid "In order to avoid an upgrade error, you must be using %1$s or later before you can upgrade to %2$s versions."
msgstr "Para evitar um erro de atualização, você deve estar usando %1$s ou posterior antes de poder atualizar para versões %2$s."

#: tpl/toolbox/beta_test.tpl.php:51
msgid "Use latest GitHub Dev/Master commit"
msgstr "Use o último commit Dev/Master do GitHub"

#: tpl/toolbox/beta_test.tpl.php:51
msgid "Press the %s button to use the most recent GitHub commit. Master is for release candidate & Dev is for experimental testing."
msgstr "Pressione o botão %s para usar o commit mais recente do GitHub. Master é para candidato a lançamento e Dev é para testes experimentais."

#: tpl/toolbox/beta_test.tpl.php:47
msgid "Downgrade not recommended. May cause fatal error due to refactored code."
msgstr "Desaconselhamos a reversão de versão. Pode causar erros fatais devido ao código reestruturado."

#: tpl/page_optm/settings_tuning.tpl.php:135
msgid "Only optimize pages for guest (not logged in) visitors. If turned this OFF, CSS/JS/CCSS files will be doubled by each user group."
msgstr "Otimizar apenas as páginas para visitantes em modo convidado (não conectados). Se DESATIVADO, os arquivos CSS/JS/CCSS serão duplicados para cada grupo de usuários."

#: tpl/page_optm/settings_tuning.tpl.php:97
msgid "Listed JS files or inline JS code will not be optimized by %s."
msgstr "Os arquivos JS listados ou o código JS incorporado não serão otimizados pelo %s."

#: tpl/page_optm/settings_tuning_css.tpl.php:82
msgid "Listed URI will not generate UCSS."
msgstr "O URI listado não irá gerar UCSS."

#: tpl/page_optm/settings_tuning_css.tpl.php:64
msgid "The selector must exist in the CSS. Parent classes in the HTML will not work."
msgstr "O seletor deve existir no CSS. Classes principais no HTML não funcionarão."

#: tpl/page_optm/settings_tuning_css.tpl.php:60
#: tpl/page_optm/settings_tuning_css.tpl.php:136
msgid "Wildcard %s supported."
msgstr "Caractere curinga %s é suportado."

#: tpl/page_optm/settings_media_exc.tpl.php:25
msgid "Useful for above-the-fold images causing CLS (a Core Web Vitals metric)."
msgstr "Útil para imagens acima da dobra que causam CLS (uma métrica do Core Web Vitals)."

#: tpl/page_optm/settings_media.tpl.php:239
msgid "Set an explicit width and height on image elements to reduce layout shifts and improve CLS (a Core Web Vitals metric)."
msgstr "Defina uma largura e altura explícitas nos elementos de imagem para reduzir deslocamentos de layout e melhorar o CLS (uma métrica do Core Web Vitals)."

#: tpl/page_optm/settings_media.tpl.php:131
msgid "Changes to this setting do not apply to already-generated LQIPs. To regenerate existing LQIPs, please %s first from the admin bar menu."
msgstr "As alterações nesta configuração não se aplicam aos LQIPs já gerados. Para generar novamente os LQIPs existentes, primeiro %s no menu da barra de administração."

#: tpl/page_optm/settings_js.tpl.php:71
msgid "Deferring until page is parsed or delaying till interaction can help reduce resource contention and improve performance causing a lower FID (Core Web Vitals metric)."
msgstr "Adiar até que a página seja analisada ou atrasar até a interação, pode ajudar a reduzir a contenção de recursos e melhorar o desempenho, causando um FID (métrica do Core Web Vitals) mais baixo."

#: tpl/page_optm/settings_js.tpl.php:69
msgid "Delayed"
msgstr "Atrasado"

#: tpl/page_optm/settings_js.tpl.php:44
msgid "JS error can be found from the developer console of browser by right clicking and choosing Inspect."
msgstr "Erros de JS podem ser encontrados no console do desenvolvedor do navegador clicando com o botão direito e escolhendo \"Inspecionar\"."

#: tpl/page_optm/settings_js.tpl.php:43 tpl/page_optm/settings_js.tpl.php:77
msgid "This option may result in a JS error or layout issue on frontend pages with certain themes/plugins."
msgstr "Essa opção pode resultar em um erro de JS ou problema de layout em páginas de interface com determinados temas/plugins."

#: tpl/page_optm/settings_html.tpl.php:139
msgid "This will also add a preconnect to Google Fonts to establish a connection earlier."
msgstr "Isso também adicionará uma pré-conexão ao Google Fonts para estabelecer uma conexão mais cedo."

#: tpl/page_optm/settings_html.tpl.php:83
msgid "Delay rendering off-screen HTML elements by its selector."
msgstr "Atrasar a renderização de elementos HTML fora da tela pelo seletor."

#: tpl/page_optm/settings_css.tpl.php:321
msgid "Disable this option to generate CCSS per Post Type instead of per page. This can save significant CCSS quota, however it may result in incorrect CSS styling if your site uses a page builder."
msgstr "Desative esta opção para gerar CCSS por tipo de post em vez de por página. Isso pode economizar uma quantidade significativa de cota de CCSS, porém pode resultar em estilos CSS incorretos se o seu site usar um construtor de páginas."

#: tpl/page_optm/settings_css.tpl.php:231
msgid "This option is bypassed due to %s option."
msgstr "Esta opção é ignorada devido à opção %s."

#: tpl/page_optm/settings_css.tpl.php:225
msgid "Elements with attribute %s in HTML code will be excluded."
msgstr "Elementos com o atributo %s no código HTML serão excluídos."

#: tpl/page_optm/settings_css.tpl.php:218
msgid "Use QUIC.cloud online service to generate critical CSS and load remaining CSS asynchronously."
msgstr "Use o serviço on-line do QUIC.cloud para gerar CSS crítico e carregar o restante do CSS de forma assíncrona."

#: tpl/page_optm/settings_css.tpl.php:182
msgid "This option will automatically bypass %s option."
msgstr "Esta opção irá ignorar automaticamente a opção %s."

#: tpl/page_optm/settings_css.tpl.php:179
msgid "Inline UCSS to reduce the extra CSS file loading. This option will not be automatically turned on for %1$s pages. To use it on %1$s pages, please set it to ON."
msgstr "Use UCSS embutido para reduzir o carregamento extra de arquivos CSS. Esta opção não será ativada automaticamente para páginas %1$s. Para usá-la em páginas %1$s, defina como ATIVADO."

#: tpl/page_optm/settings_css.tpl.php:155
#: tpl/page_optm/settings_css.tpl.php:160
#: tpl/page_optm/settings_css.tpl.php:299
#: tpl/page_optm/settings_css.tpl.php:304 tpl/page_optm/settings_vpi.tpl.php:98
#: tpl/page_optm/settings_vpi.tpl.php:103
msgid "Run %s Queue Manually"
msgstr "Executar a fila %s manualmente"

#: tpl/page_optm/settings_css.tpl.php:87
msgid "This option is bypassed because %1$s option is %2$s."
msgstr "Esta opção é ignorada porque a opção %1$s é %2$s."

#: tpl/page_optm/settings_css.tpl.php:80
msgid "Automatic generation of unique CSS is in the background via a cron-based queue."
msgstr "A geração automática de CSS único é feita em segundo plano por meio de uma fila baseada em cron."

#: tpl/page_optm/settings_css.tpl.php:78
msgid "This will drop the unused CSS on each page from the combined file."
msgstr "Isso removerá o CSS não usado em cada página do arquivo combinado."

#: tpl/page_optm/entry.tpl.php:9 tpl/page_optm/settings_html.tpl.php:9
msgid "HTML Settings"
msgstr "Configurações de HTML"

#: tpl/inc/in_upgrading.php:6
msgid "LiteSpeed cache plugin upgraded. Please refresh the page to complete the configuration data upgrade."
msgstr "O plugin de cache do LiteSpeed foi atualizado. Atualize a página para concluir a atualização dos dados de configuração."

#: tpl/general/settings_tuning.tpl.php:52
msgid "Listed IPs will be considered as Guest Mode visitors."
msgstr "Os IPs listados serão considerados como visitantes no modo visitante."

#: tpl/general/settings_tuning.tpl.php:30
msgid "Listed User Agents will be considered as Guest Mode visitors."
msgstr "Os agentes de usuário listados serão considerados como visitantes no modo visitante."

#: tpl/general/settings.tpl.php:55
msgid "Your %1$1s quota on %2$2s will still be in use."
msgstr "Sua cota de %1$1s em %2$2s ainda estará em uso."

#: tpl/general/settings_inc.guest.tpl.php:18
msgid "This option can help to correct the cache vary for certain advanced mobile or tablet visitors."
msgstr "Esta opção pode ajudar a corrigir a variação de cache para certos visitantes avançados de dispositivos móveis ou tablets."

#: tpl/general/settings_inc.guest.tpl.php:17
msgid "Guest Mode provides an always cacheable landing page for an automated guest's first time visit, and then attempts to update cache varies via AJAX."
msgstr "O modo visitante fornece uma página de destino sempre passível de ser armazenada em cache para a primeira visita automatizada de um visitante e, em seguida, tenta atualizar o cache de forma variada por meio do AJAX."

#: tpl/general/settings.tpl.php:95
msgid "Please make sure this IP is the correct one for visiting your site."
msgstr "Certifique-se de que este IP seja o correto para visitar o seu site."

#: tpl/general/settings.tpl.php:94
msgid "the auto-detected IP may not be accurate if you have an additional outgoing IP set, or you have multiple IPs configured on your server."
msgstr "o IP detectado automaticamente pode não ser preciso se você tiver um IP de saída adicional definido ou múltiplos IPs configurados no seu servidor."

#: tpl/general/settings.tpl.php:77
msgid "You need to turn %s on and finish all WebP generation to get maximum result."
msgstr "Você precisa ativar %s e concluir toda a geração de WebP para obter o resultado máximo."

#: tpl/general/settings.tpl.php:70
msgid "You need to turn %s on to get maximum result."
msgstr "Você precisa ativar %s para obter o resultado máximo."

#: tpl/general/settings.tpl.php:39
msgid "This option enables maximum optimization for Guest Mode visitors."
msgstr "Esta opção permite a otimização máxima para visitantes no modo Visitante."

#: tpl/dash/dashboard.tpl.php:49 tpl/dash/dashboard.tpl.php:77
#: tpl/dash/dashboard.tpl.php:519 tpl/dash/dashboard.tpl.php:606
#: tpl/dash/dashboard.tpl.php:634 tpl/dash/dashboard.tpl.php:671
#: tpl/dash/dashboard.tpl.php:708 tpl/dash/dashboard.tpl.php:745
#: tpl/dash/dashboard.tpl.php:782 tpl/dash/dashboard.tpl.php:833
msgid "More"
msgstr "Mais"

#: tpl/dash/dashboard.tpl.php:302
msgid "Remaining Daily Quota"
msgstr "Cota diária restante"

#: tpl/crawler/summary.tpl.php:209
msgid "Successfully Crawled"
msgstr "Rastreado"

#: tpl/crawler/summary.tpl.php:208
msgid "Already Cached"
msgstr "Já armazenado em cache"

#: tpl/crawler/settings.tpl.php:53
msgid "The crawler will use your XML sitemap or sitemap index. Enter the full URL to your sitemap here."
msgstr "O rastreador usará seu sitemap XML ou índice de sitemap. Digite o URL completo do seu sitemap aqui."

#: tpl/cdn/cf.tpl.php:45
msgid "Optional when API token used."
msgstr "Opcional quando um token de API é usado."

#: tpl/cdn/cf.tpl.php:35
msgid "Recommended to generate the token from Cloudflare API token template \"WordPress\"."
msgstr "Recomendado para gerar o token a partir do modelo de token da API do Cloudflare \"WordPress\"."

#: tpl/cdn/cf.tpl.php:29
msgid "Global API Key / API Token"
msgstr "Chave de API global / Token de API"

#: tpl/cdn/other.tpl.php:39
msgid "NOTE: QUIC.cloud CDN and Cloudflare do not use CDN Mapping. If you are are only using QUIC.cloud or Cloudflare, leave this setting %1$s."
msgstr "OBSERVAÇÃO: O CDN do QUIC.cloud e o Cloudflare não usam o mapeamento de CDN. Se você estiver usando apenas QUIC.cloud ou Cloudflare, deixe essa configuração como %1$s."

#: tpl/cdn/other.tpl.php:34
msgid "Turn this setting %1$s if you are using a traditional Content Delivery Network (CDN) or a subdomain for static content with QUIC.cloud CDN."
msgstr "Ative esta configuração %1$s se você estiver usando uma Rede de Distribuição de Conteúdo (CDN) tradicional ou um subdomínio para conteúdo estático com o CDN do QUIC.cloud."

#: tpl/cache/settings_inc.object.tpl.php:47
msgid "Use external object cache functionality."
msgstr "Use a funcionalidade de cache de objetos externos."

#: tpl/cache/settings_inc.cache_mobile.tpl.php:24
msgid "Serve a separate cache copy for mobile visitors."
msgstr "Fornece uma cópia de cache separada para visitantes móveis."

#: thirdparty/woocommerce.content.tpl.php:25
msgid "By default, the My Account, Checkout, and Cart pages are automatically excluded from caching. Misconfiguration of page associations in WooCommerce settings may cause some pages to be erroneously excluded."
msgstr "Por padrão, as páginas \"Minha conta\", \"Finalização de compra\" e \"Carrinho\" são automaticamente excluídas do cache. Uma má configuração das associações de páginas nas configurações do WooCommerce pode fazer com que algumas páginas sejam erroneamente excluídas."

#: src/purge.cls.php:272
msgid "Cleaned all Unique CSS files."
msgstr "Todos os arquivos CSS únicos foram limpos."

#: src/lang.cls.php:202
msgid "Add Missing Sizes"
msgstr "Adicionar tamanhos ausentes"

#: src/lang.cls.php:177
msgid "Optimize for Guests Only"
msgstr "Otimizar apenas para visitantes"

#: src/lang.cls.php:173
msgid "Guest Mode JS Excludes"
msgstr "Exclusões de JS no modo visitante"

#: src/lang.cls.php:153
msgid "CCSS Per URL"
msgstr "CCSS por URL"

#: src/lang.cls.php:150
msgid "HTML Lazy Load Selectors"
msgstr "Seletores de carregamento tardio HTML"

#: src/lang.cls.php:145
msgid "UCSS URI Excludes"
msgstr "Exclusões de URI UCSS"

#: src/lang.cls.php:142
msgid "UCSS Inline"
msgstr "UCSS embutido"

#: src/lang.cls.php:102
msgid "Guest Optimization"
msgstr "Otimização de visitantes"

#: src/lang.cls.php:101
msgid "Guest Mode"
msgstr "Modo visitante"

#: src/lang.cls.php:88
msgid "Guest Mode IPs"
msgstr "IPs do modo visitante"

#: src/lang.cls.php:87
msgid "Guest Mode User Agents"
msgstr "Agentes de usuário no modo visitante"

#: src/error.cls.php:131
msgid "Online node needs to be redetected."
msgstr "O nó on-line precisa ser detectado novamente."

#: src/error.cls.php:127
msgid "The current server is under heavy load."
msgstr "O servidor atual está sobrecarregado."

#: src/doc.cls.php:71
msgid "Please see %s for more details."
msgstr "Consulte %s para mais detalhes."

#: src/doc.cls.php:55
msgid "This setting will regenerate crawler list and clear the disabled list!"
msgstr "Esta configuração irá regenerar a lista do rastreadores e limpar a lista de desativados!"

#: src/gui.cls.php:82
msgid "%1$s %2$s files left in queue"
msgstr "%1$s %2$s arquivos restantes na fila"

#: src/crawler.cls.php:144
msgid "Crawler disabled list is cleared! All crawlers are set to active! "
msgstr "A lista de rastreadores desativados foi limpa! Todos os rastreadores estão ativos! "

#: src/cloud.cls.php:1474
msgid "Redetected node"
msgstr "Nó redetectado"

#: src/cloud.cls.php:1024
msgid "No available Cloud Node after checked server load."
msgstr "Nenhum nó da nuvem disponível após verificar a carga do servidor."

#: src/lang.cls.php:158
msgid "Localization Files"
msgstr "Arquivos de localização"

#: cli/purge.cls.php:231
msgid "Purged!"
msgstr "Limpo!"

#: tpl/page_optm/settings_localization.tpl.php:121
msgid "Resources listed here will be copied and replaced with local URLs."
msgstr "Os recursos listados aqui serão copiados e substituídos por URLs locais."

#: tpl/toolbox/beta_test.tpl.php:34
msgid "Use latest GitHub Master commit"
msgstr "Use o commit mais recente do Master GitHub"

#: tpl/toolbox/beta_test.tpl.php:32
msgid "Use latest GitHub Dev commit"
msgstr "Use o commit mais recente do GitHub Dev"

#: src/crawler-map.cls.php:371
msgid "No valid sitemap parsed for crawler."
msgstr "Nenhum sitemap válido analisado pelo rastreador."

#: src/lang.cls.php:140
msgid "CSS Combine External and Inline"
msgstr "Combinar CSS externo e embutido"

#: tpl/page_optm/settings_css.tpl.php:196
msgid "Include external CSS and inline CSS in combined file when %1$s is also enabled. This option helps maintain the priorities of CSS, which should minimize potential errors caused by CSS Combine."
msgstr "Incluir CSS externo e CSS embutido no arquivo combinado quando %1$s também estiver ativado. Esta opção ajuda a manter as prioridades do CSS, o que deve minimizar possíveis erros causados pela combinação de CSS."

#: tpl/page_optm/settings_css.tpl.php:41
msgid "Minify CSS files and inline CSS code."
msgstr "Minificar arquivos CSS e código CSS embutido."

#: tpl/cache/settings-excludes.tpl.php:32
#: tpl/page_optm/settings_tuning.tpl.php:62
#: tpl/page_optm/settings_tuning.tpl.php:83
#: tpl/page_optm/settings_tuning_css.tpl.php:28
#: tpl/page_optm/settings_tuning_css.tpl.php:68
#: tpl/page_optm/settings_tuning_css.tpl.php:144
msgid "Predefined list will also be combined w/ the above settings"
msgstr "A lista predefinida também será combinada com as configurações acima"

#: tpl/page_optm/entry.tpl.php:13
msgid "Localization"
msgstr "Localização"

#: tpl/page_optm/settings_js.tpl.php:58
msgid "Include external JS and inline JS in combined file when %1$s is also enabled. This option helps maintain the priorities of JS execution, which should minimize potential errors caused by JS Combine."
msgstr "Inclua JS externo e JS embutido no arquivo combinado quando %1$s também estiver ativado. Esta opção ajuda a manter as prioridades de execução de JS, o que deve minimizar possíveis erros causados pela combinação de JS."

#: tpl/page_optm/settings_js.tpl.php:39
msgid "Combine all local JS files into a single file."
msgstr "Combinar todos os arquivos JS locais em um único arquivo."

#: tpl/page_optm/settings_tuning.tpl.php:76
msgid "Listed JS files or inline JS code will not be deferred or delayed."
msgstr "Os arquivos JS listados ou o código JS incorporado não serão adiados ou atrasados."

#: src/data.upgrade.func.php:238
msgid "Click here to settings"
msgstr "Clique aqui para configurações"

#: src/data.upgrade.func.php:236
msgid "JS Defer"
msgstr "Adiar JS"

#: src/data.upgrade.func.php:231
msgid "LiteSpeed Cache upgraded successfully. NOTE: Due to changes in this version, the settings %1$s and %2$s have been turned OFF. Please turn them back on manually and verify that your site layout is correct, and you have no JS errors."
msgstr "O LiteSpeed Cache foi atualizado. OBSERVAÇÃO: Devido às alterações nesta versão, as configurações %1$s e %2$s foram DESATIVADAS. Ative-as manualmente e verifique se o layout do seu site está correto e se não há erros de JavaScript."

#: src/lang.cls.php:148
msgid "JS Combine External and Inline"
msgstr "Combinar JS externo e embutido"

#: src/admin-display.cls.php:518 tpl/banner/new_version.php:114
#: tpl/banner/score.php:140 tpl/banner/slack.php:49
msgid "Dismiss"
msgstr "Dispensar"

#: tpl/cache/settings-esi.tpl.php:103
msgid "The latest data file is"
msgstr "O arquivo de dados mais recente é"

#: tpl/cache/settings-esi.tpl.php:102
msgid "The list will be merged with the predefined nonces in your local data file."
msgstr "A lista será mesclada com os nonces predefinidos em seu arquivo de dados local."

#: tpl/page_optm/settings_css.tpl.php:55
msgid "Combine CSS files and inline CSS code."
msgstr "Combinar arquivos CSS e código CSS embutido."

#: tpl/page_optm/settings_js.tpl.php:25
msgid "Minify JS files and inline JS codes."
msgstr "Minificar arquivos JS e códigos JS embutidos."

#: tpl/page_optm/settings_tuning.tpl.php:54
msgid "Listed JS files or inline JS code will not be minified/combined."
msgstr "Os arquivos JS listados ou o código JS embutido não serão minificados/combinados."

#: tpl/page_optm/settings_tuning_css.tpl.php:21
msgid "Listed CSS files or inline CSS code will not be minified/combined."
msgstr "Os arquivos CSS listados ou o código CSS embutido não serão minificados/combinados."

#: src/admin-display.cls.php:1034
msgid "This setting is overwritten by the Network setting"
msgstr "Esta configuração é substituída pela configuração de rede"

#: src/lang.cls.php:191
msgid "LQIP Excludes"
msgstr "Exclusões de LQIP"

#: tpl/page_optm/settings_media_exc.tpl.php:123
msgid "These images will not generate LQIP."
msgstr "Essas imagens não irão gerar um LQIP (Marcador de Imagem de Baixa Qualidade)."

#: tpl/toolbox/import_export.tpl.php:53
msgid "Are you sure you want to reset all settings back to the default settings?"
msgstr "Tem certeza de que deseja redefinir todas as configurações para as configurações padrão?"

#: tpl/page_optm/settings_html.tpl.php:180
msgid "This option will remove all %s tags from HTML."
msgstr "Esta opção irá remover todas as tags %s do HTML."

#: tpl/general/online.tpl.php:24
msgid "Are you sure you want to clear all cloud nodes?"
msgstr "Tem certeza de que deseja limpar todos os nós na nuvem?"

#: src/lang.cls.php:175 tpl/presets/standard.tpl.php:46
msgid "Remove Noscript Tags"
msgstr "Remover tags Noscript"

#: src/error.cls.php:119
msgid "The site is not registered on QUIC.cloud."
msgstr "O site não está cadastrado no QUIC.cloud."

#: src/error.cls.php:48
msgid "Click here to change."
msgstr "Clique aqui para alterar."

#: src/error.cls.php:58 tpl/crawler/settings.tpl.php:106
#: tpl/crawler/settings.tpl.php:115 tpl/crawler/summary.tpl.php:185
msgid "Click here to set."
msgstr "Clique aqui para definir."

#: src/lang.cls.php:157
msgid "Localize Resources"
msgstr "Localizar recursos"

#: tpl/cache/settings_inc.browser.tpl.php:26
msgid "Setting Up Custom Headers"
msgstr "Configurando cabeçalhos personalizados"

#: tpl/toolbox/purge.tpl.php:81
msgid "This will delete all localized resources"
msgstr "Isso irá excluir todos os recursos localizados"

#: src/gui.cls.php:550 src/gui.cls.php:712 tpl/toolbox/purge.tpl.php:80
msgid "Localized Resources"
msgstr "Recursos localizados"

#: tpl/page_optm/settings_localization.tpl.php:126
msgid "Comments are supported. Start a line with a %s to turn it into a comment line."
msgstr "Comentários são suportados. Comece uma linha com um %s para transformá-la em uma linha de comentário."

#: tpl/page_optm/settings_localization.tpl.php:122
msgid "HTTPS sources only."
msgstr "Apenas fontes HTTPS."

#: tpl/page_optm/settings_localization.tpl.php:95
msgid "Localize external resources."
msgstr "Localizar recursos externos."

#: tpl/page_optm/settings_localization.tpl.php:18
msgid "Localization Settings"
msgstr "Configurações de localização"

#: tpl/page_optm/settings_css.tpl.php:77
msgid "Use QUIC.cloud online service to generate unique CSS."
msgstr "Use o serviço on-line do QUIC.cloud para gerar CSS único."

#: src/lang.cls.php:141
msgid "Generate UCSS"
msgstr "Gerar UCSS"

#: tpl/dash/dashboard.tpl.php:670 tpl/toolbox/purge.tpl.php:71
msgid "Unique CSS"
msgstr "CSS único"

#: tpl/toolbox/purge.tpl.php:108
msgid "Purge the cache entries created by this plugin except for Critical CSS & Unique CSS & LQIP caches"
msgstr "Limpar as entradas de cache criadas por este plugin, exceto os caches de CSS crítico, CSS único e LQIP (Marcador de Imagem de Baixa Qualidade)"

#: tpl/toolbox/report.tpl.php:50
msgid "LiteSpeed Report"
msgstr "Relatório do LiteSpeed"

#: tpl/img_optm/summary.tpl.php:219
msgid "Image Thumbnail Group Sizes"
msgstr "Tamanhos dos grupos de miniaturas de imagens"

#. translators: %s: LiteSpeed Web Server version
#: tpl/cache/settings_inc.cache_dropquery.tpl.php:27
msgid "Ignore certain query strings when caching. (LSWS %s required)"
msgstr "Ignorar determinadas strings de consulta ao armazenar em cache. (Requer LSWS %s)"

#: tpl/cache/settings-purge.tpl.php:116
msgid "For URLs with wildcards, there may be a delay in initiating scheduled purge."
msgstr "Para URLs com curingas, pode haver um atraso na inicialização da limpeza agendada."

#: tpl/cache/settings-purge.tpl.php:92
msgid "By design, this option may serve stale content. Do not enable this option, if that is not OK with you."
msgstr "Por padrão, esta opção pode fornecer conteúdo obsoleto. Não ative esta opção se isso não for ACEITÁVEL para você."

#: src/lang.cls.php:128
msgid "Serve Stale"
msgstr "Fornecer conteúdo obsoleto"

#: src/admin-display.cls.php:1032
msgid "This setting is overwritten by the primary site setting"
msgstr "Esta configuração é substituída pela configuração do site principal"

#: src/img-optm.cls.php:1179
msgid "One or more pulled images does not match with the notified image md5"
msgstr "Uma ou mais imagens recuperadas não correspondem à imagem notificada com o md5"

#: src/img-optm.cls.php:1072 src/img-optm.cls.php:1098
msgid "Some optimized image file(s) has expired and was cleared."
msgstr "Alguns arquivo(s) de imagem otimizada expiraram e foram apagados."

#: src/error.cls.php:88
msgid "You have too many requested images, please try again in a few minutes."
msgstr "Você tem muitas imagens solicitadas. Tente novamente em alguns minutos."

#: src/img-optm.cls.php:1115
msgid "Pulled WebP image md5 does not match the notified WebP image md5."
msgstr "A imagem WebP recuperada não corresponde ao md5 da imagem WebP notificada."

#: src/img-optm.cls.php:1144
msgid "Pulled AVIF image md5 does not match the notified AVIF image md5."
msgstr "O MD5 da imagem AVIF extraída, não corresponde ao MD5 da imagem AVIF notificada."

#: tpl/inc/admin_footer.php:14
msgid "Read LiteSpeed Documentation"
msgstr "Leia a documentação do LiteSpeed"

#: src/error.cls.php:109
msgid "There is proceeding queue not pulled yet. Queue info: %s."
msgstr "Há uma fila de processamento que ainda não foi concluída. Informações da fila: %s."

#: tpl/page_optm/settings_localization.tpl.php:80
msgid "Specify how long, in seconds, Gravatar files are cached."
msgstr "Especificar por quantos segundos os arquivos do Gravatar serão armazenados em cache."

#: src/img-optm.cls.php:604
msgid "Cleared %1$s invalid images."
msgstr "Foram limpas %1$s imagens inválidas."

#: tpl/general/entry.tpl.php:23
msgid "LiteSpeed Cache General Settings"
msgstr "Configurações gerais do LiteSpeed Cache"

#: tpl/toolbox/purge.tpl.php:99
msgid "This will delete all cached Gravatar files"
msgstr "Isso irá excluir todos os arquivos Gravatar em cache"

#: tpl/toolbox/settings-debug.tpl.php:143
msgid "Prevent any debug log of listed pages."
msgstr "Impedir qualquer registro de depuração das páginas listadas."

#: tpl/toolbox/settings-debug.tpl.php:129
msgid "Only log listed pages."
msgstr "Registrar apenas as páginas listadas."

#: tpl/toolbox/settings-debug.tpl.php:101
msgid "Specify the maximum size of the log file."
msgstr "Especificar o tamanho máximo do arquivo de registro."

#: tpl/toolbox/settings-debug.tpl.php:52
msgid "To prevent filling up the disk, this setting should be OFF when everything is working."
msgstr "Para evitar encher o disco, esta configuração deve estar DESATIVADA quando tudo estiver funcionando."

#: tpl/toolbox/beta_test.tpl.php:52
msgid "Press the %s button to stop beta testing and go back to the current release from the WordPress Plugin Directory."
msgstr "Pressione o botão %s para interromper os testes beta e voltar para a versão atual no diretório de plugins do WordPress."

#: tpl/toolbox/beta_test.tpl.php:36 tpl/toolbox/beta_test.tpl.php:52
msgid "Use latest WordPress release version"
msgstr "Use a versão mais recente do WordPress"

#: tpl/toolbox/beta_test.tpl.php:36
msgid "OR"
msgstr "OU"

#: tpl/toolbox/beta_test.tpl.php:27
msgid "Use this section to switch plugin versions. To beta test a GitHub commit, enter the commit URL in the field below."
msgstr "Use esta seção para alternar entre as versões do plugin. Para testar uma versão beta de um commit do GitHub, digite o URL do commit no campo abaixo."

#: tpl/toolbox/import_export.tpl.php:54
msgid "Reset Settings"
msgstr "Redefinir configurações"

#: tpl/toolbox/entry.tpl.php:33
msgid "LiteSpeed Cache Toolbox"
msgstr "Caixa de ferramentas do LiteSpeed Cache"

#: tpl/toolbox/entry.tpl.php:26
msgid "Beta Test"
msgstr "Teste beta"

#: tpl/toolbox/entry.tpl.php:25
msgid "Log View"
msgstr "Visualização de registros"

#: tpl/toolbox/entry.tpl.php:24 tpl/toolbox/settings-debug.tpl.php:24
msgid "Debug Settings"
msgstr "Configurações de depuração"

#: tpl/toolbox/heartbeat.tpl.php:95
msgid "Turn ON to control heartbeat in backend editor."
msgstr "ATIVAR para controlar o monitoramento de atividade (heartbeat) no editor do painel."

#: tpl/toolbox/heartbeat.tpl.php:79 tpl/toolbox/heartbeat.tpl.php:109
msgid "WordPress valid interval is %s seconds"
msgstr "O intervalo válido do WordPress é de %s segundos"

#: tpl/toolbox/heartbeat.tpl.php:65
msgid "Turn ON to control heartbeat on backend."
msgstr "ATIVAR para controlar o monitoramento de atividade (heartbeat) no painel."

#: tpl/toolbox/heartbeat.tpl.php:50 tpl/toolbox/heartbeat.tpl.php:80
#: tpl/toolbox/heartbeat.tpl.php:110
msgid "Set to %1$s to forbid heartbeat on %2$s."
msgstr "Defina como %1$s para impedir o monitoramento de atividade (heartbeat) em %2$s."

#: tpl/toolbox/heartbeat.tpl.php:49
msgid "WordPress valid interval is %s seconds."
msgstr "O intervalo válido no WordPress é de %s segundos."

#: tpl/toolbox/heartbeat.tpl.php:48 tpl/toolbox/heartbeat.tpl.php:78
#: tpl/toolbox/heartbeat.tpl.php:108
msgid "Specify the %s heartbeat interval in seconds."
msgstr "Especifique o intervalo do monitoramento de atividade (heartbeat) de %s em segundos."

#: tpl/toolbox/heartbeat.tpl.php:35
msgid "Turn ON to control heartbeat on frontend."
msgstr "ATIVAR para controlar o monitoramento de atividade (heartbeat) na interface."

#: tpl/toolbox/heartbeat.tpl.php:16
msgid "Disable WordPress interval heartbeat to reduce server load."
msgstr "Desative o intervalo do monitoramento de atividade (heartbeat) do WordPress para reduzir a carga no servidor."

#: tpl/toolbox/heartbeat.tpl.php:10
msgid "Heartbeat Control"
msgstr "Controle de monitoramento de atividade (heartbeat)"

#: tpl/toolbox/report.tpl.php:114
msgid "provide more information here to assist the LiteSpeed team with debugging."
msgstr "forneça mais informações aqui para auxiliar a equipe do LiteSpeed na depuração."

#: tpl/toolbox/report.tpl.php:113
msgid "Optional"
msgstr "Opcional"

#: tpl/toolbox/report.tpl.php:95 tpl/toolbox/report.tpl.php:97
msgid "Generate Link for Current User"
msgstr "Gerar link para o usuário atual"

#: tpl/toolbox/report.tpl.php:91
msgid "Passwordless Link"
msgstr "Link sem senha"

#: tpl/toolbox/report.tpl.php:67
msgid "System Information"
msgstr "Informações do sistema"

#: tpl/toolbox/report.tpl.php:44
msgid "Go to plugins list"
msgstr "Ir para a lista de plugins"

#: tpl/toolbox/report.tpl.php:43
msgid "Install DoLogin Security"
msgstr "Instalar o DoLogin Security"

#: tpl/general/settings.tpl.php:93
msgid "Check my public IP from"
msgstr "Verifique meu endereço IP público em"

#: tpl/general/settings.tpl.php:93
msgid "Your server IP"
msgstr "Seu IP do servidor"

#: tpl/general/settings.tpl.php:92
msgid "Enter this site's IP address to allow cloud services directly call IP instead of domain name. This eliminates the overhead of DNS and CDN lookups."
msgstr "Digite o endereço IP deste site para permitir que os serviços em nuvem chamem diretamente o IP em vez do nome de domínio. Isso elimina a sobrecarga de consultas DNS e CDN."

#: tpl/crawler/settings.tpl.php:25
msgid "This will enable crawler cron."
msgstr "Isso ativará o cron do rastreador."

#: tpl/crawler/settings.tpl.php:11
msgid "Crawler General Settings"
msgstr "Configurações gerais do rastreador"

#: tpl/crawler/blacklist.tpl.php:49
msgid "Remove from Blocklist"
msgstr "Remover da lista de bloqueio"

#: tpl/crawler/blacklist.tpl.php:17
msgid "Empty blocklist"
msgstr "Esvaziar lista de bloqueios"

#: tpl/crawler/blacklist.tpl.php:16
msgid "Are you sure to delete all existing blocklist items?"
msgstr "Tem certeza de que deseja excluir todos os itens existentes na lista de bloqueio?"

#: tpl/crawler/blacklist.tpl.php:66 tpl/crawler/map.tpl.php:96
msgid "Blocklisted due to not cacheable"
msgstr "Lista de bloqueio por não ser armazenável em cache"

#: tpl/crawler/map.tpl.php:83
msgid "Add to Blocklist"
msgstr "Adicionar à lista de bloqueio"

#: tpl/crawler/blacklist.tpl.php:36 tpl/crawler/map.tpl.php:69
msgid "Operation"
msgstr "Operação"

#: tpl/crawler/map.tpl.php:40
msgid "Sitemap Total"
msgstr "Total de sitemaps"

#: tpl/crawler/map.tpl.php:36
msgid "Sitemap List"
msgstr "Lista de sitemaps"

#: tpl/crawler/map.tpl.php:23
msgid "Refresh Crawler Map"
msgstr "Atualizar mapa do rastreador"

#: tpl/crawler/map.tpl.php:19
msgid "Clean Crawler Map"
msgstr "Limpar mapa do rastreador"

#: tpl/crawler/blacklist.tpl.php:22 tpl/crawler/entry.tpl.php:10
msgid "Blocklist"
msgstr "Lista de bloqueio"

#: tpl/crawler/entry.tpl.php:9
msgid "Map"
msgstr "Mapa"

#: tpl/crawler/entry.tpl.php:8
msgid "Summary"
msgstr "Resumo"

#: tpl/crawler/summary.tpl.php:218
msgid "&nbsp;If both the cron and a manual run start at similar times, the first to be started will take precedence."
msgstr "&nbsp;Se tanto o cron quanto uma execução manual começarem aproximadamente ao mesmo tempo, o primeiro a ser iniciado terá prioridade."

#: tpl/crawler/summary.tpl.php:217
msgid "Crawlers cannot run concurrently."
msgstr "Os rastreadores não podem ser executados simultaneamente."

#: tpl/crawler/map.tpl.php:52 tpl/crawler/map.tpl.php:95
msgid "Cache Miss"
msgstr "Não encontrado no cache"

#: tpl/crawler/map.tpl.php:51 tpl/crawler/map.tpl.php:94
msgid "Cache Hit"
msgstr "Encontrado no cache"

#: tpl/crawler/summary.tpl.php:207
msgid "Waiting to be Crawled"
msgstr "Aguardando ser rastreado"

#: tpl/crawler/blacklist.tpl.php:67 tpl/crawler/map.tpl.php:53
#: tpl/crawler/map.tpl.php:97 tpl/crawler/summary.tpl.php:175
#: tpl/crawler/summary.tpl.php:210
msgid "Blocklisted"
msgstr "Lista de bloqueio"

#: tpl/crawler/summary.tpl.php:174
msgid "Miss"
msgstr "Não encontrado"

#: tpl/crawler/summary.tpl.php:173
msgid "Hit"
msgstr "Encontrado"

#: tpl/crawler/summary.tpl.php:172
msgid "Waiting"
msgstr "Aguardando"

#: tpl/crawler/summary.tpl.php:140
msgid "Running"
msgstr "Executando"

#: tpl/crawler/settings.tpl.php:144
msgid "Use %1$s in %2$s to indicate this cookie has not been set."
msgstr "Use %1$s em %2$s para indicar que esse cookie não foi definido."

#: src/admin-display.cls.php:218
msgid "Add new cookie to simulate"
msgstr "Adicionar novo cookie para simular"

#: src/admin-display.cls.php:217
msgid "Remove cookie simulation"
msgstr "Remover simulação de cookies"

#. translators: %s: Current mobile agents in htaccess
#: tpl/cache/settings_inc.cache_mobile.tpl.php:51
msgid "Htaccess rule is: %s"
msgstr "A regra .htaccess é: %s"

#. translators: %s: LiteSpeed Cache menu label
#: tpl/cache/more_settings_tip.tpl.php:27
msgid "More settings available under %s menu"
msgstr "Mais configurações disponíveis no menu %s"

#: tpl/cache/settings_inc.browser.tpl.php:63
msgid "The amount of time, in seconds, that files will be stored in browser cache before expiring."
msgstr "O tempo, em segundos, que os arquivos serão armazenados no cache do navegador antes de expirarem."

#: tpl/cache/settings_inc.browser.tpl.php:25
msgid "OpenLiteSpeed users please check this"
msgstr "Usuários do OpenLiteSpeed, verifiquem isso"

#: tpl/cache/settings_inc.browser.tpl.php:17
msgid "Browser Cache Settings"
msgstr "Configurações de cache do navegador"

#: tpl/cache/settings-cache.tpl.php:158
msgid "Paths containing these strings will be forced to public cached regardless of no-cacheable settings."
msgstr "Caminhos de URI contendo essas strings serão forçados a serem armazenados em cache como públicos, independentemente das configurações de não armazenamento em cache."

#: tpl/cache/settings-cache.tpl.php:49
msgid "With QUIC.cloud CDN enabled, you may still be seeing cache headers from your local server."
msgstr "Com o CDN QUIC.cloud ativado, você ainda pode estar vendo cabeçalhos de cache do seu servidor local."

#: tpl/cache/settings-esi.tpl.php:114
msgid "An optional second parameter may be used to specify cache control. Use a space to separate"
msgstr "Um segundo parâmetro opcional pode ser usado para especificar o controle de cache. Use um espaço para separar"

#: tpl/cache/settings-esi.tpl.php:112
msgid "The above nonces will be converted to ESI automatically."
msgstr "Os nonces acima serão convertidos automaticamente em ESI."

#: tpl/cache/entry.tpl.php:25 tpl/cache/entry_network.tpl.php:20
msgid "Browser"
msgstr "Navegador"

#: tpl/cache/entry.tpl.php:24 tpl/cache/entry_network.tpl.php:19
msgid "Object"
msgstr "Objeto"

#. translators: %1$s: Object cache name, %2$s: Port number
#: tpl/cache/settings_inc.object.tpl.php:128
#: tpl/cache/settings_inc.object.tpl.php:137
msgid "Default port for %1$s is %2$s."
msgstr "A porta padrão para %1$s é %2$s."

#: tpl/cache/settings_inc.object.tpl.php:33
msgid "Object Cache Settings"
msgstr "Configurações de cache de objetos"

#: tpl/cache/settings-ttl.tpl.php:111
msgid "Specify an HTTP status code and the number of seconds to cache that page, separated by a space."
msgstr "Especifique um código de status HTTP e o número de segundos para armazenar em cache esta página, separados por um espaço."

#: tpl/cache/settings-ttl.tpl.php:59
msgid "Specify how long, in seconds, the front page is cached."
msgstr "Especifique por quanto tempo, em segundos, a página inicial é armazenada em cache."

#: tpl/cache/entry.tpl.php:17 tpl/cache/settings-ttl.tpl.php:15
msgid "TTL"
msgstr "TTL"

#: tpl/cache/settings-purge.tpl.php:86
msgid "If ON, the stale copy of a cached page will be shown to visitors until a new cache copy is available. Reduces the server load for following visits. If OFF, the page will be dynamically generated while visitors wait."
msgstr "Se ATIVADO, uma cópia obsoleta de uma página em cache será mostrada aos visitantes até que uma nova cópia em cache esteja disponível. Isso reduz a carga do servidor para visitas subsequentes. Se DESATIVADO, a página será gerada dinamicamente enquanto os visitantes esperam."

#: tpl/page_optm/settings_css.tpl.php:348
msgid "Swap"
msgstr "Trocar"

#: tpl/page_optm/settings_css.tpl.php:347
msgid "Set this to append %1$s to all %2$s rules before caching CSS to specify how fonts should be displayed while being downloaded."
msgstr "Defina isso para anexar %1$s a todas as regras %2$s antes de armazenar em cache o CSS, especificando como as fontes devem ser exibidas ao serem baixadas."

#: tpl/page_optm/settings_localization.tpl.php:58
msgid "Avatar list in queue waiting for update"
msgstr "Lista de avatares na fila aguardando atualização"

#: tpl/page_optm/settings_localization.tpl.php:45
msgid "Refresh Gravatar cache by cron."
msgstr "Atualizar o cache do Gravatar através do cron."

#: tpl/page_optm/settings_localization.tpl.php:32
msgid "Accelerates the speed by caching Gravatar (Globally Recognized Avatars)."
msgstr "Acelera a velocidade ao armazenar em cache o Gravatar (Avatares Reconhecidos Globalmente)."

#: tpl/page_optm/settings_localization.tpl.php:31
msgid "Store Gravatar locally."
msgstr "Armazenar o Gravatar localmente."

#: tpl/page_optm/settings_localization.tpl.php:13
msgid "Failed to create Avatar table. Please follow <a %s>Table Creation guidance from LiteSpeed Wiki</a> to finish setup."
msgstr "Falha ao criar a tabela Avatar. Siga as <a %s>Diretrizes de criação de tabela no Wiki do LiteSpeed</a> para concluir a configuração."

#: tpl/page_optm/settings_media.tpl.php:146
msgid "LQIP requests will not be sent for images where both width and height are smaller than these dimensions."
msgstr "Não serão enviadas solicitações de LQIP para imagens cuja largura e altura sejam ambas menores que essas dimensões."

#: tpl/page_optm/settings_media.tpl.php:144
msgid "pixels"
msgstr "pixels"

#: tpl/page_optm/settings_media.tpl.php:128
msgid "Larger number will generate higher resolution quality placeholder, but will result in larger files which will increase page size and consume more points."
msgstr "Um número maior gerará um marcador de posição de maior qualidade de resolução, mas resultará em arquivos maiores que aumentarão o tamanho da página e consumirão mais pontos."

#: tpl/page_optm/settings_media.tpl.php:127
msgid "Specify the quality when generating LQIP."
msgstr "Especificar a qualidade ao gerar o \"Marcador de Imagem de Baixa Qualidade\" (LQIP)."

#: tpl/page_optm/settings_media.tpl.php:113
msgid "Keep this off to use plain color placeholders."
msgstr "Mantenha isso desativado para usar marcadores de posição de cor sólida."

#: tpl/page_optm/settings_media.tpl.php:112
msgid "Use QUIC.cloud LQIP (Low Quality Image Placeholder) generator service for responsive image previews while loading."
msgstr "Use o serviço gerador de LQIP (Marcador de Posição de Imagem de Baixa Qualidade) do QUIC.cloud para pré-visualizações de imagens responsivas durante o carregamento."

#: tpl/page_optm/settings_media.tpl.php:97
msgid "Specify the responsive placeholder SVG color."
msgstr "Especifique a cor do marcador de posição responsivo em SVG."

#: tpl/page_optm/settings_media.tpl.php:83
msgid "Variables %s will be replaced with the configured background color."
msgstr "As variáveis %s serão substituídas pela cor de fundo configurada."

#: tpl/page_optm/settings_media.tpl.php:82
msgid "Variables %s will be replaced with the corresponding image properties."
msgstr "As variáveis %s serão substituídas pelas propriedades correspondentes da imagem."

#: tpl/page_optm/settings_media.tpl.php:81
msgid "It will be converted to a base64 SVG placeholder on-the-fly."
msgstr "Será convertido em um marcador de posição SVG em base64 sob demanda."

#: tpl/page_optm/settings_media.tpl.php:80
msgid "Specify an SVG to be used as a placeholder when generating locally."
msgstr "Especifique um arquivo SVG para ser usado como marcador de posição ao gerar localmente."

#: tpl/page_optm/settings_media_exc.tpl.php:109
msgid "Prevent any lazy load of listed pages."
msgstr "Impedir qualquer carregamento tardio das páginas listadas."

#: tpl/page_optm/settings_media_exc.tpl.php:95
msgid "Iframes having these parent class names will not be lazy loaded."
msgstr "Iframes que tenham esses nomes de classes principal não serão carregados tardiamente."

#: tpl/page_optm/settings_media_exc.tpl.php:80
msgid "Iframes containing these class names will not be lazy loaded."
msgstr "Iframes que contenham esses nomes de classes não serão carregados tardiamente."

#: tpl/page_optm/settings_media_exc.tpl.php:66
msgid "Images having these parent class names will not be lazy loaded."
msgstr "Imagens que tenham esses nomes de classes principal não serão carregadas tardiamente."

#: tpl/page_optm/entry.tpl.php:22
msgid "LiteSpeed Cache Page Optimization"
msgstr "Otimização de páginas do LiteSpeed Cache"

#: tpl/page_optm/entry.tpl.php:12 tpl/page_optm/settings_media_exc.tpl.php:8
msgid "Media Excludes"
msgstr "Exclusões de mídia"

#: tpl/page_optm/entry.tpl.php:7 tpl/page_optm/settings_css.tpl.php:25
msgid "CSS Settings"
msgstr "Configurações de CSS"

#: tpl/page_optm/settings_css.tpl.php:348
msgid "%s is recommended."
msgstr "%s é recomendado."

#: tpl/page_optm/settings_js.tpl.php:69
msgid "Deferred"
msgstr "Adiado"

#: tpl/page_optm/settings_css.tpl.php:345
msgid "Default"
msgstr "Padrão"

#: tpl/page_optm/settings_html.tpl.php:53
msgid "This can improve the page loading speed."
msgstr "Isso pode melhorar a velocidade de carregamento da página."

#: tpl/page_optm/settings_html.tpl.php:52
msgid "Automatically enable DNS prefetching for all URLs in the document, including images, CSS, JavaScript, and so forth."
msgstr "Ativar automaticamente o pré-carregamento de DNS para todos os URLs no documento, incluindo imagens, CSS, JavaScript e assim por diante."

#: tpl/banner/new_version_dev.tpl.php:30
msgid "New developer version %s is available now."
msgstr "A nova versão para desenvolvedores %s já está disponível."

#: tpl/banner/new_version_dev.tpl.php:22
msgid "New Developer Version Available!"
msgstr "Nova versão para desenvolvedores disponível!"

#: tpl/banner/cloud_news.tpl.php:51 tpl/banner/cloud_promo.tpl.php:73
msgid "Dismiss this notice"
msgstr "Dispensar esta notificação"

#: tpl/banner/cloud_promo.tpl.php:61
msgid "Tweet this"
msgstr "Tweetar isso"

#: tpl/banner/cloud_promo.tpl.php:45
msgid "Tweet preview"
msgstr "Pré-visualização doTtweet"

#: tpl/banner/cloud_promo.tpl.php:40
#: tpl/page_optm/settings_tuning_css.tpl.php:59
#: tpl/page_optm/settings_tuning_css.tpl.php:135
msgid "Learn more"
msgstr "Saber mais"

#: tpl/banner/cloud_promo.tpl.php:22
msgid "You just unlocked a promotion from QUIC.cloud!"
msgstr "Você acaba de desbloquear uma promoção do QUIC.cloud!"

#: tpl/page_optm/settings_media.tpl.php:264
msgid "The image compression quality setting of WordPress out of 100."
msgstr "A configuração de qualidade de compressão de imagem do WordPress em uma escala de 0 a 100."

#: tpl/img_optm/entry.tpl.php:9 tpl/img_optm/entry.tpl.php:15
#: tpl/img_optm/network_settings.tpl.php:10 tpl/img_optm/settings.tpl.php:11
msgid "Image Optimization Settings"
msgstr "Configurações de otimização de imagem"

#: tpl/img_optm/summary.tpl.php:360
msgid "Are you sure to destroy all optimized images?"
msgstr "Tem certeza de que deseja remover todas as imagens otimizadas?"

#: tpl/img_optm/summary.tpl.php:344
msgid "Use Optimized Files"
msgstr "Usar arquivos otimizados"

#: tpl/img_optm/summary.tpl.php:343
msgid "Switch back to using optimized images on your site"
msgstr "Voltar a usar imagens otimizadas em seu site"

#: tpl/img_optm/summary.tpl.php:340
msgid "Use Original Files"
msgstr "Usar arquivos originais"

#: tpl/img_optm/summary.tpl.php:339
msgid "Use original images (unoptimized) on your site"
msgstr "Use imagens originais (não otimizadas) em seu site"

#: tpl/img_optm/summary.tpl.php:334
msgid "You can quickly switch between using original (unoptimized versions) and optimized image files. It will affect all images on your website, both regular and webp versions if available."
msgstr "Você pode alternar rapidamente entre o uso de versões originais (não otimizadas) e arquivos de imagem otimizados. Isso afetará todas as imagens do seu site, tanto as versões regulares quanto as versões WebP, se estiverem disponíveis."

#: tpl/img_optm/summary.tpl.php:331
msgid "Optimization Tools"
msgstr "Ferramentas de otimização"

#: tpl/img_optm/summary.tpl.php:297
msgid "Rescan New Thumbnails"
msgstr "Verificar novamente novas miniaturas"

#: tpl/img_optm/summary.tpl.php:280
msgid "Congratulations, all gathered!"
msgstr "Parabéns, todos reunidos!"

#: tpl/img_optm/summary.tpl.php:285
msgid "What is an image group?"
msgstr "O que é um grupo de imagens?"

#: tpl/img_optm/summary.tpl.php:232
msgid "Delete all backups of the original images"
msgstr "Excluir todos os backups das imagens originais"

#: tpl/img_optm/summary.tpl.php:212
msgid "Calculate Backups Disk Space"
msgstr "Calcular espaço em disco para backups"

#: tpl/img_optm/summary.tpl.php:100
msgid "Optimization Status"
msgstr "Status de otimização"

#: tpl/img_optm/summary.tpl.php:61
msgid "Current limit is"
msgstr "O limite atual é"

#: tpl/img_optm/summary.tpl.php:60
msgid "To make sure our server can communicate with your server without any issues and everything works fine, for the few first requests the number of image groups allowed in a single request is limited."
msgstr "Para garantir que nosso servidor possa se comunicar com o seu servidor sem problemas e que tudo funcione bem, o número de grupos de imagens permitidos em uma única solicitação é limitado para as primeiras solicitações."

#: tpl/img_optm/summary.tpl.php:55
msgid "You can request a maximum of %s images at once."
msgstr "Você pode solicitar um máximo de %s imagens de uma só vez."

#: tpl/img_optm/summary.tpl.php:50
msgid "Optimize images with our QUIC.cloud server"
msgstr "Otimizar imagens com nosso servidor QUIC.cloud"

#: tpl/img_optm/summary.tpl.php:46 tpl/page_optm/settings_css.tpl.php:106
#: tpl/page_optm/settings_css.tpl.php:250
#: tpl/page_optm/settings_media.tpl.php:184
#: tpl/page_optm/settings_vpi.tpl.php:53
msgid "Current closest Cloud server is %s.&#10; Click to redetect."
msgstr "O servidor em nuvem mais próximo atual é %s.&#10; Clique para redetectar."

#: tpl/db_optm/settings.tpl.php:38
msgid "Revisions newer than this many days will be kept when cleaning revisions."
msgstr "Revisões mais recentes do que este número de dias serão mantidas ao limpar as revisões."

#: tpl/db_optm/settings.tpl.php:36
msgid "Day(s)"
msgstr "Dia(s)"

#: tpl/db_optm/settings.tpl.php:24
msgid "Specify the number of most recent revisions to keep when cleaning revisions."
msgstr "Especifique o número de revisões mais recentes a serem mantidas ao limpar as revisões."

#: tpl/db_optm/entry.tpl.php:18
msgid "LiteSpeed Cache Database Optimization"
msgstr "Otimização do banco de dados do LiteSpeed Cache"

#: tpl/db_optm/entry.tpl.php:11 tpl/db_optm/settings.tpl.php:11
msgid "DB Optimization Settings"
msgstr "Configurações de otimização do banco de dados"

#: tpl/db_optm/manage.tpl.php:181
msgid "Option Name"
msgstr "Nome da opção"

#: tpl/db_optm/manage.tpl.php:165
msgid "Database Summary"
msgstr "Resumo do banco de dados"

#: tpl/db_optm/manage.tpl.php:143
msgid "We are good. No table uses MyISAM engine."
msgstr "Estamos bem. Nenhuma tabela está usando o mecanismo MyISAM."

#: tpl/db_optm/manage.tpl.php:135
msgid "Convert to InnoDB"
msgstr "Converter para InnoDB"

#: tpl/db_optm/manage.tpl.php:120
msgid "Tool"
msgstr "Ferramenta"

#: tpl/db_optm/manage.tpl.php:119
msgid "Engine"
msgstr "Mecanismo"

#: tpl/db_optm/manage.tpl.php:118
msgid "Table"
msgstr "Tabela"

#: tpl/db_optm/manage.tpl.php:110
msgid "Database Table Engine Converter"
msgstr "Conversor de mecanismo de tabela de banco de dados"

#: tpl/db_optm/manage.tpl.php:57
msgid "Clean revisions older than %1$s day(s), excluding %2$s latest revisions"
msgstr "Limpar revisões com mais de %1$s dia(s), excluindo as %2$s revisões mais recentes"

#: tpl/dash/dashboard.tpl.php:84 tpl/dash/dashboard.tpl.php:789
msgid "Currently active crawler"
msgstr "Rastreador ativo atualmente"

#: tpl/dash/dashboard.tpl.php:81 tpl/dash/dashboard.tpl.php:786
msgid "Crawler(s)"
msgstr "Rastreador(es)"

#: tpl/crawler/map.tpl.php:68 tpl/dash/dashboard.tpl.php:76
#: tpl/dash/dashboard.tpl.php:781
msgid "Crawler Status"
msgstr "Status do rastreador"

#: tpl/dash/dashboard.tpl.php:654 tpl/dash/dashboard.tpl.php:691
#: tpl/dash/dashboard.tpl.php:728 tpl/dash/dashboard.tpl.php:765
msgid "Force cron"
msgstr "Forçar cron"

#: tpl/dash/dashboard.tpl.php:647 tpl/dash/dashboard.tpl.php:684
#: tpl/dash/dashboard.tpl.php:721 tpl/dash/dashboard.tpl.php:758
msgid "Requests in queue"
msgstr "Solicitações na fila"

#: tpl/dash/dashboard.tpl.php:642 tpl/dash/dashboard.tpl.php:679
#: tpl/dash/dashboard.tpl.php:716 tpl/dash/dashboard.tpl.php:753
msgid "Time to execute previous request"
msgstr "CSS único"

#: tpl/dash/dashboard.tpl.php:55 tpl/dash/dashboard.tpl.php:612
msgid "Private Cache"
msgstr "Cache privado"

#: tpl/dash/dashboard.tpl.php:54 tpl/dash/dashboard.tpl.php:611
msgid "Public Cache"
msgstr "Cache público"

#: tpl/dash/dashboard.tpl.php:48 tpl/dash/dashboard.tpl.php:605
msgid "Cache Status"
msgstr "Status do cache"

#: tpl/dash/dashboard.tpl.php:578
msgid "Last Pull"
msgstr "Última recuperação"

#: tpl/dash/dashboard.tpl.php:518 tpl/img_optm/entry.tpl.php:8
msgid "Image Optimization Summary"
msgstr "Resumo da otimização de imagens"

#: tpl/dash/dashboard.tpl.php:509
msgid "Refresh page score"
msgstr "Atualizar pontuação da página"

#: tpl/dash/dashboard.tpl.php:378 tpl/img_optm/summary.tpl.php:46
#: tpl/page_optm/settings_css.tpl.php:106
#: tpl/page_optm/settings_css.tpl.php:250
#: tpl/page_optm/settings_media.tpl.php:184
#: tpl/page_optm/settings_vpi.tpl.php:53
msgid "Are you sure you want to redetect the closest cloud server for this service?"
msgstr "Tem certeza de que deseja redetectar o servidor em nuvem mais próximo para este serviço?"

#: tpl/dash/dashboard.tpl.php:378
msgid "Current closest Cloud server is %s.&#10;Click to redetect."
msgstr "O servidor em nuvem mais próximo atual é %s.&#10;Clique para redetectar."

#: tpl/dash/dashboard.tpl.php:434 tpl/dash/dashboard.tpl.php:503
#: tpl/dash/dashboard.tpl.php:662 tpl/dash/dashboard.tpl.php:699
#: tpl/dash/dashboard.tpl.php:736 tpl/dash/dashboard.tpl.php:773
msgid "Last requested"
msgstr "Última solicitação"

#: tpl/dash/dashboard.tpl.php:441
msgid "Refresh page load time"
msgstr "Atualizar o tempo de carregamento da página"

#: tpl/dash/dashboard.tpl.php:352 tpl/general/online.tpl.php:115
msgid "Go to QUIC.cloud dashboard"
msgstr "Acessar o painel do QUIC.cloud"

#: tpl/dash/dashboard.tpl.php:193 tpl/dash/dashboard.tpl.php:707
#: tpl/dash/network_dash.tpl.php:31
msgid "Low Quality Image Placeholder"
msgstr "Marcador de posição de imagem de baixa qualidade"

#: tpl/dash/dashboard.tpl.php:175
msgid "Sync data from Cloud"
msgstr "Sincronizar dados a partir da nuvem"

#: tpl/dash/dashboard.tpl.php:172
msgid "QUIC.cloud Service Usage Statistics"
msgstr "Estatísticas de uso do serviço QUIC.cloud"

#: tpl/dash/dashboard.tpl.php:293 tpl/dash/network_dash.tpl.php:102
msgid "Total images optimized in this month"
msgstr "Total de imagens otimizadas neste mês"

#: tpl/dash/dashboard.tpl.php:292 tpl/dash/network_dash.tpl.php:101
msgid "Total Usage"
msgstr "Uso total"

#: tpl/dash/dashboard.tpl.php:267 tpl/dash/network_dash.tpl.php:94
msgid "Pay as You Go Usage Statistics"
msgstr "Estatísticas de uso do PAYG (Pagamento Conforme o Uso)"

#: tpl/dash/network_dash.tpl.php:92
msgid "This Month Usage"
msgstr "Uso deste mês"

#: tpl/dash/dashboard.tpl.php:264 tpl/dash/network_dash.tpl.php:91
msgid "PAYG Balance"
msgstr "Saldo PAYG"

#: tpl/dash/network_dash.tpl.php:90
msgid "Pay as You Go"
msgstr "Pague conforme o uso"

#: tpl/dash/dashboard.tpl.php:251 tpl/dash/network_dash.tpl.php:79
msgid "Usage"
msgstr "Uso"

#: tpl/dash/dashboard.tpl.php:251 tpl/dash/network_dash.tpl.php:79
msgid "Fast Queue Usage"
msgstr "Uso de fila rápida"

#: tpl/dash/dashboard.tpl.php:192 tpl/dash/network_dash.tpl.php:30
msgid "CDN Bandwidth"
msgstr "Largura de banda do CDN"

#: tpl/dash/network_dash.tpl.php:20
msgid "Usage Statistics"
msgstr "Estatísticas de uso"

#: tpl/dash/entry.tpl.php:21
msgid "LiteSpeed Cache Dashboard"
msgstr "Painel do LiteSpeed Cache"

#: tpl/dash/entry.tpl.php:12
msgid "Network Dashboard"
msgstr "Painel de rede"

#: tpl/general/online.tpl.php:36
msgid "No cloud services currently in use"
msgstr "Nenhum serviço em nuvem em uso no momento"

#: tpl/general/online.tpl.php:24
msgid "Click to clear all nodes for further redetection."
msgstr "Clique para limpar todos os nós para uma nova redetecção."

#: tpl/general/online.tpl.php:23
msgid "Current Cloud Nodes in Service"
msgstr "Nós de nuvem em serviço atualmente"

#: tpl/cdn/qc.tpl.php:105 tpl/cdn/qc.tpl.php:108 tpl/dash/dashboard.tpl.php:357
#: tpl/general/online.tpl.php:140
msgid "Link to QUIC.cloud"
msgstr "Vincular ao QUIC.cloud"

#: tpl/general/entry.tpl.php:9 tpl/general/entry.tpl.php:15
#: tpl/general/network_settings.tpl.php:10 tpl/general/settings.tpl.php:15
msgid "General Settings"
msgstr "Configurações gerais"

#: tpl/cdn/other.tpl.php:111
msgid "Specify which HTML element attributes will be replaced with CDN Mapping."
msgstr "Especificar quais atributos de elementos HTML serão substituídos pelo mapeamento de CDN."

#: src/admin-display.cls.php:240
msgid "Add new CDN URL"
msgstr "Adicionar novo URL de CDN"

#: src/admin-display.cls.php:239
msgid "Remove CDN URL"
msgstr "Remover URL do CDN"

#: tpl/cdn/cf.tpl.php:97
msgid "To enable the following functionality, turn ON Cloudflare API in CDN Settings."
msgstr "Para ativar a seguinte funcionalidade, ATIVE a API do Cloudflare em \"Configurações de CDN\"."

#: tpl/cdn/entry.tpl.php:8
msgid "QUIC.cloud"
msgstr "QUIC.cloud"

#: thirdparty/woocommerce.content.tpl.php:17
msgid "WooCommerce Settings"
msgstr "Configurações do WooCommerce"

#: src/doc.cls.php:155
msgid "Current Online Server IPs"
msgstr "IPs atuais do servidor on-line"

#: src/doc.cls.php:154
msgid "Before generating key, please verify all IPs on this list are allowlisted"
msgstr "Antes de gerar a chave, verifique se todos os IPs desta lista estão na lista de permissões"

#: src/doc.cls.php:153
msgid "For online services to work correctly, you must allowlist all %s server IPs."
msgstr "Para que os serviços online funcionem corretamente, você deve permitir todos os IPs do servidor %s na lista de permissões."

#: src/gui.cls.php:560 src/gui.cls.php:722
#: tpl/page_optm/settings_media.tpl.php:131 tpl/toolbox/purge.tpl.php:89
msgid "LQIP Cache"
msgstr "Cache de LQIP"

#: src/admin-settings.cls.php:274 src/admin-settings.cls.php:308
msgid "Options saved."
msgstr "Opções salvas."

#: src/img-optm.cls.php:1757
msgid "Removed backups successfully."
msgstr "Backups removidos."

#: src/img-optm.cls.php:1665
msgid "Calculated backups successfully."
msgstr "Backups calculados."

#: src/img-optm.cls.php:1599
msgid "Rescanned %d images successfully."
msgstr "%d imagens reexaminadas."

#: src/img-optm.cls.php:1537 src/img-optm.cls.php:1599
msgid "Rescanned successfully."
msgstr "Reexaminadas."

#: src/img-optm.cls.php:1472
msgid "Destroy all optimization data successfully."
msgstr "Todos os dados de otimização foram removidos."

#: src/img-optm.cls.php:1371
msgid "Cleaned up unfinished data successfully."
msgstr "Dados não concluídos limpos."

#: src/img-optm.cls.php:962
msgid "Pull Cron is running"
msgstr "O cron de recuperação está em execução"

#: src/img-optm.cls.php:686
msgid "No valid image found by Cloud server in the current request."
msgstr "Nenhuma imagem válida encontrada pelo servidor em nuvem na solicitação atual."

#: src/img-optm.cls.php:661
msgid "No valid image found in the current request."
msgstr "Nenhuma imagem válida encontrada na solicitação atual."

#: src/img-optm.cls.php:343
msgid "Pushed %1$s to Cloud server, accepted %2$s."
msgstr "Enviado %1$s para o servidor em nuvem, aceito %2$s."

#: src/lang.cls.php:266
msgid "Revisions Max Age"
msgstr "Idade máxima de revisões"

#: src/lang.cls.php:265
msgid "Revisions Max Number"
msgstr "Número máximo de revisões"

#: src/lang.cls.php:262
msgid "Debug URI Excludes"
msgstr "Exclusões de URI de depuração"

#: src/lang.cls.php:261
msgid "Debug URI Includes"
msgstr "Inclusões de URI de depuração"

#: src/lang.cls.php:241
msgid "HTML Attribute To Replace"
msgstr "Atributo HTML para substituir"

#: src/lang.cls.php:235
msgid "Use CDN Mapping"
msgstr "Usar mapeamento CDN"

#: src/lang.cls.php:233
msgid "Editor Heartbeat TTL"
msgstr "TTL do monitoramento de atividade (heartbeat) do editor"

#: src/lang.cls.php:232
msgid "Editor Heartbeat"
msgstr "Monitoramento de atividade (heartbeat) do editor"

#: src/lang.cls.php:231
msgid "Backend Heartbeat TTL"
msgstr "TTL do monitoramento de atividade (heartbeat) do painel"

#: src/lang.cls.php:230
msgid "Backend Heartbeat Control"
msgstr "Controle do monitoramento de atividade (heartbeat) do painel"

#: src/lang.cls.php:229
msgid "Frontend Heartbeat TTL"
msgstr "TTL do monitoramento de atividade (heartbeat) da interface"

#: src/lang.cls.php:228
msgid "Frontend Heartbeat Control"
msgstr "Controle do monitoramento de atividade (heartbeat) da interface"

#: tpl/toolbox/edit_htaccess.tpl.php:68
msgid "Backend .htaccess Path"
msgstr "Caminho .htaccess do painel"

#: tpl/toolbox/edit_htaccess.tpl.php:50
msgid "Frontend .htaccess Path"
msgstr "Caminho do .htaccess da interface"

#: src/lang.cls.php:218
msgid "ESI Nonces"
msgstr "Nonces ESI"

#: src/lang.cls.php:214
msgid "WordPress Image Quality Control"
msgstr "Controle de qualidade de imagem no WordPress"

#: src/lang.cls.php:206
msgid "Auto Request Cron"
msgstr "Cron de solicitação automática"

#: src/lang.cls.php:200
msgid "Generate LQIP In Background"
msgstr "Gerar LQIP em segundo plano"

#: src/lang.cls.php:198
msgid "LQIP Minimum Dimensions"
msgstr "Dimensões mínimas do LQIP"

#: src/lang.cls.php:197
msgid "LQIP Quality"
msgstr "Qualidade do LQIP"

#: src/lang.cls.php:196
msgid "LQIP Cloud Generator"
msgstr "Gerador de LQIP na nuvem"

#: src/lang.cls.php:195
msgid "Responsive Placeholder SVG"
msgstr "SVG do marcador de posição responsivo"

#: src/lang.cls.php:194
msgid "Responsive Placeholder Color"
msgstr "Cor do marcador de posição responsivo"

#: src/lang.cls.php:192
msgid "Basic Image Placeholder"
msgstr "Marcador de posição básico de imagem"

#: src/lang.cls.php:190
msgid "Lazy Load URI Excludes"
msgstr "Exclusões de URI para carregamento tardio"

#: src/lang.cls.php:189
msgid "Lazy Load Iframe Parent Class Name Excludes"
msgstr "Exclusões do nome de classes principal de iframes para carregamento tardio"

#: src/lang.cls.php:188
msgid "Lazy Load Iframe Class Name Excludes"
msgstr "Exclusões do nome de classe de iframes para carregamento tardio"

#: src/lang.cls.php:187
msgid "Lazy Load Image Parent Class Name Excludes"
msgstr "Exclusões do nome de classe principal da imagem para carregamento tardio"

#: src/lang.cls.php:182
msgid "Gravatar Cache TTL"
msgstr "TTL do cache do Gravatar"

#: src/lang.cls.php:181
msgid "Gravatar Cache Cron"
msgstr "Cron de cache do Gravatar"

#: src/gui.cls.php:570 src/gui.cls.php:732 src/lang.cls.php:180
#: tpl/presets/standard.tpl.php:43 tpl/toolbox/purge.tpl.php:98
msgid "Gravatar Cache"
msgstr "Cache do Gravatar"

#: src/lang.cls.php:160
msgid "DNS Prefetch Control"
msgstr "Controle de pré-busca de DNS"

#: src/lang.cls.php:155 tpl/presets/standard.tpl.php:40
msgid "Font Display Optimization"
msgstr "Otimização de exibição de fonte"

#: src/lang.cls.php:132
msgid "Force Public Cache URIs"
msgstr "Forçar URIs de cache público"

#: src/lang.cls.php:103
msgid "Notifications"
msgstr "Notificações"

#: src/lang.cls.php:97
msgid "Default HTTP Status Code Page TTL"
msgstr "TTL padrão da página de código de status HTTP"

#: src/lang.cls.php:96
msgid "Default REST TTL"
msgstr "TTL padrão da REST"

#: src/lang.cls.php:90
msgid "Enable Cache"
msgstr "Ativar cache"

#: src/cloud.cls.php:236 src/cloud.cls.php:291 src/lang.cls.php:86
msgid "Server IP"
msgstr "IP do servidor"

#: src/lang.cls.php:25
msgid "Images not requested"
msgstr "Imagens não solicitadas"

#: src/cloud.cls.php:1999
msgid "Sync credit allowance with Cloud Server successfully."
msgstr "A sincronização do limite de crédito com o servidor em nuvem foi realizada."

#: src/cloud.cls.php:1620
msgid "Failed to communicate with QUIC.cloud server"
msgstr "Falha ao se comunicar com o servidor QUIC.cloud"

#: src/cloud.cls.php:1543
msgid "Good news from QUIC.cloud server"
msgstr "Boas notícias do servidor QUIC.cloud"

#: src/cloud.cls.php:1527 src/cloud.cls.php:1535
msgid "Message from QUIC.cloud server"
msgstr "Mensagem do servidor QUIC.cloud"

#: src/cloud.cls.php:1234
msgid "Please try after %1$s for service %2$s."
msgstr "Tente novamente após %1$s para o serviço %2$s."

#: src/cloud.cls.php:1090
msgid "No available Cloud Node."
msgstr "Nenhum nó da nuvem disponível."

#: src/cloud.cls.php:973 src/cloud.cls.php:986 src/cloud.cls.php:1024
#: src/cloud.cls.php:1090 src/cloud.cls.php:1231
msgid "Cloud Error"
msgstr "Erro na nuvem"

#: src/data.cls.php:220
msgid "The database has been upgrading in the background since %s. This message will disappear once upgrade is complete."
msgstr "O banco de dados está sendo atualizado em segundo plano desde %s. Esta mensagem desaparecerá assim que a atualização estiver concluída."

#: src/media.cls.php:403
msgid "Restore from backup"
msgstr "Restaurar a partir do backup"

#: src/media.cls.php:388
msgid "No backup of unoptimized WebP file exists."
msgstr "Não há backup do arquivo WebP não otimizado."

#: src/media.cls.php:374
msgid "WebP file reduced by %1$s (%2$s)"
msgstr "Arquivo WebP reduzido em %1$s (%2$s)"

#: src/media.cls.php:366
msgid "Currently using original (unoptimized) version of WebP file."
msgstr "Atualmente usando a versão original (não otimizada) do arquivo WebP."

#: src/media.cls.php:359
msgid "Currently using optimized version of WebP file."
msgstr "Atualmente usando a versão otimizada do arquivo WebP."

#: src/media.cls.php:337
msgid "Orig"
msgstr "Original"

#: src/media.cls.php:335
msgid "(no savings)"
msgstr "(sem economia)"

#: src/media.cls.php:335
msgid "Orig %s"
msgstr "Original %s"

#: src/media.cls.php:334
msgid "Congratulation! Your file was already optimized"
msgstr "Parabéns! Seu arquivo já foi otimizado"

#: src/media.cls.php:329
msgid "No backup of original file exists."
msgstr "Não há backup do arquivo original."

#: src/media.cls.php:329 src/media.cls.php:387
msgid "Using optimized version of file. "
msgstr "Usando a versão otimizada do arquivo. "

#: src/media.cls.php:322
msgid "Orig saved %s"
msgstr "Economizado do original %s"

#: src/media.cls.php:318
msgid "Original file reduced by %1$s (%2$s)"
msgstr "Arquivo original reduzido em %1$s (%2$s)"

#: src/media.cls.php:312 src/media.cls.php:367
msgid "Click to switch to optimized version."
msgstr "Clique para alternar para a versão otimizada."

#: src/media.cls.php:312
msgid "Currently using original (unoptimized) version of file."
msgstr "Atualmente usando a versão original (não otimizada) do arquivo."

#: src/media.cls.php:311 src/media.cls.php:363
msgid "(non-optm)"
msgstr "(não-otimizada)"

#: src/media.cls.php:308 src/media.cls.php:360
msgid "Click to switch to original (unoptimized) version."
msgstr "Clique para alternar para a versão original (não otimizada)."

#: src/media.cls.php:308
msgid "Currently using optimized version of file."
msgstr "Atualmente usando a versão otimizada do arquivo."

#: src/media.cls.php:307 src/media.cls.php:330 src/media.cls.php:356
#: src/media.cls.php:389
msgid "(optm)"
msgstr "(otimizada)"

#: src/placeholder.cls.php:141
msgid "LQIP image preview for size %s"
msgstr "Pré-visualização de imagem LQIP para tamanho %s"

#: src/placeholder.cls.php:84
msgid "LQIP"
msgstr "LQIP"

#: src/crawler.cls.php:1390
msgid "Previously existed in blocklist"
msgstr "Existia anteriormente na lista de bloqueios"

#: src/crawler.cls.php:1387
msgid "Manually added to blocklist"
msgstr "Adicionado manualmente à lista de bloqueios"

#: src/htaccess.cls.php:328
msgid "Mobile Agent Rules"
msgstr "Regras do Mobile Agent"

#: src/crawler-map.cls.php:376
msgid "Sitemap created successfully: %d items"
msgstr "Sitemap criado: %d itens"

#: src/crawler-map.cls.php:279
msgid "Sitemap cleaned successfully"
msgstr "Sitemap limpo"

#: src/admin-display.cls.php:1192
msgid "Invalid IP"
msgstr "IP inválido"

#: src/admin-display.cls.php:1167
msgid "Value range"
msgstr "Intervalo de valores"

#: src/admin-display.cls.php:1164
msgid "Smaller than"
msgstr "Menor que"

#: src/admin-display.cls.php:1162
msgid "Larger than"
msgstr "Maior que"

#: src/admin-display.cls.php:1156
msgid "Zero, or"
msgstr "Zero, ou"

#: src/admin-display.cls.php:1144
msgid "Maximum value"
msgstr "Valor máximo"

#: src/admin-display.cls.php:1141
msgid "Minimum value"
msgstr "Valor mínimo"

#: src/admin-display.cls.php:1123
msgid "Path must end with %s"
msgstr "O caminho deve terminar com %s"

#: src/admin-display.cls.php:1106
msgid "Invalid rewrite rule"
msgstr "Regra de reescrita inválida"

#: src/admin-display.cls.php:1037
msgid "currently set to %s"
msgstr "atualmente definido para %s"

#: src/admin-display.cls.php:1030
msgid "This setting is overwritten by the PHP constant %s"
msgstr "Essa configuração é substituída pela constante PHP %s"

#: src/admin-display.cls.php:140
msgid "Toolbox"
msgstr "Caixa de ferramentas"

#: src/admin-display.cls.php:136
msgid "Database"
msgstr "Banco de dados"

#: src/admin-display.cls.php:134 tpl/dash/dashboard.tpl.php:191
#: tpl/dash/network_dash.tpl.php:29 tpl/general/online.tpl.php:120
#: tpl/general/online.tpl.php:135
msgid "Page Optimization"
msgstr "Otimização de página"

#: src/admin-display.cls.php:122 tpl/dash/entry.tpl.php:7
msgid "Dashboard"
msgstr "Painel"

#: src/db-optm.cls.php:292
msgid "Converted to InnoDB successfully."
msgstr "Convertido para InnoDB."

#: src/purge.cls.php:327
msgid "Cleaned all Gravatar files."
msgstr "Todos os arquivos do Gravatar foram limpos."

#: src/purge.cls.php:310
msgid "Cleaned all LQIP files."
msgstr "Todos os arquivos LQIP foram limpos."

#: src/error.cls.php:219
msgid "Unknown error"
msgstr "Erro desconhecido"

#: src/error.cls.php:208
msgid "Your domain has been forbidden from using our services due to a previous policy violation."
msgstr "Seu domínio foi proibido de usar nossos serviços devido a uma violação de política anterior."

#: src/error.cls.php:203
msgid "The callback validation to your domain failed. Please make sure there is no firewall blocking our servers. Response code: "
msgstr "A validação de retorno de chamada para seu domínio falhou. Certifique-se de que não haja nenhum firewall bloqueando nossos servidores. Código de resposta: "

#: src/error.cls.php:198
msgid "The callback validation to your domain failed. Please make sure there is no firewall blocking our servers."
msgstr "A validação de retorno de chamada para seu domínio falhou. Certifique-se de que não haja nenhum firewall bloqueando nossos servidores."

#: src/error.cls.php:194
msgid "The callback validation to your domain failed due to hash mismatch."
msgstr "A validação de retorno de chamada para seu domínio falhou, devido a uma incompatibilidade de hash."

#: src/error.cls.php:190
msgid "Your application is waiting for approval."
msgstr "Sua aplicação está aguardando aprovação."

#: src/error.cls.php:184
msgid "Previous request too recent. Please try again after %s."
msgstr "Solicitação anterior muito recente. Tente novamente após %s."

#: src/error.cls.php:179
msgid "Previous request too recent. Please try again later."
msgstr "Solicitação anterior muito recente. Tente novamente mais tarde."

#: src/error.cls.php:175
msgid "Crawler disabled by the server admin."
msgstr "O rastreador foi desativado pelo administrador do servidor."

#: src/error.cls.php:171
msgid "Failed to create table %1$s! SQL: %2$s."
msgstr "Falha ao criar a tabela %1$s! SQL: %2$s."

#: src/error.cls.php:147
msgid "Could not find %1$s in %2$s."
msgstr "Não foi possível encontrar %1$s em %2$s."

#: src/error.cls.php:135
msgid "Credits are not enough to proceed the current request."
msgstr "Os créditos não são suficientes para prosseguir com a solicitação atual."

#: src/error.cls.php:123
msgid "The domain key is not correct. Please try to sync your domain key again."
msgstr "A chave do domínio não está correta. Tente sincronizar sua chave de domínio novamente."

#: src/error.cls.php:104
msgid "There is proceeding queue not pulled yet."
msgstr "Há uma fila de processamento que ainda não foi concluída."

#: src/error.cls.php:100
msgid "Not enough parameters. Please check if the domain key is set correctly"
msgstr "Parâmetros insuficientes. Verifique se a chave de domínio está definida corretamente"

#: src/error.cls.php:96
msgid "The image list is empty."
msgstr "A lista de imagens está vazia."

#: src/error.cls.php:45
msgid "The setting %s is currently enabled."
msgstr "A configuração %s está ativada atualmente."

#: src/task.cls.php:233
msgid "LiteSpeed Crawler Cron"
msgstr "Cron do rastreador LiteSpeed"

#: src/task.cls.php:214
msgid "Every Minute"
msgstr "A cada minuto"

#: tpl/general/settings.tpl.php:110
msgid "Turn this option ON to show latest news automatically, including hotfixes, new releases, available beta versions, and promotions."
msgstr "ATIVAR esta opção para mostrar automaticamente as últimas notícias, incluindo correções urgentes, novos lançamentos, versões beta disponíveis e promoções."

#: tpl/toolbox/report.tpl.php:100
msgid "To grant wp-admin access to the LiteSpeed Support Team, please generate a passwordless link for the current logged-in user to be sent with the report."
msgstr "Para conceder acesso wp-admin à equipe de suporte LiteSpeed, gere um link sem senha para o usuário conectado atualmente ser enviado com o relatório."

#: tpl/toolbox/report.tpl.php:103
msgid "Generated links may be managed under <a %s>Settings</a>."
msgstr "Os links gerados podem ser gerenciados em <a %s>Configurações</a>."

#: tpl/toolbox/report.tpl.php:102
msgid "Please do NOT share the above passwordless link with anyone."
msgstr "NÃO compartilhe o link sem senha acima com ninguém."

#: tpl/toolbox/report.tpl.php:40
msgid "To generate a passwordless link for LiteSpeed Support Team access, you must install %s."
msgstr "Para gerar um link sem senha para acesso à equipe de suporte do LiteSpeed, você deve instalar %s."

#: tpl/banner/cloud_news.tpl.php:30 tpl/banner/cloud_news.tpl.php:41
msgid "Install"
msgstr "Instalar"

#: tpl/cache/settings-esi.tpl.php:46
msgid "These options are only available with LiteSpeed Enterprise Web Server or QUIC.cloud CDN."
msgstr "Essas opções estão disponíveis apenas com o LiteSpeed Enterprise Web Server ou o CDN QUIC.cloud."

#: tpl/banner/score.php:72 tpl/dash/dashboard.tpl.php:450
msgid "PageSpeed Score"
msgstr "Pontuação do PageSpeed"

#: tpl/banner/score.php:60 tpl/banner/score.php:94
#: tpl/dash/dashboard.tpl.php:406 tpl/dash/dashboard.tpl.php:486
msgid "Improved by"
msgstr "Melhorado por"

#: tpl/banner/score.php:51 tpl/banner/score.php:85
#: tpl/dash/dashboard.tpl.php:398 tpl/dash/dashboard.tpl.php:478
msgid "After"
msgstr "Depois"

#: tpl/banner/score.php:43 tpl/banner/score.php:77
#: tpl/dash/dashboard.tpl.php:389 tpl/dash/dashboard.tpl.php:470
msgid "Before"
msgstr "Antes"

#: tpl/banner/score.php:38 tpl/dash/dashboard.tpl.php:373
msgid "Page Load Time"
msgstr "Tempo de carregamento da página"

#: tpl/inc/check_cache_disabled.php:11
msgid "To use the caching functions you must have a LiteSpeed web server or be using QUIC.cloud CDN."
msgstr "Para usar as funções de cache, você deve ter um servidor web LiteSpeed ou estar usando o CDN do QUIC.cloud."

#: src/lang.cls.php:211
msgid "Preserve EXIF/XMP data"
msgstr "Preservar dados EXIF/XMP"

#: tpl/toolbox/beta_test.tpl.php:23
msgid "Try GitHub Version"
msgstr "Experimentar a versão do GitHub"

#: tpl/cdn/other.tpl.php:87
msgid "If you turn any of the above settings OFF, please remove the related file types from the %s box."
msgstr "Se você DESATIVAR qualquer uma das configurações acima, remova os tipos de arquivo relacionados da caixa %s."

#: src/doc.cls.php:124
msgid "Both full and partial strings can be used."
msgstr "Podem ser usadas strings completas e parciais."

#: tpl/page_optm/settings_media_exc.tpl.php:51
msgid "Images containing these class names will not be lazy loaded."
msgstr "Imagens que contenham esses nomes de classes não serão carregadas tardiamente."

#: src/lang.cls.php:186
msgid "Lazy Load Image Class Name Excludes"
msgstr "Exclusões de nome de classe de imagem para carregamento tardio"

#: tpl/cache/settings-cache.tpl.php:139 tpl/cache/settings-cache.tpl.php:164
msgid "For example, %1$s defines a TTL of %2$s seconds for %3$s."
msgstr "Por exemplo, %1$s define um TTL de %2$s segundos para %3$s."

#: tpl/cache/settings-cache.tpl.php:136 tpl/cache/settings-cache.tpl.php:161
msgid "To define a custom TTL for a URI, add a space followed by the TTL value to the end of the URI."
msgstr "Para definir um TTL personalizado para um URI, adicione um espaço seguido pelo valor TTL ao final do URI."

#: tpl/banner/new_version.php:93
msgid "Maybe Later"
msgstr "Talvez mais tarde"

#: tpl/banner/new_version.php:87
msgid "Turn On Auto Upgrade"
msgstr "Ativar atualização automática"

#: tpl/banner/new_version.php:77 tpl/banner/new_version_dev.tpl.php:41
#: tpl/toolbox/beta_test.tpl.php:60
msgid "Upgrade"
msgstr "Atualizar"

#: tpl/banner/new_version.php:66
msgid "New release %s is available now."
msgstr "Nova versão %s está disponível agora."

#: tpl/banner/new_version.php:58
msgid "New Version Available!"
msgstr "Nova versão disponível!"

#: tpl/banner/score.php:119
msgid "Created with ❤️ by LiteSpeed team."
msgstr "Criado com ❤️ pela equipe LiteSpeed."

#: tpl/banner/score.php:110
msgid "Sure I'd love to review!"
msgstr "Claro, adoraria fazer uma avaliação!"

#: tpl/banner/score.php:34
msgid "Thank You for Using the LiteSpeed Cache Plugin!"
msgstr "Obrigado por usar o plugin LiteSpeed Cache!"

#: src/activation.cls.php:503
msgid "Upgraded successfully."
msgstr "Atualizado."

#: src/activation.cls.php:494 src/activation.cls.php:499
msgid "Failed to upgrade."
msgstr "Falha ao atualizar."

#: src/conf.cls.php:682
msgid "Changed setting successfully."
msgstr "Configuração alterada."

#: tpl/cache/settings-esi.tpl.php:37
msgid "ESI sample for developers"
msgstr "Exemplo de ESI para desenvolvedores"

#: tpl/cache/settings-esi.tpl.php:29
msgid "Replace %1$s with %2$s."
msgstr "Substitua %1$s por %2$s."

#: tpl/cache/settings-esi.tpl.php:26
msgid "You can turn shortcodes into ESI blocks."
msgstr "Você pode transformar shortcodes em blocos ESI."

#: tpl/cache/settings-esi.tpl.php:22
msgid "WpW: Private Cache vs. Public Cache"
msgstr "WpW: cache privado vs. cache público"

#: tpl/page_optm/settings_html.tpl.php:124
msgid "Append query string %s to the resources to bypass this action."
msgstr "Anexar a string de consulta %s aos recursos para ignorar esta ação."

#: tpl/page_optm/settings_html.tpl.php:119
msgid "Google reCAPTCHA will be bypassed automatically."
msgstr "O Google reCAPTCHA será automaticamente ignorado."

#: tpl/crawler/settings.tpl.php:142
msgid "To crawl for a particular cookie, enter the cookie name, and the values you wish to crawl for. Values should be one per line. There will be one crawler created per cookie value, per simulated role."
msgstr "Para rastrear um cookie específico, digite o nome do cookie e os valores pelos quais deseja rastrear. Os valores devem ser digitados um por linha. Será criado um rastreador para cada valor de cookie, por função simulada."

#: src/admin-display.cls.php:215 tpl/crawler/settings.tpl.php:144
msgid "Cookie Values"
msgstr "Valores de cookies"

#: src/admin-display.cls.php:214
msgid "Cookie Name"
msgstr "Nome do cookie"

#: src/lang.cls.php:252
msgid "Cookie Simulation"
msgstr "Simulação de cookie"

#: tpl/page_optm/settings_html.tpl.php:138
msgid "Use Web Font Loader library to load Google Fonts asynchronously while leaving other CSS intact."
msgstr "Use a biblioteca de carregamento de fontes da web para carregar Google Fonts de forma assíncrona, mantendo o restante do CSS intacto."

#: tpl/general/settings_inc.auto_upgrade.tpl.php:16
msgid "Turn this option ON to have LiteSpeed Cache updated automatically, whenever a new version is released. If OFF, update manually as usual."
msgstr "Deixe esta opção como ATIVADO para que o LiteSpeed Cache seja atualizado automaticamente sempre que uma nova versão for lançada. Se estiver DESATIVADO, atualize manualmente como de costume."

#: src/lang.cls.php:100
msgid "Automatically Upgrade"
msgstr "Atualização automática"

#: tpl/toolbox/settings-debug.tpl.php:67
msgid "Your IP"
msgstr "Seu IP"

#: src/import.cls.php:153
msgid "Reset successfully."
msgstr "Redefinição concluída."

#: tpl/toolbox/import_export.tpl.php:51
msgid "This will reset all settings to default settings."
msgstr "Isso irá redefinir todas as configurações para as configurações padrão."

#: tpl/toolbox/import_export.tpl.php:50
msgid "Reset All Settings"
msgstr "Redefinir todas as configurações"

#: tpl/page_optm/settings_tuning_css.tpl.php:119
msgid "Separate critical CSS files will be generated for paths containing these strings."
msgstr "Arquivos CSS críticos separados serão gerados para os caminhos que contém essas strings."

#: src/lang.cls.php:170
msgid "Separate CCSS Cache URIs"
msgstr "Separar URIs de cache do CCSS"

#: tpl/page_optm/settings_tuning_css.tpl.php:105
msgid "For example, if every Page on the site has different formatting, enter %s in the box. Separate critical CSS files will be stored for every Page on the site."
msgstr "Por exemplo, se cada página no site tiver uma formatação diferente, digite %s na caixa. Arquivos CSS críticos separados serão armazenados para cada página no site."

#: tpl/page_optm/settings_tuning_css.tpl.php:104
msgid "List post types where each item of that type should have its own CCSS generated."
msgstr "Liste os tipos de post nos quais cada item desse tipo deve ter seu próprio CCSS gerado."

#: src/lang.cls.php:169
msgid "Separate CCSS Cache Post Types"
msgstr "Separar tipos de post para o cache do CCSS"

#: tpl/page_optm/settings_media.tpl.php:190
msgid "Size list in queue waiting for cron"
msgstr "Lista de tamanho na fila aguardando o cron"

#: tpl/page_optm/settings_media.tpl.php:165
msgid "If set to %1$s, before the placeholder is localized, the %2$s configuration will be used."
msgstr "Se definido como %1$s, antes que o marcador de posição seja localizado, a configuração %2$s será usada."

#: tpl/page_optm/settings_media.tpl.php:162
msgid "Automatically generate LQIP in the background via a cron-based queue."
msgstr "Gerar automaticamente LQIP em segundo plano através de uma fila baseada em cron."

#: tpl/page_optm/settings_media.tpl.php:67
msgid "This will generate the placeholder with same dimensions as the image if it has the width and height attributes."
msgstr "Isso gerará o marcador de posição com as mesmas dimensões da imagem se ela tiver os atributos de largura e altura."

#: tpl/page_optm/settings_media.tpl.php:66
msgid "Responsive image placeholders can help to reduce layout reshuffle when images are loaded."
msgstr "Marcadores de posição de imagens responsivas podem ajudar a reduzir a reorganização do layout quando as imagens são carregadas."

#: src/lang.cls.php:193
msgid "Responsive Placeholder"
msgstr "Marcador de posição responsivo"

#: tpl/toolbox/purge.tpl.php:90
msgid "This will delete all generated image LQIP placeholder files"
msgstr "Isso irá excluir todos os arquivos de marcador de posição de imagem LQIP gerados"

#: tpl/inc/check_cache_disabled.php:22
msgid "Please enable LiteSpeed Cache in the plugin settings."
msgstr "Ative o LiteSpeed Cache nas configurações do plugin."

#: tpl/inc/check_cache_disabled.php:16
msgid "Please enable the LSCache Module at the server level, or ask your hosting provider."
msgstr "Ative o módulo LSCache no nível do servidor ou consulte seu provedor de hospedagem."

#: src/cloud.cls.php:1399 src/cloud.cls.php:1422
msgid "Failed to request via WordPress"
msgstr "Falha ao solicitar através do WordPress"

#. Description of the plugin
#: litespeed-cache.php
msgid "High-performance page caching and site optimization from LiteSpeed"
msgstr "Cache de página de alto desempenho e otimização de site da LiteSpeed"

#: src/img-optm.cls.php:2111
msgid "Reset the optimized data successfully."
msgstr "Dados otimizados redefinidos."

#: src/gui.cls.php:795
msgid "Update %s now"
msgstr "Atualizar %s agora"

#: src/gui.cls.php:792
msgid "View %1$s version %2$s details"
msgstr "Ver detalhes da versão %2$s do %1$s"

#: src/gui.cls.php:790
msgid "<a href=\"%1$s\" %2$s>View version %3$s details</a> or <a href=\"%4$s\" %5$s target=\"_blank\">update now</a>."
msgstr "<a href=\"%1$s\" %2$s>Ver detalhes da versão %3$s</a> ou <a href=\"%4$s\" %5$s target=\"_blank\">atualizar agora</a>."

#: src/gui.cls.php:770
msgid "Install %s"
msgstr "Instalar %s"

#: tpl/inc/check_cache_disabled.php:34
msgid "LSCache caching functions on this page are currently unavailable!"
msgstr "As funções de cache do LSCache nesta página estão atualmente indisponíveis!"

#: src/cloud.cls.php:1553
msgid "%1$s plugin version %2$s required for this action."
msgstr "A versão %2$s do plugin %1$s é necessária para esta ação."

#: src/cloud.cls.php:1482
msgid "We are working hard to improve your online service experience. The service will be unavailable while we work. We apologize for any inconvenience."
msgstr "Estamos trabalhando intensamente para melhorar sua experiência de serviço on-line. O serviço estará indisponível durante nosso trabalho. Pedimos desculpas por qualquer inconveniente."

#: tpl/img_optm/settings.tpl.php:52
msgid "Automatically remove the original image backups after fetching optimized images."
msgstr "Remover automaticamente os backups das imagens originais após buscar as imagens otimizadas."

#: src/lang.cls.php:208
msgid "Remove Original Backups"
msgstr "Remover backups originais"

#: tpl/img_optm/settings.tpl.php:26
msgid "Automatically request optimization via cron job."
msgstr "Solicitar otimização automática por meio de tarefa cron."

#: tpl/img_optm/summary.tpl.php:181
msgid "A backup of each image is saved before it is optimized."
msgstr "É feito um backup de cada imagem antes de ser otimizada."

#: src/img-optm.cls.php:1904
msgid "Switched images successfully."
msgstr "Imagens alteradas."

#: tpl/img_optm/settings.tpl.php:73
msgid "This can improve quality but may result in larger images than lossy compression will."
msgstr "Isso pode melhorar a qualidade, mas pode resultar em imagens maiores do que a compactação com perda de qualidade."

#: tpl/img_optm/settings.tpl.php:72
msgid "Optimize images using lossless compression."
msgstr "Otimizar imagens usando compactação sem perda de qualidade."

#: src/lang.cls.php:210
msgid "Optimize Losslessly"
msgstr "Otimizar sem perda de qualidade"

#: tpl/img_optm/settings.media_webp.tpl.php:17
msgid "Request WebP/AVIF versions of original images when doing optimization."
msgstr "Solicitar versões WebP/AVIF das imagens originais ao realizar a otimização."

#: tpl/img_optm/settings.tpl.php:39
msgid "Optimize images and save backups of the originals in the same folder."
msgstr "Otimizar imagens e salvar backups das originais na mesma pasta."

#: src/lang.cls.php:207
msgid "Optimize Original Images"
msgstr "Otimizar imagens originais"

#: tpl/page_optm/settings_css.tpl.php:221
msgid "When this option is turned %s, it will also load Google Fonts asynchronously."
msgstr "Quando esta opção é ativada %s, ela também carregará o Google Fonts de forma assíncrona."

#: src/purge.cls.php:253
msgid "Cleaned all Critical CSS files."
msgstr "Todos os arquivos CSS críticos foram limpos."

#: tpl/page_optm/settings_css.tpl.php:334
msgid "This will inline the asynchronous CSS library to avoid render blocking."
msgstr "Isso irá incorporar a biblioteca CSS assíncrona para evitar bloqueios de renderização."

#: src/lang.cls.php:154
msgid "Inline CSS Async Lib"
msgstr "Biblioteca assíncrona de CSS embutido"

#: tpl/page_optm/settings_localization.tpl.php:63
#: tpl/page_optm/settings_media.tpl.php:209
msgid "Run Queue Manually"
msgstr "Executar fila manualmente"

#: tpl/page_optm/settings_css.tpl.php:112
#: tpl/page_optm/settings_css.tpl.php:256 tpl/page_optm/settings_vpi.tpl.php:59
msgid "URL list in %s queue waiting for cron"
msgstr "Lista de URLs na fila %s aguardando o cron"

#: tpl/page_optm/settings_css.tpl.php:100
#: tpl/page_optm/settings_css.tpl.php:244
msgid "Last requested cost"
msgstr "Custo da última solicitação."

#: tpl/dash/dashboard.tpl.php:639 tpl/dash/dashboard.tpl.php:676
#: tpl/dash/dashboard.tpl.php:713 tpl/dash/dashboard.tpl.php:750
#: tpl/page_optm/settings_css.tpl.php:97 tpl/page_optm/settings_css.tpl.php:241
#: tpl/page_optm/settings_media.tpl.php:178
#: tpl/page_optm/settings_vpi.tpl.php:47
msgid "Last generated"
msgstr "Última gerada"

#: tpl/page_optm/settings_media.tpl.php:170
msgid "If set to %s this is done in the foreground, which may slow down page load."
msgstr "Se definido como %s, isso é feito em primeiro plano, o que pode retardar o carregamento da página."

#: tpl/page_optm/settings_css.tpl.php:220
msgid "Automatic generation of critical CSS is in the background via a cron-based queue."
msgstr "A geração automática de CSS crítico é realizada em segundo plano por meio de uma fila baseada em cron."

#: tpl/page_optm/settings_css.tpl.php:216
msgid "Optimize CSS delivery."
msgstr "Otimizar a entrega de CSS."

#: tpl/toolbox/purge.tpl.php:63
msgid "This will delete all generated critical CSS files"
msgstr "Isso irá excluir todos os arquivos de CSS crítico gerados"

#: tpl/dash/dashboard.tpl.php:633 tpl/toolbox/purge.tpl.php:62
msgid "Critical CSS"
msgstr "CSS crítico"

#: src/doc.cls.php:66
msgid "This site utilizes caching in order to facilitate a faster response time and better user experience. Caching potentially stores a duplicate copy of every web page that is on display on this site. All cache files are temporary, and are never accessed by any third party, except as necessary to obtain technical support from the cache plugin vendor. Cache files expire on a schedule set by the site administrator, but may easily be purged by the admin before their natural expiration, if necessary. We may use QUIC.cloud services to process & cache your data temporarily."
msgstr "Este site utiliza o cache para facilitar um tempo de resposta mais rápido e uma melhor experiência do usuário. O cache potencialmente armazena uma cópia duplicada de cada página da web exibida neste site. Todos os arquivos de cache são temporários e nunca são acessados por terceiros, exceto conforme necessário para obter suporte técnico do fornecedor do plugin de cache. Os arquivos de cache expiram conforme programado pelo administrador do site, mas podem ser facilmente eliminados pelo administrador antes da expiração natural, se necessário. Podemos usar os serviços QUIC.cloud para processar e armazenar em cache temporariamente os seus dados."

#: tpl/toolbox/heartbeat.tpl.php:19
msgid "Disabling this may cause WordPress tasks triggered by AJAX to stop working."
msgstr "Desativar isso pode fazer com que as tarefas do WordPress acionadas por AJAX parem de funcionar."

#: src/utility.cls.php:228
msgid "right now"
msgstr "neste instante"

#: src/utility.cls.php:228
msgid "just now"
msgstr "recentemente"

#: tpl/img_optm/summary.tpl.php:250
msgid "Saved"
msgstr "Salvo"

#: tpl/img_optm/summary.tpl.php:244
#: tpl/page_optm/settings_localization.tpl.php:52
msgid "Last ran"
msgstr "Última execução"

#: tpl/img_optm/settings.tpl.php:58 tpl/img_optm/summary.tpl.php:236
msgid "You will be unable to Revert Optimization once the backups are deleted!"
msgstr "Você não poderá reverter a otimização uma vez que os backups forem excluídos!"

#: tpl/img_optm/settings.tpl.php:57 tpl/img_optm/summary.tpl.php:235
msgid "This is irreversible."
msgstr "Isso é irreversível."

#: tpl/img_optm/summary.tpl.php:255
msgid "Remove Original Image Backups"
msgstr "Remover backups de imagens originais"

#: tpl/img_optm/summary.tpl.php:254
msgid "Are you sure you want to remove all image backups?"
msgstr "Tem certeza de que deseja remover todos os backups de imagens?"

#: tpl/crawler/blacklist.tpl.php:26 tpl/img_optm/summary.tpl.php:195
msgid "Total"
msgstr "Total"

#: tpl/img_optm/summary.tpl.php:192 tpl/img_optm/summary.tpl.php:247
msgid "Files"
msgstr "Arquivos"

#: tpl/img_optm/summary.tpl.php:188
msgid "Last calculated"
msgstr "Último cálculo"

#: tpl/img_optm/summary.tpl.php:203
msgid "Calculate Original Image Storage"
msgstr "Calcular o armazenamento de imagem original"

#: tpl/img_optm/summary.tpl.php:177
msgid "Storage Optimization"
msgstr "Otimização de armazenamento"

#: tpl/img_optm/settings.tpl.php:133
msgid "Enable replacement of WebP/AVIF in %s elements that were generated outside of WordPress logic."
msgstr "Ativar a substituição de WebP/AVIF em elementos %s que foram gerados fora da lógica do WordPress."

#: tpl/cdn/other.tpl.php:113 tpl/img_optm/settings.tpl.php:119
msgid "Use the format %1$s or %2$s (element is optional)."
msgstr "Use o formato %1$s ou %2$s (o elemento é opcional)."

#: tpl/cdn/other.tpl.php:112 tpl/img_optm/settings.tpl.php:118
msgid "Only attributes listed here will be replaced."
msgstr "Apenas os atributos listados aqui serão substituídos."

#: tpl/img_optm/settings.tpl.php:117
msgid "Specify which element attributes will be replaced with WebP/AVIF."
msgstr "Especificar quais atributos de elementos serão substituídos por WebP/AVIF."

#: src/lang.cls.php:212
msgid "WebP/AVIF Attribute To Replace"
msgstr "Atributo WebP/AVIF a ser substituído"

#: tpl/cdn/other.tpl.php:150
msgid "Only files within these directories will be pointed to the CDN."
msgstr "Apenas arquivos dentro destes diretórios serão apontados para o CDN."

#: src/lang.cls.php:243
msgid "Included Directories"
msgstr "Diretórios incluídos"

#: tpl/cache/settings-purge.tpl.php:152
msgid "A Purge All will be executed when WordPress runs these hooks."
msgstr "Um \"Limpar tudo\" será executado quando o WordPress executar esses ganchos."

#: src/lang.cls.php:220
msgid "Purge All Hooks"
msgstr "Limpar todos os ganchos"

#: src/purge.cls.php:212
msgid "Purged all caches successfully."
msgstr "Todos os caches foram limpos."

#: src/gui.cls.php:484 src/gui.cls.php:593 src/gui.cls.php:646
msgid "LSCache"
msgstr "LSCache"

#: src/gui.cls.php:428
msgid "Forced cacheable"
msgstr "Armazenamento em cache forçado"

#: tpl/cache/settings-cache.tpl.php:133
msgid "Paths containing these strings will be cached regardless of no-cacheable settings."
msgstr "Caminhos de URI contendo essas strings serão armazenadas em cache independentemente das configurações de não armazenamento em cache."

#: src/lang.cls.php:131
msgid "Force Cache URIs"
msgstr "Forçar cache de URIs"

#: tpl/cache/network_settings-excludes.tpl.php:17
#: tpl/cache/settings-excludes.tpl.php:15
msgid "Exclude Settings"
msgstr "Configurações de exclusão"

#: tpl/toolbox/settings-debug.tpl.php:38
msgid "This will disable LSCache and all optimization features for debug purpose."
msgstr "Isso desativará o LSCache e todos os recursos de otimização para fins de depuração."

#: src/lang.cls.php:255 tpl/inc/disabled_all.php:6
msgid "Disable All Features"
msgstr "Desativar todos os recursos"

#: src/gui.cls.php:521 src/gui.cls.php:683 tpl/toolbox/purge.tpl.php:53
msgid "Opcode Cache"
msgstr "Cache de Opcode"

#: src/gui.cls.php:492 src/gui.cls.php:654 tpl/toolbox/purge.tpl.php:36
msgid "CSS/JS Cache"
msgstr "Cache de CSS/JS"

#: src/gui.cls.php:751 tpl/img_optm/summary.tpl.php:169
msgid "Remove all previous unfinished image optimization requests."
msgstr "Remover todas as solicitações anteriores de otimização de imagem não concluídas."

#: src/gui.cls.php:752 tpl/img_optm/summary.tpl.php:171
msgid "Clean Up Unfinished Data"
msgstr "Limpar dados não concluídos"

#: tpl/banner/slack.php:40
msgid "Join Us on Slack"
msgstr "Junte-se a nós no Slack"

#. translators: %s: Link to LiteSpeed Slack community
#: tpl/banner/slack.php:28
msgid "Join the %s community."
msgstr "Junte-se à comunidade %s."

#: tpl/banner/slack.php:24
msgid "Want to connect with other LiteSpeed users?"
msgstr "Deseja se conectar com outros usuários do LiteSpeed?"

#: tpl/cdn/cf.tpl.php:34
msgid "Get it from <a %1$s>%2$s</a>."
msgstr "Obtenha-o em <a %1$s>%2$s</a>."

#: tpl/cdn/cf.tpl.php:33
msgid "Your API key / token is used to access %s APIs."
msgstr "Sua chave de API / token é usada para acessar APIs de %s."

#: tpl/cdn/cf.tpl.php:44
msgid "Your Email address on %s."
msgstr "Seu endereço de e-mail em %s."

#: tpl/cdn/cf.tpl.php:25
msgid "Use %s API functionality."
msgstr "Usar a funcionalidade da API %s."

#: tpl/cdn/other.tpl.php:64
msgid "To randomize CDN hostname, define multiple hostnames for the same resources."
msgstr "Para randomizar o nome do host do CDN, defina vários nomes de host para os mesmos recursos."

#: tpl/inc/admin_footer.php:18
msgid "Join LiteSpeed Slack community"
msgstr "Junte-se à comunidade do LiteSpeed no Slack"

#: tpl/inc/admin_footer.php:16
msgid "Visit LSCWP support forum"
msgstr "Visite o fórum de suporte do LSCWP"

#: src/lang.cls.php:28 tpl/dash/dashboard.tpl.php:565
msgid "Images notified to pull"
msgstr "Imagens notificadas a serem recuperadas"

#: tpl/img_optm/summary.tpl.php:283
msgid "What is a group?"
msgstr "O que é um grupo?"

#: src/admin-display.cls.php:1260
msgid "%s image"
msgstr "%s imagem"

#: src/admin-display.cls.php:1257
msgid "%s group"
msgstr "%s grupo"

#: src/admin-display.cls.php:1248
msgid "%s images"
msgstr "%s imagens"

#: src/admin-display.cls.php:1245
msgid "%s groups"
msgstr "%s grupos"

#: src/crawler.cls.php:1216
msgid "Guest"
msgstr "Visitante"

#: tpl/crawler/settings.tpl.php:98
msgid "To crawl the site as a logged-in user, enter the user ids to be simulated."
msgstr "Para rastrear o site como um usuário conectado, digite os IDs de usuário a serem simulados."

#: src/lang.cls.php:251
msgid "Role Simulation"
msgstr "Simulação de função"

#: tpl/crawler/summary.tpl.php:195
msgid "running"
msgstr "executando"

#: tpl/db_optm/manage.tpl.php:183
msgid "Size"
msgstr "Tamanho"

#: tpl/crawler/summary.tpl.php:109 tpl/dash/dashboard.tpl.php:103
#: tpl/dash/dashboard.tpl.php:808
msgid "Ended reason"
msgstr "Motivo do término"

#: tpl/crawler/summary.tpl.php:102 tpl/dash/dashboard.tpl.php:96
#: tpl/dash/dashboard.tpl.php:801
msgid "Last interval"
msgstr "Último intervalo"

#: tpl/crawler/summary.tpl.php:90 tpl/dash/dashboard.tpl.php:89
#: tpl/dash/dashboard.tpl.php:794
msgid "Current crawler started at"
msgstr "O rastreador atual iniciou em"

#: tpl/crawler/summary.tpl.php:83
msgid "Run time for previous crawler"
msgstr "Tempo de execução do rastreador anterior"

#: tpl/crawler/summary.tpl.php:77 tpl/crawler/summary.tpl.php:84
msgid "%d seconds"
msgstr "%d segundos"

#: tpl/crawler/summary.tpl.php:76
msgid "Last complete run time for all crawlers"
msgstr "Tempo da última execução completa de todos os rastreadores"

#: tpl/crawler/summary.tpl.php:62
msgid "Current sitemap crawl started at"
msgstr "O rastreamento atual do sitemap começou em"

#. translators: %1$s: Object Cache Admin title, %2$s: OFF status
#: tpl/cache/settings_inc.object.tpl.php:278
msgid "Save transients in database when %1$s is %2$s."
msgstr "Salvar transientes no banco de dados quando %1$s está %2$s."

#: src/lang.cls.php:125
msgid "Store Transients"
msgstr "Armazenar transientes"

#. translators: %1$s: Cache Mobile label, %2$s: ON status, %3$s: List of Mobile
#. User Agents label
#: tpl/cache/settings_inc.cache_mobile.tpl.php:89
msgid "If %1$s is %2$s, then %3$s must be populated!"
msgstr "Se %1$s for %2$s, então %3$s deve ser preenchido!"

#: tpl/crawler/settings.tpl.php:79
msgid "Server allowed max value"
msgstr "Valor máximo permitido pelo servidor"

#: tpl/crawler/settings.tpl.php:74
msgid "Server enforced value"
msgstr "Valor imposto pelo servidor"

#: tpl/cache/more_settings_tip.tpl.php:22
#: tpl/cache/settings-excludes.tpl.php:71
#: tpl/cache/settings-excludes.tpl.php:104 tpl/cdn/other.tpl.php:63
#: tpl/crawler/settings.tpl.php:73 tpl/crawler/settings.tpl.php:78
msgid "NOTE"
msgstr "OBSERVAÇÃO"

#: src/admin-display.cls.php:1214
msgid "Server variable(s) %s available to override this setting."
msgstr "Variável(is) de servidor %s disponível(eis) para substituir esta configuração."

#: src/admin-display.cls.php:1212 tpl/cache/settings-esi.tpl.php:105
#: tpl/page_optm/settings_css.tpl.php:224
#: tpl/page_optm/settings_html.tpl.php:123
#: tpl/page_optm/settings_media.tpl.php:249
#: tpl/page_optm/settings_media_exc.tpl.php:27
#: tpl/page_optm/settings_tuning.tpl.php:39
#: tpl/page_optm/settings_tuning.tpl.php:59
#: tpl/page_optm/settings_tuning.tpl.php:80
#: tpl/page_optm/settings_tuning.tpl.php:101
#: tpl/page_optm/settings_tuning.tpl.php:120
#: tpl/page_optm/settings_tuning_css.tpl.php:25
#: tpl/page_optm/settings_tuning_css.tpl.php:86
#: tpl/toolbox/edit_htaccess.tpl.php:58 tpl/toolbox/edit_htaccess.tpl.php:76
msgid "API"
msgstr "API"

#: src/purge.cls.php:409
msgid "Reset the entire opcode cache successfully."
msgstr "Todo o cache Opcode foi redefinido."

#: src/purge.cls.php:394
msgid "Opcode cache is not enabled."
msgstr "O cache Opcode não está ativado."

#: src/import.cls.php:131
msgid "Imported setting file %s successfully."
msgstr "Arquivo de configuração %s importado."

#: src/import.cls.php:78
msgid "Import failed due to file error."
msgstr "A importação falhou devido a um erro no arquivo."

#: tpl/page_optm/settings_css.tpl.php:56 tpl/page_optm/settings_js.tpl.php:40
msgid "How to Fix Problems Caused by CSS/JS Optimization."
msgstr "Como corrigir problemas causados pela otimização de CSS/JS."

#: tpl/cache/settings-advanced.tpl.php:76
msgid "This will generate extra requests to the server, which will increase server load."
msgstr "Isso irá gerar solicitações adicionais ao servidor, o que aumentará a carga do servidor."

#: tpl/cache/settings-advanced.tpl.php:71
msgid "When a visitor hovers over a page link, preload that page. This will speed up the visit to that link."
msgstr "Quando um visitante passar o mouse sobre um link da página, pré-carregar essa página. Isso acelerará a visita a esse link."

#: src/lang.cls.php:222
msgid "Instant Click"
msgstr "Clique instantâneo"

#: tpl/toolbox/purge.tpl.php:54
msgid "Reset the entire opcode cache"
msgstr "Redefinir todo o cache de opcode"

#: tpl/toolbox/import_export.tpl.php:47
msgid "This will import settings from a file and override all current LiteSpeed Cache settings."
msgstr "Isso irá importar configurações de um arquivo e substituir todas as configurações atuais do LiteSpeed Cache."

#: tpl/toolbox/import_export.tpl.php:42
msgid "Last imported"
msgstr "Última importação"

#: tpl/toolbox/import_export.tpl.php:36
msgid "Import"
msgstr "Importar"

#: tpl/toolbox/import_export.tpl.php:28
msgid "Import Settings"
msgstr "Importar configurações"

#: tpl/toolbox/import_export.tpl.php:25
msgid "This will export all current LiteSpeed Cache settings and save them as a file."
msgstr "Isso irá exportar todas as configurações atuais do LiteSpeed Cache e salvá-las como um arquivo."

#: tpl/toolbox/import_export.tpl.php:20
msgid "Last exported"
msgstr "Última exportação"

#: tpl/toolbox/import_export.tpl.php:15
msgid "Export"
msgstr "Exportar"

#: tpl/toolbox/import_export.tpl.php:10
msgid "Export Settings"
msgstr "Exportar configurações"

#: tpl/presets/entry.tpl.php:8 tpl/toolbox/entry.tpl.php:11
msgid "Import / Export"
msgstr "Importação/exportação"

#: tpl/cache/settings_inc.object.tpl.php:249
msgid "Use keep-alive connections to speed up cache operations."
msgstr "Use conexões keep-alive (mantenha ativa) para acelerar as operações de cache."

#: tpl/cache/settings_inc.object.tpl.php:209
msgid "Database to be used"
msgstr "Banco de dados a ser usado"

#: src/lang.cls.php:120
msgid "Redis Database ID"
msgstr "ID do banco de dados Redis"

#: tpl/cache/settings_inc.object.tpl.php:196
msgid "Specify the password used when connecting."
msgstr "Especifique a senha usada durante a conexão."

#: src/lang.cls.php:119
msgid "Password"
msgstr "Senha"

#. translators: %s: SASL
#: tpl/cache/settings_inc.object.tpl.php:180
msgid "Only available when %s is installed."
msgstr "Disponível apenas quando %s está instalado."

#: src/lang.cls.php:118
msgid "Username"
msgstr "Nome de usuário"

#. translators: %s: Object cache name
#: tpl/cache/settings_inc.object.tpl.php:99
msgid "Your %s Hostname or IP address."
msgstr "Seu nome de servidor ou endereço IP %s."

#: src/lang.cls.php:114
msgid "Method"
msgstr "Método"

#: src/purge.cls.php:450
msgid "Purge all object caches successfully."
msgstr "Limpeza de todos os caches de objetos concluída."

#: src/purge.cls.php:437
msgid "Object cache is not enabled."
msgstr "O cache de objeto não está ativado."

#: tpl/cache/settings_inc.object.tpl.php:262
msgid "Improve wp-admin speed through caching. (May encounter expired data)"
msgstr "Melhore a velocidade do wp-admin por meio de cache. (Pode encontrar dados expirados)"

#: src/lang.cls.php:124
msgid "Cache WP-Admin"
msgstr "Cache do WP-Admin"

#: src/lang.cls.php:123
msgid "Persistent Connection"
msgstr "Conexão persistente"

#: src/lang.cls.php:122
msgid "Do Not Cache Groups"
msgstr "Não armazenar grupos em cache"

#: tpl/cache/settings_inc.object.tpl.php:222
msgid "Groups cached at the network level."
msgstr "Grupos armazenados em cache no nível da rede."

#: src/lang.cls.php:121
msgid "Global Groups"
msgstr "Grupos globais"

#: tpl/cache/settings_inc.object.tpl.php:71
msgid "Connection Test"
msgstr "Teste de conexão"

#. translators: %s: Object cache name
#: tpl/cache/settings_inc.object.tpl.php:58
#: tpl/cache/settings_inc.object.tpl.php:66
msgid "%s Extension"
msgstr "Extensão %s"

#: tpl/cache/settings_inc.object.tpl.php:52 tpl/crawler/blacklist.tpl.php:35
#: tpl/crawler/summary.tpl.php:138
msgid "Status"
msgstr "Status"

#: tpl/cache/settings_inc.object.tpl.php:164
msgid "Default TTL for cached objects."
msgstr "TTL (Tempo de Vida) padrão para objetos em cache."

#: src/lang.cls.php:117
msgid "Default Object Lifetime"
msgstr "Tempo de vida padrão do objeto"

#: src/lang.cls.php:116
msgid "Port"
msgstr "Porta"

#: src/lang.cls.php:115
msgid "Host"
msgstr "Servidor"

#: src/gui.cls.php:511 src/gui.cls.php:673 src/lang.cls.php:113
#: tpl/dash/dashboard.tpl.php:56 tpl/dash/dashboard.tpl.php:613
#: tpl/toolbox/purge.tpl.php:44
msgid "Object Cache"
msgstr "Cache de objetos"

#: tpl/cache/settings_inc.object.tpl.php:28
msgid "Failed"
msgstr "Reprovado"

#: tpl/cache/settings_inc.object.tpl.php:25
msgid "Passed"
msgstr "Aprovado"

#: tpl/cache/settings_inc.object.tpl.php:23
msgid "Not Available"
msgstr "Não disponível"

#: tpl/toolbox/purge.tpl.php:45
msgid "Purge all the object caches"
msgstr "Limpar todos os caches de objetos"

#: src/cdn/cloudflare.cls.php:259 src/cdn/cloudflare.cls.php:281
msgid "Failed to communicate with Cloudflare"
msgstr "Falha ao se comunicar com o Cloudflare"

#: src/cdn/cloudflare.cls.php:272
msgid "Communicated with Cloudflare successfully."
msgstr "A comunicação com o Cloudflare foi bem-sucedida."

#: src/cdn/cloudflare.cls.php:169
msgid "No available Cloudflare zone"
msgstr "Nenhuma zona Cloudflare disponível"

#: src/cdn/cloudflare.cls.php:155
msgid "Notified Cloudflare to purge all successfully."
msgstr "O Cloudflare foi notificado para limpar tudo."

#: src/cdn/cloudflare.cls.php:139
msgid "Cloudflare API is set to off."
msgstr "A API do Cloudflare está definida como desativada."

#: src/cdn/cloudflare.cls.php:111
msgid "Notified Cloudflare to set development mode to %s successfully."
msgstr "O Cloudflare foi notificado para definir o modo de desenvolvimento para %s."

#: tpl/cdn/cf.tpl.php:59
msgid "Once saved, it will be matched with the current list and completed automatically."
msgstr "Depois de salvo, ele será correspondido com a lista atual e preenchido automaticamente."

#: tpl/cdn/cf.tpl.php:58
msgid "You can just type part of the domain."
msgstr "Você pode simplesmente digitar parte do domínio."

#: tpl/cdn/cf.tpl.php:50
msgid "Domain"
msgstr "Domínio"

#: src/lang.cls.php:245
msgid "Cloudflare API"
msgstr "API Cloudflare"

#: tpl/cdn/cf.tpl.php:150
msgid "Purge Everything"
msgstr "Limpar tudo"

#: tpl/cdn/cf.tpl.php:144
msgid "Cloudflare Cache"
msgstr "Cache do Cloudflare"

#: tpl/cdn/cf.tpl.php:138
msgid "Development Mode will be turned off automatically after three hours."
msgstr "O modo de desenvolvimento será desativado automaticamente após três horas."

#: tpl/cdn/cf.tpl.php:137
msgid "Temporarily bypass Cloudflare cache. This allows changes to the origin server to be seen in realtime."
msgstr "Ignorar temporariamente o cache do Cloudflare. Isso permite que as alterações no servidor de origem sejam vistas em tempo real."

#: tpl/cdn/cf.tpl.php:129
msgid "Development mode will be automatically turned off in %s."
msgstr "O modo de desenvolvimento será automaticamente desativado em %s."

#: tpl/cdn/cf.tpl.php:128
msgid "Current status is %s."
msgstr "O status atual é %s."

#: tpl/cdn/cf.tpl.php:123
msgid "Current status is %1$s since %2$s."
msgstr "O status atual é %1$s desde %2$s."

#: tpl/cdn/cf.tpl.php:114
msgid "Check Status"
msgstr "Verificar status"

#: tpl/cdn/cf.tpl.php:111
msgid "Turn OFF"
msgstr "DESATIVAR"

#: tpl/cdn/cf.tpl.php:108
msgid "Turn ON"
msgstr "ATIVAR"

#: tpl/cdn/cf.tpl.php:106
msgid "Development Mode"
msgstr "Modo de desenvolvimento"

#: tpl/cdn/cf.tpl.php:103
msgid "Cloudflare Zone"
msgstr "Zona do Cloudflare"

#: tpl/cdn/cf.tpl.php:102
msgid "Cloudflare Domain"
msgstr "Domínio do Cloudflare"

#: src/gui.cls.php:501 src/gui.cls.php:663 tpl/cdn/cf.tpl.php:91
#: tpl/cdn/entry.tpl.php:9
msgid "Cloudflare"
msgstr "Cloudflare"

#: tpl/page_optm/settings_html.tpl.php:37
#: tpl/page_optm/settings_html.tpl.php:68
msgid "For example"
msgstr "Por exemplo"

#: tpl/page_optm/settings_html.tpl.php:36
msgid "Prefetching DNS can reduce latency for visitors."
msgstr "O pré-carregamento de DNS pode reduzir a latência para os visitantes."

#: src/lang.cls.php:159
msgid "DNS Prefetch"
msgstr "Pré-busca de DNS"

#: tpl/page_optm/settings_media.tpl.php:35
msgid "Adding Style to Your Lazy-Loaded Images"
msgstr "Adicionando estilo às suas imagens carregadas de forma lenta"

#: src/admin-display.cls.php:1078 src/admin-display.cls.php:1082
#: tpl/cdn/other.tpl.php:84
msgid "Default value"
msgstr "Valor padrão"

#: tpl/cdn/other.tpl.php:81
msgid "Static file type links to be replaced by CDN links."
msgstr "Links de tipos de arquivos estáticos a serem substituídos por links de CDN."

#. translators: %1$s: Example query string, %2$s: Example wildcard
#: tpl/cache/settings_inc.cache_dropquery.tpl.php:34
msgid "For example, to drop parameters beginning with %1$s, %2$s can be used here."
msgstr "Por exemplo, para remover parâmetros começando com %1$s, %2$s pode ser usado aqui."

#: src/lang.cls.php:111
msgid "Drop Query String"
msgstr "Remover string de consulta"

#: tpl/cache/settings-advanced.tpl.php:57
msgid "Enable this option if you are using both HTTP and HTTPS in the same domain and are noticing cache irregularities."
msgstr "Ative esta opção se você estiver usando tanto HTTP quanto HTTPS no mesmo domínio e estiver percebendo irregularidades no cache."

#: src/lang.cls.php:221
msgid "Improve HTTP/HTTPS Compatibility"
msgstr "Melhorar a compatibilidade HTTP/HTTPS"

#: tpl/img_optm/summary.tpl.php:365
msgid "Remove all previous image optimization requests/results, revert completed optimizations, and delete all optimization files."
msgstr "Remover todas as solicitações/resultados anteriores de otimização de imagem, reverter as otimizações concluídas e excluir todos os arquivos de otimização."

#: tpl/img_optm/settings.media_webp.tpl.php:26 tpl/img_optm/summary.tpl.php:361
msgid "Destroy All Optimization Data"
msgstr "Remover todos os dados de otimização"

#: tpl/img_optm/summary.tpl.php:296
msgid "Scan for any new unoptimized image thumbnail sizes and resend necessary image optimization requests."
msgstr "Verifique se há novos tamanhos de miniaturas de imagens não otimizadas e reenvie as solicitações de otimização de imagem necessárias."

#: tpl/img_optm/settings.tpl.php:87
msgid "This will increase the size of optimized files."
msgstr "Isso aumentará o tamanho dos arquivos otimizados."

#: tpl/img_optm/settings.tpl.php:86
msgid "Preserve EXIF data (copyright, GPS, comments, keywords, etc) when optimizing."
msgstr "Preservar dados EXIF (direitos autorais, GPS, comentários, palavras-chave, etc) ao otimizar."

#: tpl/toolbox/log_viewer.tpl.php:79
msgid "Clear Logs"
msgstr "Limpar registros"

#: thirdparty/woocommerce.content.tpl.php:24
msgid "To test the cart, visit the <a %s>FAQ</a>."
msgstr "Para testar o carrinho, visite <a %s>Perguntas frequentes</a>."

#: src/utility.cls.php:231 tpl/dash/dashboard.tpl.php:434
#: tpl/dash/dashboard.tpl.php:503
msgid " %s ago"
msgstr " %s atrás"

#: src/media.cls.php:380
msgid "WebP saved %s"
msgstr "WebP salvo %s"

#: tpl/toolbox/report.tpl.php:60
msgid "If you run into any issues, please refer to the report number in your support message."
msgstr "Se você encontrar algum problema, consulte o número do relatório em sua mensagem de suporte."

#: tpl/img_optm/summary.tpl.php:149
msgid "Last pull initiated by cron at %s."
msgstr "Última recuperação iniciada pelo cron às %s."

#: tpl/img_optm/summary.tpl.php:85
msgid "Images will be pulled automatically if the cron job is running."
msgstr "As imagens serão recuperadas automaticamente se a tarefa cron estiver em execução."

#: tpl/img_optm/summary.tpl.php:85
msgid "Only press the button if the pull cron job is disabled."
msgstr "Pressione o botão apenas se a tarefa cron de recuperação estiver desativada."

#: tpl/img_optm/summary.tpl.php:94
msgid "Pull Images"
msgstr "Recuperar imagens"

#: tpl/img_optm/summary.tpl.php:134
msgid "This process is automatic."
msgstr "Esse processo é automático."

#: tpl/dash/dashboard.tpl.php:575 tpl/img_optm/summary.tpl.php:314
msgid "Last Request"
msgstr "Última solicitação"

#: tpl/dash/dashboard.tpl.php:547 tpl/img_optm/summary.tpl.php:311
msgid "Images Pulled"
msgstr "Imagens recuperadas"

#: tpl/toolbox/entry.tpl.php:20
msgid "Report"
msgstr "Relatório"

#: tpl/toolbox/report.tpl.php:126
msgid "Send this report to LiteSpeed. Refer to this report number when posting in the WordPress support forum."
msgstr "Enviar este relatório para o LiteSpeed. Faça referência a este número de relatório ao publicar no fórum de suporte do WordPress."

#: tpl/toolbox/report.tpl.php:30
msgid "Send to LiteSpeed"
msgstr "Enviar para o LiteSpeed"

#: src/media.cls.php:258
msgid "LiteSpeed Optimization"
msgstr "Otimização LiteSpeed"

#: src/lang.cls.php:166
msgid "Load Google Fonts Asynchronously"
msgstr "Carregar Google Fonts de forma assíncrona"

#: src/lang.cls.php:98
msgid "Browser Cache TTL"
msgstr "TTL do cache do navegador"

#: tpl/img_optm/summary.tpl.php:325
msgid "Results can be checked in <a %s>Media Library</a>."
msgstr "Os resultados podem ser verificados na <a %s>Biblioteca de mídia</a>."

#: src/doc.cls.php:88 src/doc.cls.php:140 tpl/cdn/cf.tpl.php:139
#: tpl/dash/dashboard.tpl.php:179 tpl/dash/dashboard.tpl.php:831
#: tpl/general/online.tpl.php:67 tpl/general/online.tpl.php:79
#: tpl/general/online.tpl.php:95 tpl/img_optm/summary.tpl.php:51
#: tpl/inc/check_cache_disabled.php:42
msgid "Learn More"
msgstr "Saber mais"

#: tpl/img_optm/summary.tpl.php:275
msgid "Image groups total"
msgstr "Total de grupos de imagens"

#: src/lang.cls.php:29
msgid "Images optimized and pulled"
msgstr "Imagens otimizadas e recuperadas"

#: src/lang.cls.php:27 tpl/dash/dashboard.tpl.php:555
msgid "Images requested"
msgstr "Imagens solicitadas"

#: src/img-optm.cls.php:2001 src/img-optm.cls.php:2061
msgid "Switched to optimized file successfully."
msgstr "Alternado para o arquivo otimizado."

#: src/img-optm.cls.php:2055
msgid "Restored original file successfully."
msgstr "Arquivo original restaurado."

#: src/img-optm.cls.php:2025
msgid "Enabled WebP file successfully."
msgstr "Arquivo WebP ativado."

#: src/img-optm.cls.php:2020
msgid "Disabled WebP file successfully."
msgstr "Arquivo WebP desativado."

#: tpl/img_optm/settings.media_webp.tpl.php:18
msgid "Significantly improve load time by replacing images with their optimized %s versions."
msgstr "Melhorar significativamente o tempo de carregamento substituindo imagens por suas versões otimizadas em %s."

#: tpl/cache/settings-excludes.tpl.php:135
msgid "Selected roles will be excluded from cache."
msgstr "As funções selecionadas serão excluídas do cache."

#: tpl/general/entry.tpl.php:10 tpl/page_optm/entry.tpl.php:14
#: tpl/page_optm/entry.tpl.php:15
msgid "Tuning"
msgstr "Ajustes"

#: tpl/page_optm/settings_tuning.tpl.php:147
msgid "Selected roles will be excluded from all optimizations."
msgstr "As funções selecionadas serão excluídas de todas as otimizações."

#: src/lang.cls.php:178
msgid "Role Excludes"
msgstr "Exclusões de funções"

#: tpl/general/settings_tuning.tpl.php:9
#: tpl/page_optm/settings_tuning.tpl.php:20
#: tpl/page_optm/settings_tuning_css.tpl.php:8
msgid "Tuning Settings"
msgstr "Configurações de ajuste"

#: tpl/cache/settings-excludes.tpl.php:106
msgid "If the tag slug is not found, the tag will be removed from the list on save."
msgstr "Se o slug da tag não for encontrado, a tag será removida da lista ao salvar."

#: tpl/cache/settings-excludes.tpl.php:73
msgid "If the category name is not found, the category will be removed from the list on save."
msgstr "Se o nome da categoria não for encontrado, a categoria será removida da lista ao salvar."

#: tpl/img_optm/summary.tpl.php:133
msgid "After the QUIC.cloud Image Optimization server finishes optimization, it will notify your site to pull the optimized images."
msgstr "Após o servidor de otimização de imagens QUIC.cloud concluir a otimização, ele notificará o seu site para recuperar as imagens otimizadas."

#: tpl/dash/dashboard.tpl.php:538 tpl/img_optm/summary.tpl.php:68
#: tpl/img_optm/summary.tpl.php:81
msgid "Send Optimization Request"
msgstr "Enviar solicitação de otimização"

#: tpl/img_optm/summary.tpl.php:266
msgid "Image Information"
msgstr "Informações de imagens"

#: tpl/dash/dashboard.tpl.php:544 tpl/img_optm/summary.tpl.php:308
msgid "Total Reduction"
msgstr "Redução total"

#: tpl/img_optm/summary.tpl.php:305
msgid "Optimization Summary"
msgstr "Resumo da otimização"

#: tpl/img_optm/entry.tpl.php:23
msgid "LiteSpeed Cache Image Optimization"
msgstr "Otimização de imagens do LiteSpeed Cache"

#: src/admin-display.cls.php:132 src/gui.cls.php:629
#: tpl/dash/dashboard.tpl.php:190 tpl/dash/network_dash.tpl.php:28
#: tpl/general/online.tpl.php:121 tpl/general/online.tpl.php:136
#: tpl/presets/standard.tpl.php:25
msgid "Image Optimization"
msgstr "Otimização de imagem"

#: tpl/page_optm/settings_media.tpl.php:52
msgid "For example, %s can be used for a transparent placeholder."
msgstr "Por exemplo, %s pode ser usado para um marcador de posição transparente."

#: tpl/page_optm/settings_media.tpl.php:51
msgid "By default a gray image placeholder %s will be used."
msgstr "Por padrão, um marcador de posição de imagem em cinza %s será usado."

#: tpl/page_optm/settings_media.tpl.php:50
msgid "This can be predefined in %2$s as well using constant %1$s, with this setting taking priority."
msgstr "Isso também pode ser predefinido em %2$s usando a constante %1$s, com esta configuração tendo prioridade."

#: tpl/page_optm/settings_media.tpl.php:49
msgid "Specify a base64 image to be used as a simple placeholder while images finish loading."
msgstr "Especifique uma imagem em base64 para ser usada como marcador de posição simples enquanto as imagens terminam de carregar."

#: tpl/page_optm/settings_media_exc.tpl.php:29
#: tpl/page_optm/settings_tuning.tpl.php:61
#: tpl/page_optm/settings_tuning.tpl.php:82
#: tpl/page_optm/settings_tuning.tpl.php:103
#: tpl/page_optm/settings_tuning_css.tpl.php:27
msgid "Elements with attribute %s in html code will be excluded."
msgstr "Elementos com o atributo %s no código HTML serão excluídos."

#: tpl/cache/settings-esi.tpl.php:106
#: tpl/page_optm/settings_media_exc.tpl.php:28
#: tpl/page_optm/settings_tuning.tpl.php:40
#: tpl/page_optm/settings_tuning.tpl.php:60
#: tpl/page_optm/settings_tuning.tpl.php:81
#: tpl/page_optm/settings_tuning.tpl.php:102
#: tpl/page_optm/settings_tuning.tpl.php:121
#: tpl/page_optm/settings_tuning_css.tpl.php:26
#: tpl/page_optm/settings_tuning_css.tpl.php:87
msgid "Filter %s is supported."
msgstr "O filtro %s é suportado."

#: tpl/page_optm/settings_media_exc.tpl.php:22
msgid "Listed images will not be lazy loaded."
msgstr "As imagens listadas não serão carregadas tardiamente."

#: src/lang.cls.php:185
msgid "Lazy Load Image Excludes"
msgstr "Exclusões de imagens para carregamento tardio"

#: src/gui.cls.php:461
msgid "No optimization"
msgstr "Sem otimização"

#: tpl/page_optm/settings_tuning.tpl.php:117
msgid "Prevent any optimization of listed pages."
msgstr "Impedir qualquer otimização das páginas listadas."

#: src/lang.cls.php:176
msgid "URI Excludes"
msgstr "Exclusões de URI"

#: tpl/page_optm/settings_html.tpl.php:166
msgid "Stop loading WordPress.org emoji. Browser default emoji will be displayed instead."
msgstr "Interromper o carregamento dos emojis do WordPress.org. Em vez disso, os emojis padrão do navegador serão exibidos."

#: src/doc.cls.php:126
msgid "Both full URLs and partial strings can be used."
msgstr "Podem ser usados URLs completos e strings parciais."

#: tpl/page_optm/settings_media.tpl.php:225
msgid "Load iframes only when they enter the viewport."
msgstr "Carregar iframes apenas quando entrarem na janela de visualização (viewport)."

#: src/lang.cls.php:201
msgid "Lazy Load Iframes"
msgstr "Carregamento tardio de iframes"

#: tpl/page_optm/settings_media.tpl.php:31
#: tpl/page_optm/settings_media.tpl.php:226
msgid "This can improve page loading time by reducing initial HTTP requests."
msgstr "Isso pode melhorar o tempo de carregamento da página ao reduzir as solicitações HTTP iniciais."

#: tpl/page_optm/settings_media.tpl.php:30
msgid "Load images only when they enter the viewport."
msgstr "Carregar imagens apenas quando entrarem na janela de visualização (viewport)."

#: src/lang.cls.php:184
msgid "Lazy Load Images"
msgstr "Carregamento tardio de imagens"

#: tpl/page_optm/entry.tpl.php:10 tpl/page_optm/settings_media.tpl.php:16
msgid "Media Settings"
msgstr "Configurações de mídia"

#: tpl/cache/settings-excludes.tpl.php:46
msgid "For example, for %1$s, %2$s and %3$s can be used here."
msgstr "Por exemplo, para %1$s, %2$s e %3$s podem ser usados aqui."

#: tpl/cache/settings-esi.tpl.php:117 tpl/cache/settings-purge.tpl.php:111
#: tpl/cdn/other.tpl.php:128
msgid "Wildcard %1$s supported (match zero or more characters). For example, to match %2$s and %3$s, use %4$s."
msgstr "Caractere curinga %1$s é suportado (corresponde a zero ou mais caracteres). Por exemplo, para corresponder a %2$s e %3$s, use %4$s."

#: src/admin-display.cls.php:1229
msgid "To match the beginning, add %s to the beginning of the item."
msgstr "Para corresponder ao início, adicione %s no início do item."

#: src/admin-display.cls.php:1227
msgid "For example, for %1$s, %2$s can be used here."
msgstr "Por exemplo, para %1$s, %2$s pode ser usado aqui."

#: tpl/banner/score.php:115
msgid "Maybe later"
msgstr "Talvez mais tarde"

#: tpl/banner/score.php:114
msgid "I've already left a review"
msgstr "Já deixei uma avaliação"

#: tpl/banner/slack.php:20
msgid "Welcome to LiteSpeed"
msgstr "Boas-vindas ao LiteSpeed"

#: src/lang.cls.php:174 tpl/presets/standard.tpl.php:45
msgid "Remove WordPress Emoji"
msgstr "Remover emojis do WordPress"

#: src/gui.cls.php:469
msgid "More settings"
msgstr "Mais configurações"

#: src/gui.cls.php:450
msgid "Private cache"
msgstr "Cache privado"

#: src/gui.cls.php:439
msgid "Non cacheable"
msgstr "Não armazenável em cache"

#: src/gui.cls.php:416
msgid "Mark this page as "
msgstr "Marcar esta página como "

#: src/gui.cls.php:392 src/gui.cls.php:407
msgid "Purge this page"
msgstr "Limpar esta página"

#: src/lang.cls.php:156
msgid "Load JS Deferred"
msgstr "Carregar JS de forma adiada"

#: tpl/page_optm/settings_tuning_css.tpl.php:158
msgid "Specify critical CSS rules for above-the-fold content when enabling %s."
msgstr "Especifique regras de CSS críticas para o conteúdo acima da dobra ao ativar %s."

#: src/lang.cls.php:168
msgid "Critical CSS Rules"
msgstr "Regras de CSS crítico"

#: src/lang.cls.php:152 tpl/page_optm/settings_tuning_css.tpl.php:158
msgid "Load CSS Asynchronously"
msgstr "Carregar CSS de forma assíncrona"

#: tpl/page_optm/settings_html.tpl.php:153
msgid "Prevent Google Fonts from loading on all pages."
msgstr "Impedir o carregamento do Google Fonts em todas as páginas."

#: src/lang.cls.php:167
msgid "Remove Google Fonts"
msgstr "Remover Google Fonts"

#: tpl/page_optm/settings_css.tpl.php:217
#: tpl/page_optm/settings_html.tpl.php:167 tpl/page_optm/settings_js.tpl.php:73
msgid "This can improve your speed score in services like Pingdom, GTmetrix and PageSpeed."
msgstr "Isso pode melhorar sua pontuação de velocidade em serviços como Pingdom, GTmetrix e PageSpeed."

#: tpl/page_optm/settings_html.tpl.php:115
msgid "Remove query strings from internal static resources."
msgstr "Remover strings de consulta de recursos estáticos internos."

#: src/lang.cls.php:165
msgid "Remove Query Strings"
msgstr "Remover strings de consulta"

#: tpl/cache/settings_inc.exclude_useragent.tpl.php:28
msgid "user agents"
msgstr "agentes de usuário"

#: tpl/cache/settings_inc.exclude_cookies.tpl.php:28
msgid "cookies"
msgstr "cookies"

#. translators: %1$s: Opening link tag, %2$s: Closing link tag
#: tpl/cache/settings_inc.browser.tpl.php:46
msgid "You can turn on browser caching in server admin too. %1$sLearn more about LiteSpeed browser cache settings%2$s."
msgstr "Você também pode ativar o cache do navegador no painel administrativo do servidor. %1$sSaiba mais sobre as configurações de cache do navegador LiteSpeed%2$s."

#: tpl/cache/settings_inc.browser.tpl.php:41
msgid "Browser caching stores static files locally in the user's browser. Turn on this setting to reduce repeated requests for static files."
msgstr "O cache do navegador armazena localmente arquivos estáticos no navegador do usuário. Ative essa configuração para reduzir as solicitações repetidas de arquivos estáticos."

#: src/lang.cls.php:91 tpl/dash/dashboard.tpl.php:57
#: tpl/dash/dashboard.tpl.php:614 tpl/presets/standard.tpl.php:13
msgid "Browser Cache"
msgstr "Cache do navegador"

#: tpl/cache/settings-excludes.tpl.php:100
msgid "tags"
msgstr "tags"

#: src/lang.cls.php:136
msgid "Do Not Cache Tags"
msgstr "Não armazenar tags em cache"

#: tpl/cache/settings-excludes.tpl.php:110
msgid "To exclude %1$s, insert %2$s."
msgstr "Para excluir %1$s, insira %2$s."

#: tpl/cache/settings-excludes.tpl.php:67
msgid "categories"
msgstr "categorias"

#. translators: %s: "cookies"
#. translators: %s: "user agents"
#: tpl/cache/settings-excludes.tpl.php:67
#: tpl/cache/settings-excludes.tpl.php:100
#: tpl/cache/settings_inc.exclude_cookies.tpl.php:27
#: tpl/cache/settings_inc.exclude_useragent.tpl.php:27
msgid "To prevent %s from being cached, enter them here."
msgstr "Para impedir que %s sejam armazenados(as) em cache, digite aqui."

#: src/lang.cls.php:135
msgid "Do Not Cache Categories"
msgstr "Não armazenar categorias em cache"

#: tpl/cache/settings-excludes.tpl.php:45
msgid "Query strings containing these parameters will not be cached."
msgstr "As strings de consulta contendo esses parâmetros não serão armazenadas em cache."

#: src/lang.cls.php:134
msgid "Do Not Cache Query Strings"
msgstr "Não armazenar strings de consulta em cache"

#: tpl/cache/settings-excludes.tpl.php:30
msgid "Paths containing these strings will not be cached."
msgstr "Os caminhos contendo essas strings não serão armazenados em cache."

#: src/lang.cls.php:133
msgid "Do Not Cache URIs"
msgstr "Não armazenar URIs em cache"

#: src/admin-display.cls.php:1231 src/doc.cls.php:109
msgid "One per line."
msgstr "Um(a) por linha."

#: tpl/cache/settings-cache.tpl.php:119
msgid "URI Paths containing these strings will NOT be cached as public."
msgstr "Caminhos de URI contendo essas strings NÃO serão armazenados em cache como públicos."

#: src/lang.cls.php:110
msgid "Private Cached URIs"
msgstr "URIs em cache privado"

#: tpl/cdn/other.tpl.php:164
msgid "Paths containing these strings will not be served from the CDN."
msgstr "Caminhos contendo essas strings não serão servidos a partir do CDN."

#: src/lang.cls.php:244
msgid "Exclude Path"
msgstr "Excluir caminho"

#: tpl/cdn/other.tpl.php:83
msgid "This will affect all tags containing attributes: %1$s %2$s %3$s."
msgstr "Isso afetará todas as tags que contém os atributos: %1$s %2$s %3$s."

#: src/lang.cls.php:240 tpl/cdn/other.tpl.php:87
msgid "Include File Types"
msgstr "Incluir tipos de arquivos"

#: tpl/cdn/other.tpl.php:77
msgid "Serve all JavaScript files through the CDN. This will affect all enqueued WP JavaScript files."
msgstr "Sirva todos os arquivos JavaScript por meio do CDN. Isso afetará todos os arquivos JavaScript WP enfileirados."

#: src/lang.cls.php:239
msgid "Include JS"
msgstr "Incluir JS"

#: tpl/cdn/other.tpl.php:73
msgid "Serve all CSS files through the CDN. This will affect all enqueued WP CSS files."
msgstr "Sirva todos os arquivos de CSS por meio do CDN. Isso afetará todos os arquivos de CSS WP enfileirados."

#: src/lang.cls.php:238
msgid "Include CSS"
msgstr "Incluir CSS"

#: tpl/cdn/other.tpl.php:69
msgid "Serve all image files through the CDN. This will affect all attachments, HTML %1$s tags, and CSS %2$s attributes."
msgstr "Sirva todos os arquivos de imagem por meio do CDN. Isso afetará todos os anexos, tags HTML %1$s e atributos CSS %2$s."

#: src/lang.cls.php:237
msgid "Include Images"
msgstr "Incluir imagens"

#: src/admin-display.cls.php:237
msgid "CDN URL to be used. For example, %s"
msgstr "URL do CDN a ser usado. Exemplo: %s"

#: src/lang.cls.php:236
msgid "CDN URL"
msgstr "URL do CDN"

#: tpl/cdn/other.tpl.php:127
msgid "Site URL to be served through the CDN. Beginning with %1$s. For example, %2$s."
msgstr "URL do site a ser servido através do CDN. Começando com %1$s. Por exemplo, %2$s."

#: src/lang.cls.php:242
msgid "Original URLs"
msgstr "URLs originais"

#: tpl/cdn/other.tpl.php:20
msgid "CDN Settings"
msgstr "Configurações de CDN"

#: src/admin-display.cls.php:130
msgid "CDN"
msgstr "CDN"

#: src/admin-display.cls.php:242 src/admin-display.cls.php:944
#: src/admin-display.cls.php:970 src/admin-display.cls.php:1019
#: tpl/cache/settings-cache.tpl.php:28
#: tpl/cache/settings_inc.object.tpl.php:280 tpl/cdn/other.tpl.php:39
#: tpl/img_optm/settings.media_webp.tpl.php:14
#: tpl/page_optm/settings_css.tpl.php:87 tpl/page_optm/settings_js.tpl.php:69
#: tpl/page_optm/settings_media.tpl.php:170
#: tpl/toolbox/settings-debug.tpl.php:49
msgid "OFF"
msgstr "DESATIVADO"

#: src/admin-display.cls.php:241 src/admin-display.cls.php:943
#: src/admin-display.cls.php:970 src/admin-display.cls.php:1019
#: src/doc.cls.php:40 tpl/cache/settings-cache.tpl.php:28
#: tpl/cache/settings_inc.cache_mobile.tpl.php:91 tpl/cdn/other.tpl.php:34
#: tpl/crawler/settings.tpl.php:114 tpl/page_optm/settings_css.tpl.php:221
#: tpl/page_optm/settings_media.tpl.php:166
#: tpl/toolbox/settings-debug.tpl.php:49
msgid "ON"
msgstr "ATIVADO"

#: src/purge.cls.php:378
msgid "Notified LiteSpeed Web Server to purge CSS/JS entries."
msgstr "Servidor Web LiteSpeed notificado para limpar entradas CSS/JS."

#: tpl/page_optm/settings_html.tpl.php:23
msgid "Minify HTML content."
msgstr "Minificar conteúdo HTML."

#: src/lang.cls.php:149
msgid "HTML Minify"
msgstr "Minificar HTML"

#: src/lang.cls.php:164
msgid "JS Excludes"
msgstr "Exclusões de JS"

#: src/data.upgrade.func.php:235 src/lang.cls.php:147
msgid "JS Combine"
msgstr "Combinar JS"

#: src/lang.cls.php:146
msgid "JS Minify"
msgstr "Minificar JS"

#: src/lang.cls.php:162
msgid "CSS Excludes"
msgstr "Exclusões de CSS"

#: src/lang.cls.php:139
msgid "CSS Combine"
msgstr "Combinar CSS"

#: src/lang.cls.php:138
msgid "CSS Minify"
msgstr "Minificar CSS"

#: tpl/page_optm/entry.tpl.php:34
msgid "Please test thoroughly when enabling any option in this list. After changing Minify/Combine settings, please do a Purge All action."
msgstr "Teste minuciosamente ao ativar qualquer opção nesta lista. Após alterar as configurações de Minificar/Combinar, execute uma ação de \"Limpar tudo\"."

#: tpl/toolbox/purge.tpl.php:37
msgid "This will purge all minified/combined CSS/JS entries only"
msgstr "Isso irá limpar todas as entradas de CSS/JS minificadas/combinadas apenas"

#: tpl/toolbox/purge.tpl.php:23
msgid "Purge %s Error"
msgstr "Limpar erro %s"

#: tpl/db_optm/manage.tpl.php:78
msgid "Database Optimizer"
msgstr "Otimizador de banco de dados"

#: tpl/db_optm/manage.tpl.php:50
msgid "Optimize all tables in your database"
msgstr "Otimizar todas as tabelas em seu banco de dados"

#: tpl/db_optm/manage.tpl.php:49
msgid "Optimize Tables"
msgstr "Otimizar tabelas"

#: tpl/db_optm/manage.tpl.php:46
msgid "Clean all transient options"
msgstr "Limpar todas as opções de transientes"

#: tpl/db_optm/manage.tpl.php:45
msgid "All Transients"
msgstr "Todos os transientes"

#: tpl/db_optm/manage.tpl.php:42
msgid "Clean expired transient options"
msgstr "Limpar opções de transientes expirados"

#: tpl/db_optm/manage.tpl.php:41
msgid "Expired Transients"
msgstr "Transientes expirados"

#: tpl/db_optm/manage.tpl.php:38
msgid "Clean all trackbacks and pingbacks"
msgstr "Limpar todos os trackbacks e pingbacks"

#: tpl/db_optm/manage.tpl.php:37
msgid "Trackbacks/Pingbacks"
msgstr "Trackbacks/Pingbacks"

#: tpl/db_optm/manage.tpl.php:34
msgid "Clean all trashed comments"
msgstr "Limpar todos os comentários da lixeira"

#: tpl/db_optm/manage.tpl.php:33
msgid "Trashed Comments"
msgstr "Comentários na lixeira"

#: tpl/db_optm/manage.tpl.php:30
msgid "Clean all spam comments"
msgstr "Limpar todos os comentários de spam"

#: tpl/db_optm/manage.tpl.php:29
msgid "Spam Comments"
msgstr "Comentários de spam"

#: tpl/db_optm/manage.tpl.php:26
msgid "Clean all trashed posts and pages"
msgstr "Limpar todos os posts e páginas da lixeira"

#: tpl/db_optm/manage.tpl.php:25
msgid "Trashed Posts"
msgstr "Posts na lixeira"

#: tpl/db_optm/manage.tpl.php:22
msgid "Clean all auto saved drafts"
msgstr "Limpar todos os rascunhos automáticos salvos"

#: tpl/db_optm/manage.tpl.php:21
msgid "Auto Drafts"
msgstr "Rascunhos automáticos"

#: tpl/db_optm/manage.tpl.php:14
msgid "Clean all post revisions"
msgstr "Limpar todas as revisões de posts"

#: tpl/db_optm/manage.tpl.php:13
msgid "Post Revisions"
msgstr "Revisões de posts"

#: tpl/db_optm/manage.tpl.php:9
msgid "Clean All"
msgstr "Limpar tudo"

#: src/db-optm.cls.php:242
msgid "Optimized all tables."
msgstr "Todas as tabelas otimizadas."

#: src/db-optm.cls.php:232
msgid "Clean all transients successfully."
msgstr "Todos os transientes foram limpos."

#: src/db-optm.cls.php:228
msgid "Clean expired transients successfully."
msgstr "Transientes expirados limpos."

#: src/db-optm.cls.php:224
msgid "Clean trackbacks and pingbacks successfully."
msgstr "Trackbacks e pingbacks limpos."

#: src/db-optm.cls.php:220
msgid "Clean trashed comments successfully."
msgstr "Comentários excluídos limpos."

#: src/db-optm.cls.php:216
msgid "Clean spam comments successfully."
msgstr "Comentários de spam limpos."

#: src/db-optm.cls.php:212
msgid "Clean trashed posts and pages successfully."
msgstr "Posts e páginas excluídas limpos."

#: src/db-optm.cls.php:208
msgid "Clean auto drafts successfully."
msgstr "Rascunhos automáticos limpos."

#: src/db-optm.cls.php:200
msgid "Clean post revisions successfully."
msgstr "Revisões de posts limpas."

#: src/db-optm.cls.php:143
msgid "Clean all successfully."
msgstr "Tudo limpo."

#: src/lang.cls.php:93
msgid "Default Private Cache TTL"
msgstr "TTL padrão de cache privado"

#: tpl/cache/settings-esi.tpl.php:145
msgid "If your site contains public content that certain user roles can see but other roles cannot, you can specify a Vary Group for those user roles. For example, specifying an administrator vary group allows there to be a separate publicly-cached page tailored to administrators (with “edit” links, etc), while all other user roles see the default public page."
msgstr "Se o seu site contém conteúdo público que determinadas funções de usuário podem ver, mas outras não, você pode especificar um \"Grupo de variação\" para essas funções de usuário. Por exemplo, especificar um grupo de variação para administradores permite que haja uma página separada publicamente armazenada em cache, adaptada para administradores (com links de \"editar\", etc.), enquanto todas as outras funções de usuário veem a página pública padrão."

#: src/lang.cls.php:219 tpl/page_optm/settings_css.tpl.php:139
#: tpl/page_optm/settings_css.tpl.php:283 tpl/page_optm/settings_vpi.tpl.php:86
msgid "Vary Group"
msgstr "Grupo de variação"

#: tpl/cache/settings-esi.tpl.php:85
msgid "Cache the built-in Comment Form ESI block."
msgstr "Armazena em cache o bloco ESI do formulário de comentários integrado."

#: src/lang.cls.php:217
msgid "Cache Comment Form"
msgstr "Cache do formulário de comentários"

#: tpl/cache/settings-esi.tpl.php:72
msgid "Cache the built-in Admin Bar ESI block."
msgstr "Armazena em cache o bloco ESI da barra administrativa integrada."

#: src/lang.cls.php:216
msgid "Cache Admin Bar"
msgstr "Cache da barra de administração"

#: tpl/cache/settings-esi.tpl.php:59
msgid "Turn ON to cache public pages for logged in users, and serve the Admin Bar and Comment Form via ESI blocks. These two blocks will be uncached unless enabled below."
msgstr "ATIVAR para armazenar em cache as páginas públicas para usuários conectados e servir a barra de administração e o formulário de comentários por meio de blocos ESI. Esses dois blocos não serão armazenados em cache, a menos que estejam ATIVADOS abaixo."

#: tpl/cache/settings-esi.tpl.php:21
msgid "ESI allows you to designate parts of your dynamic page as separate fragments that are then assembled together to make the whole page. In other words, ESI lets you “punch holes” in a page, and then fill those holes with content that may be cached privately, cached publicly with its own TTL, or not cached at all."
msgstr "ESI permite que você designe partes da sua página dinâmica como fragmentos separados, que são então montados para criar a página inteira. Em outras palavras, o ESI permite que você \"abra espaços\" em uma página e, em seguida, preencha esses espaços com conteúdo que pode ser armazenado em cache de forma privada, armazenado em cache publicamente com seu próprio tempo de vida (TTL) ou não armazenado em cache de forma alguma."

#: tpl/cache/settings-esi.tpl.php:20
msgid "With ESI (Edge Side Includes), pages may be served from cache for logged-in users."
msgstr "Com ESI (Edge Side Includes), as páginas podem ser servidas em cache para usuários conectados."

#: tpl/esi_widget_edit.php:47
msgid "Private"
msgstr "Privado"

#: tpl/esi_widget_edit.php:46
msgid "Public"
msgstr "Público"

#: tpl/cache/network_settings-purge.tpl.php:17
#: tpl/cache/settings-purge.tpl.php:15
msgid "Purge Settings"
msgstr "Configurações de limpeza"

#: src/lang.cls.php:108 tpl/cache/settings_inc.cache_mobile.tpl.php:90
msgid "Cache Mobile"
msgstr "Cache móvel"

#: tpl/toolbox/settings-debug.tpl.php:88
msgid "Advanced level will log more details."
msgstr "O nível avançado irá registrar mais detalhes."

#: tpl/presets/standard.tpl.php:22 tpl/toolbox/settings-debug.tpl.php:86
msgid "Basic"
msgstr "Básico"

#: tpl/crawler/settings.tpl.php:68
msgid "The maximum average server load allowed while crawling. The number of crawler threads in use will be actively reduced until average server load falls under this limit. If this cannot be achieved with a single thread, the current crawler run will be terminated."
msgstr "A carga média máxima permitida no servidor durante o rastreamento. O número de threads do rastreador em uso será reduzido ativamente até que a carga média do servidor caia abaixo desse limite. Se isso não puder ser alcançado com uma única thread, a execução atual do rastreador será encerrada."

#: src/lang.cls.php:107
msgid "Cache Login Page"
msgstr "Cache da página de acesso"

#: tpl/cache/settings-cache.tpl.php:89
msgid "Cache requests made by WordPress REST API calls."
msgstr "Solicitações de cache feitas por chamadas à API REST do WordPress."

#: src/lang.cls.php:106
msgid "Cache REST API"
msgstr "Cache da API REST"

#: tpl/cache/settings-cache.tpl.php:76
msgid "Privately cache commenters that have pending comments. Disabling this option will serve non-cacheable pages to commenters. (LSWS %s required)"
msgstr "Armazenar em cache de forma privada os comentaristas que têm comentários pendentes. Desativar esta opção exibirá páginas não armazenáveis para os comentaristas. (Requer LSWS %s)"

#: src/lang.cls.php:105
msgid "Cache Commenters"
msgstr "Cache de comentaristas"

#: tpl/cache/settings-cache.tpl.php:63
msgid "Privately cache frontend pages for logged-in users. (LSWS %s required)"
msgstr "Armazenar em cache de forma privada as páginas de interface para usuários conectados. (Requer LSWS %s)"

#: src/lang.cls.php:104
msgid "Cache Logged-in Users"
msgstr "Cache de usuários conectados"

#: tpl/cache/network_settings-cache.tpl.php:17
#: tpl/cache/settings-cache.tpl.php:15
msgid "Cache Control Settings"
msgstr "Configurações de controle de cache"

#: tpl/cache/entry.tpl.php:20
msgid "ESI"
msgstr "ESI"

#: tpl/cache/entry.tpl.php:19 tpl/cache/entry_network.tpl.php:18
msgid "Excludes"
msgstr "Exclusões"

#: tpl/cache/entry.tpl.php:18 tpl/cache/entry_network.tpl.php:17
#: tpl/toolbox/entry.tpl.php:7 tpl/toolbox/purge.tpl.php:134
msgid "Purge"
msgstr "Limpeza"

#: src/admin-display.cls.php:128 tpl/cache/entry.tpl.php:16
#: tpl/cache/entry_network.tpl.php:16
msgid "Cache"
msgstr "Cache"

#: thirdparty/woocommerce.tab.tpl.php:3
msgid "WooCommerce"
msgstr "WooCommerce"

#: tpl/inc/show_rule_conflict.php:7
msgid "Unexpected cache rule %2$s found in %1$s file. This rule may cause visitors to see old versions of pages due to the browser caching HTML pages. If you are sure that HTML pages are not being browser cached, this message can be dismissed. (<a %3$s>Learn More</a>)"
msgstr "Regra de cache inesperada %2$s encontrada no arquivo %1$s. Essa regra pode fazer com que os visitantes vejam versões antigas das páginas devido ao cache do navegador de páginas HTML. Se você tem certeza de que as páginas HTML não estão sendo armazenadas em cache no navegador, esta mensagem pode ser ignorada. (<a %3$s>Saiba mais</a>)"

#: tpl/cache/settings-purge.tpl.php:132
msgid "Current server time is %s."
msgstr "A hora atual do servidor é %s."

#: tpl/cache/settings-purge.tpl.php:131
msgid "Specify the time to purge the \"%s\" list."
msgstr "Especifique a hora para limpar a lista \"%s\"."

#: tpl/cache/settings-purge.tpl.php:107
msgid "Both %1$s and %2$s are acceptable."
msgstr "Ambos %1$s e %2$s são aceitáveis."

#: src/lang.cls.php:130 tpl/cache/settings-purge.tpl.php:106
msgid "Scheduled Purge Time"
msgstr "Horário de limpeza agendada"

#: tpl/cache/settings-purge.tpl.php:106
msgid "The URLs here (one per line) will be purged automatically at the time set in the option \"%s\"."
msgstr "Os URLs aqui (um por linha) serão automaticamente limpos no horário definido na opção \"%s\"."

#: src/lang.cls.php:129 tpl/cache/settings-purge.tpl.php:131
msgid "Scheduled Purge URLs"
msgstr "URLs de limpeza agendada"

#: tpl/toolbox/settings-debug.tpl.php:116
msgid "Shorten query strings in the debug log to improve readability."
msgstr "Reduzir os parâmetros da consulta no registro de depuração para melhorar a legibilidade."

#: tpl/toolbox/entry.tpl.php:19
msgid "Heartbeat"
msgstr "Heartbeat"

#: tpl/toolbox/settings-debug.tpl.php:99
msgid "MB"
msgstr "MB"

#: src/lang.cls.php:259
msgid "Log File Size Limit"
msgstr "Limite de tamanho do arquivo de registro"

#: src/htaccess.cls.php:787
msgid "<p>Please add/replace the following codes into the beginning of %1$s:</p> %2$s"
msgstr "<p>Adicione/substitua os seguintes códigos no início de %1$s:</p> %2$s"

#: src/error.cls.php:139 src/error.cls.php:163
msgid "%s file not writable."
msgstr "%s arquivo não gravável."

#: src/error.cls.php:159
msgid "%s file not readable."
msgstr "%s arquivo não legível."

#: src/lang.cls.php:260
msgid "Collapse Query Strings"
msgstr "Recolher strings de consulta"

#: tpl/cache/settings-esi.tpl.php:15
msgid "ESI Settings"
msgstr "Configurações ESI"

#: tpl/esi_widget_edit.php:74
msgid "A TTL of 0 indicates do not cache."
msgstr "Um TTL de 0 indica para não armazenar em cache."

#: tpl/esi_widget_edit.php:73
msgid "Recommended value: 28800 seconds (8 hours)."
msgstr "Valor recomendado: 28.800 segundos (8 horas)."

#: tpl/esi_widget_edit.php:63
msgid "Widget Cache TTL:"
msgstr "TTL do cache de widgets:"

#: src/lang.cls.php:215 tpl/esi_widget_edit.php:36
msgid "Enable ESI"
msgstr "Ativar ESI"

#: tpl/crawler/summary.tpl.php:55
msgid "See <a %s>Introduction for Enabling the Crawler</a> for detailed information."
msgstr "Consulte <a %s>Introdução para ativar o rastreador</a> para informações detalhadas."

#: src/lang.cls.php:253
msgid "Custom Sitemap"
msgstr "Sitemap personalizado"

#: tpl/toolbox/purge.tpl.php:284
msgid "Purge pages by relative or full URL."
msgstr "Limpar páginas por URL relativo ou completo."

#: tpl/crawler/summary.tpl.php:54
msgid "The crawler feature is not enabled on the LiteSpeed server. Please consult your server admin or hosting provider."
msgstr "O recurso de rastreador não está ativado no servidor LiteSpeed. Consulte o administrador do servidor ou o provedor de hospedagem."

#: tpl/cache/settings-esi.tpl.php:45 tpl/cdn/cf.tpl.php:95
#: tpl/crawler/summary.tpl.php:53 tpl/inc/check_cache_disabled.php:31
#: tpl/inc/check_if_network_disable_all.php:19
#: tpl/page_optm/settings_css.tpl.php:72 tpl/page_optm/settings_css.tpl.php:212
#: tpl/page_optm/settings_localization.tpl.php:12
msgid "WARNING"
msgstr "ATENÇÃO"

#: tpl/crawler/summary.tpl.php:116
msgid "<b>Last crawled:</b> %s item(s)"
msgstr "<b>Último rastreamento:</b> %s item(s)"

#: tpl/crawler/summary.tpl.php:68
msgid "The next complete sitemap crawl will start at"
msgstr "O próximo rastreamento completo do sitemap vai iniciar em"

#: src/file.cls.php:178
msgid "Failed to write to %s."
msgstr "Falha ao gravar em %s."

#: src/file.cls.php:161
msgid "Folder is not writable: %s."
msgstr "A pasta não é gravável: %s."

#: src/file.cls.php:153
msgid "Can not create folder: %1$s. Error: %2$s"
msgstr "Não é possível criar a pasta: %1$s. Erro: %2$s"

#: src/file.cls.php:141
msgid "Folder does not exist: %s"
msgstr "A pasta não existe: %s"

#: src/core.cls.php:332
msgid "Notified LiteSpeed Web Server to purge the list."
msgstr "O LiteSpeed Web Server foi notificado para limpar a lista com sucesso."

#. translators: %1$s: Opening link tag, %2$s: Closing link tag
#: tpl/cache/settings-cache.tpl.php:36
msgid "Please visit the %1$sInformation%2$s page on how to test the cache."
msgstr "Acesse a página de %1$sInformações%2$s para saber como testar o cache."

#: tpl/toolbox/settings-debug.tpl.php:66
msgid "Allows listed IPs (one per line) to perform certain actions from their browsers."
msgstr "Permite que os IPs listados (um por linha) executem certas ações a partir de seus navegadores."

#: src/lang.cls.php:250
msgid "Server Load Limit"
msgstr "Limite de carga do servidor"

#: tpl/crawler/settings.tpl.php:39
msgid "Specify how long in seconds before the crawler should initiate crawling the entire sitemap again."
msgstr "Especifique por quantos segundos o rastreador deve esperar antes de iniciar o rastreamento completo do sitemap novamente."

#: src/lang.cls.php:249
msgid "Crawl Interval"
msgstr "Intervalo de rastreamento"

#. translators: %s: Example subdomain
#: tpl/cache/settings_inc.login_cookie.tpl.php:53
msgid "Then another WordPress is installed (NOT MULTISITE) at %s"
msgstr "Em seguida, outro WordPress é instalado (NÃO MULTISITE) em %s"

#: tpl/cache/entry_network.tpl.php:27
msgid "LiteSpeed Cache Network Cache Settings"
msgstr "Configurações de cache em rede do LiteSpeed Cache"

#: tpl/toolbox/purge.tpl.php:178
msgid "Select below for \"Purge by\" options."
msgstr "Selecione abaixo as opções de \"Limpar por\"."

#: tpl/cdn/entry.tpl.php:17
msgid "LiteSpeed Cache CDN"
msgstr "CDN do LiteSpeed Cache"

#: tpl/crawler/summary.tpl.php:251
msgid "No crawler meta file generated yet"
msgstr "Nenhum arquivo de metadados do rastreador foi gerado ainda"

#: tpl/crawler/summary.tpl.php:233
msgid "Show crawler status"
msgstr "Mostrar o status do rastreador"

#: tpl/crawler/summary.tpl.php:226
msgid "Watch Crawler Status"
msgstr "Acompanhar o status do rastreador"

#: tpl/crawler/summary.tpl.php:221
msgid "Please see <a %s>Hooking WP-Cron Into the System Task Scheduler</a> to learn how to create the system cron task."
msgstr "Consulte <a %s>Conectando o WP-Cron ao agendador de tarefas do sistema</a> para aprender como criar a tarefa cron do sistema."

#: tpl/crawler/summary.tpl.php:214
msgid "Run frequency is set by the Interval Between Runs setting."
msgstr "A frequência de execução é definida pela configuração do intervalo entre as execuções."

#: tpl/crawler/summary.tpl.php:125
msgid "Manually run"
msgstr "Executar manualmente"

#: tpl/crawler/summary.tpl.php:122
msgid "Reset position"
msgstr "Redefinir posição"

#: tpl/crawler/summary.tpl.php:137
msgid "Run Frequency"
msgstr "Frequência de execução"

#: tpl/crawler/summary.tpl.php:136
msgid "Cron Name"
msgstr "Nome do cron"

#: tpl/crawler/summary.tpl.php:47
msgid "Crawler Cron"
msgstr "Cron do rastreador"

#: cli/crawler.cls.php:75 tpl/crawler/summary.tpl.php:41
msgid "%d minute"
msgstr "%d minuto"

#: cli/crawler.cls.php:73 tpl/crawler/summary.tpl.php:39
msgid "%d minutes"
msgstr "%d minutos"

#: cli/crawler.cls.php:66 tpl/crawler/summary.tpl.php:32
msgid "%d hour"
msgstr "%d hora"

#: cli/crawler.cls.php:64 tpl/crawler/summary.tpl.php:30
msgid "%d hours"
msgstr "%d horas"

#: tpl/crawler/map.tpl.php:30
msgid "Generated at %s"
msgstr "Gerado em %s"

#: tpl/crawler/entry.tpl.php:18
msgid "LiteSpeed Cache Crawler"
msgstr "Rastreador do LiteSpeed Cache"

#: tpl/inc/show_display_installed.php:29
msgid "If there are any questions, the team is always happy to answer any questions on the <a %s>support forum</a>."
msgstr "Se houver alguma dúvida, a equipe sempre terá prazer em responder às perguntas no <a %s>fórum de suporte</a>."

#: src/admin-display.cls.php:138 src/lang.cls.php:248
msgid "Crawler"
msgstr "Rastreador"

#. Plugin URI of the plugin
#: litespeed-cache.php
msgid "https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration"
msgstr "https://www.litespeedtech.com/products/cache-plugins/wordpress-acceleration"

#: src/purge.cls.php:675
msgid "Notified LiteSpeed Web Server to purge all pages."
msgstr "Servidor Web LiteSpeed notificado para limpar todas as páginas."

#: tpl/cache/settings-purge.tpl.php:25
msgid "All pages with Recent Posts Widget"
msgstr "Todas as páginas com o widget de posts recentes"

#: tpl/cache/settings-purge.tpl.php:24
msgid "Pages"
msgstr "Páginas"

#: tpl/toolbox/purge.tpl.php:16
msgid "This will Purge Pages only"
msgstr "Isso irá limpar apenas as páginas"

#: tpl/toolbox/purge.tpl.php:15
msgid "Purge Pages"
msgstr "Limpar páginas"

#: src/gui.cls.php:83
msgid "Cancel"
msgstr "Cancelar"

#: tpl/crawler/summary.tpl.php:139
msgid "Activate"
msgstr "Ativar"

#: tpl/cdn/cf.tpl.php:40
msgid "Email Address"
msgstr "Endereço de e-mail"

#: src/gui.cls.php:771
msgid "Install Now"
msgstr "Instalar agora"

#: cli/purge.cls.php:177
msgid "Purged the url!"
msgstr "Limpar o URL!"

#: cli/purge.cls.php:130
msgid "Purged the blog!"
msgstr "Limpar o blog!"

#: cli/purge.cls.php:88
msgid "Purged All!"
msgstr "Limpar tudo!"

#: src/purge.cls.php:695
msgid "Notified LiteSpeed Web Server to purge error pages."
msgstr "Servidor Web LiteSpeed notificado para limpar páginas de erro."

#: tpl/inc/show_error_cookie.php:14
msgid "If using OpenLiteSpeed, the server must be restarted once for the changes to take effect."
msgstr "Se estiver usando o OpenLiteSpeed, o servidor deve ser reiniciado uma vez para que as alterações tenham efeito."

#: tpl/inc/show_error_cookie.php:11
msgid "If not, please verify the setting in the <a href=\"%1$s\">Advanced tab</a>."
msgstr "Se não, verifique a configuração na aba <a href=\"%1$s\">Avançado</a>."

#: tpl/inc/show_error_cookie.php:9
msgid "If the login cookie was recently changed in the settings, please log out and back in."
msgstr "Se o cookie de acesso foi alterado recentemente nas configurações, desconecte e volte a acessar."

#: tpl/inc/show_display_installed.php:15
msgid "However, there is no way of knowing all the possible customizations that were implemented."
msgstr "No entanto, não há maneira de saber todas as possíveis personalizações que foram implementadas."

#: tpl/inc/show_display_installed.php:13
msgid "The LiteSpeed Cache plugin is used to cache pages - a simple way to improve the performance of the site."
msgstr "O plugin LiteSpeed Cache é usado para armazenar em cache páginas - uma maneira simples de melhorar o desempenho do site."

#: tpl/cache/settings-cache.tpl.php:45
msgid "The network admin setting can be overridden here."
msgstr "A configuração do administrador de rede pode ser substituída aqui."

#: tpl/cache/settings-ttl.tpl.php:29
msgid "Specify how long, in seconds, public pages are cached."
msgstr "Especifique por quanto tempo, em segundos, as páginas públicas são armazenadas em cache."

#: tpl/cache/settings-ttl.tpl.php:44
msgid "Specify how long, in seconds, private pages are cached."
msgstr "Especifique por quanto tempo, em segundos, as páginas privadas são armazenadas em cache."

#: tpl/cache/network_settings-cache.tpl.php:29
msgid "It is STRONGLY recommended that the compatibility with other plugins on a single/few sites is tested first."
msgstr "É ALTAMENTE recomendável que a compatibilidade com outros plugins em um único ou alguns sites seja testada primeiro."

#: tpl/toolbox/purge.tpl.php:262
msgid "Purge pages by post ID."
msgstr "Limpar páginas por ID de post."

#: tpl/toolbox/purge.tpl.php:31
msgid "Purge the LiteSpeed cache entries created by this plugin"
msgstr "Limpar as entradas de cache LiteSpeed criadas por este plugin"

#: tpl/toolbox/purge.tpl.php:24
msgid "Purge %s error pages"
msgstr "Limpar páginas de erro %s"

#: tpl/toolbox/purge.tpl.php:10
msgid "This will Purge Front Page only"
msgstr "Isso irá limpar apenas a página inicial"

#: tpl/toolbox/purge.tpl.php:272
msgid "Purge pages by tag name - e.g. %2$s should be used for the URL %1$s."
msgstr "Limpar páginas por nome da tag - por exemplo, %2$s deve ser usado para o URL %1$s."

#: tpl/toolbox/purge.tpl.php:250
msgid "Purge pages by category name - e.g. %2$s should be used for the URL %1$s."
msgstr "Limpar páginas por nome de categoria - exemplo: %2$s deve ser usado para o URL %1$s."

#: tpl/toolbox/purge.tpl.php:125
msgid "If only the WordPress site should be purged, use Purge All."
msgstr "Se apenas o site WordPress deve ser limpo, use \"Limpar tudo\"."

#: src/core.cls.php:327
msgid "Notified LiteSpeed Web Server to purge everything."
msgstr "O LiteSpeed Web Server foi notificado para limpar tudo com sucesso."

#: tpl/general/network_settings.tpl.php:22
msgid "Use Primary Site Configuration"
msgstr "Usar a configuração do site principal"

#: tpl/general/network_settings.tpl.php:27
msgid "This will disable the settings page on all subsites."
msgstr "Isso irá desativar a página de configurações em todos os subsites."

#: tpl/general/network_settings.tpl.php:26
msgid "Check this option to use the primary site's configuration for all subsites."
msgstr "Marque esta opção para usar a configuração do site principal para todos os subsites."

#: src/admin-display.cls.php:813 src/admin-display.cls.php:817
msgid "Save Changes"
msgstr "Salvar alterações"

#: tpl/inc/check_if_network_disable_all.php:22
msgid "The following options are selected, but are not editable in this settings page."
msgstr "As seguintes opções estão selecionadas, mas não são editáveis nesta página de configurações."

#: tpl/inc/check_if_network_disable_all.php:21
msgid "The network admin selected use primary site configs for all subsites."
msgstr "O administrador da rede selecionou usar configurações do site principal para todos os subsites."

#: tpl/toolbox/purge.tpl.php:117
msgid "Empty Entire Cache"
msgstr "Esvaziar todo o cache"

#: tpl/toolbox/purge.tpl.php:119
msgid "This action should only be used if things are cached incorrectly."
msgstr "Essa ação deve ser usada apenas se as coisas estiverem sendo armazenadas em cache de forma incorreta."

#: tpl/toolbox/purge.tpl.php:118
msgid "Clears all cache entries related to this site, <i>including other web applications</i>."
msgstr "Limpa todas as entradas de cache relacionadas a este site, <i>incluindo outras aplicações web</i>."

#: tpl/toolbox/purge.tpl.php:124
msgid "This may cause heavy load on the server."
msgstr "Isso pode gerar uma grande demanda no servidor."

#: tpl/toolbox/purge.tpl.php:123
msgid "This will clear EVERYTHING inside the cache."
msgstr "Isso irá limpar TODOS os conteúdos do cache."

#: src/gui.cls.php:593
msgid "LiteSpeed Cache Purge All"
msgstr "Limpar tudo no LiteSpeed Cache"

#: tpl/inc/show_display_installed.php:33
msgid "If you would rather not move at litespeed, you can deactivate this plugin."
msgstr "Se você preferir não se mover na velocidade da luz, pode desativar este plugin."

#: tpl/inc/show_display_installed.php:25
msgid "Create a post, make sure the front page is accurate."
msgstr "Crie um post e certifique-se de que a página inicial esteja correta."

#: tpl/inc/show_display_installed.php:22
msgid "Visit the site while logged out."
msgstr "Visite o site enquanto estiver desconectado."

#: tpl/inc/show_display_installed.php:19
msgid "Examples of test cases include:"
msgstr "Exemplos de casos de teste incluem:"

#: tpl/inc/show_display_installed.php:17
msgid "For that reason, please test the site to make sure everything still functions properly."
msgstr "Por esse motivo, teste o site para garantir que tudo continue funcionando corretamente."

#: tpl/inc/show_display_installed.php:11
msgid "This message indicates that the plugin was installed by the server admin."
msgstr "Esta mensagem indica que o plugin foi instalado pelo administrador do servidor."

#: tpl/inc/show_display_installed.php:8
msgid "LiteSpeed Cache plugin is installed!"
msgstr "O plugin de cache do LiteSpeed está instalado!"

#: src/lang.cls.php:256 tpl/toolbox/log_viewer.tpl.php:11
msgid "Debug Log"
msgstr "Registro de depuração"

#: tpl/toolbox/settings-debug.tpl.php:49
msgid "Admin IP Only"
msgstr "Apenas IP do Admin"

#: tpl/toolbox/settings-debug.tpl.php:53
msgid "The Admin IP option will only output log messages on requests from admin IPs listed below."
msgstr "A opção de IP do administrador só irá gerar mensagens de registro nas solicitações dos IPs do administrador listados abaixo."

#: tpl/cache/settings-ttl.tpl.php:89
msgid "Specify how long, in seconds, REST calls are cached."
msgstr "Especifique por quanto tempo, em segundos, as chamadas da REST são armazenadas em cache."

#: tpl/toolbox/report.tpl.php:58
msgid "The environment report contains detailed information about the WordPress configuration."
msgstr "O relatório do ambiente contém informações detalhadas sobre a configuração do WordPress."

#: tpl/cache/settings_inc.login_cookie.tpl.php:36
msgid "The server will determine if the user is logged in based on the existence of this cookie."
msgstr "O servidor determinará se o usuário está conectado com base na existência deste cookie."

#: tpl/cache/settings-purge.tpl.php:53 tpl/cache/settings-purge.tpl.php:90
#: tpl/cache/settings-purge.tpl.php:114
#: tpl/page_optm/settings_tuning_css.tpl.php:62
#: tpl/page_optm/settings_tuning_css.tpl.php:138
msgid "Note"
msgstr "Observação"

#: thirdparty/woocommerce.content.tpl.php:23
msgid "After verifying that the cache works in general, please test the cart."
msgstr "Após verificar que o cache funciona de forma geral, teste o carrinho."

#: tpl/cache/settings_inc.purge_on_upgrade.tpl.php:25
msgid "When enabled, the cache will automatically purge when any plugin, theme or the WordPress core is upgraded."
msgstr "Quando ativado, o cache será automaticamente limpo quando qualquer plugin, tema ou o núcleo do WordPress for atualizado."

#: src/lang.cls.php:127
msgid "Purge All On Upgrade"
msgstr "Limpar tudo na atualização"

#: thirdparty/woocommerce.content.tpl.php:33
msgid "Product Update Interval"
msgstr "Intervalo de atualização do produto"

#: thirdparty/woocommerce.content.tpl.php:54
msgid "Determines how changes in product quantity and product stock status affect product pages and their associated category pages."
msgstr "Determina como as alterações na quantidade de produtos e no status de estoque dos produtos afetam as páginas de produtos e suas páginas de categoria associadas."

#: thirdparty/woocommerce.content.tpl.php:41
msgid "Always purge both product and categories on changes to the quantity or stock status."
msgstr "Sempre limpar tanto o produto quanto as categorias em caso de alterações na quantidade ou status do estoque."

#: thirdparty/woocommerce.content.tpl.php:40
msgid "Do not purge categories on changes to the quantity or stock status."
msgstr "Não limpar categorias em caso de alterações na quantidade ou status do estoque."

#: thirdparty/woocommerce.content.tpl.php:40
msgid "Purge product only when the stock status changes."
msgstr "Limpar produto apenas quando o status do estoque mudar."

#: thirdparty/woocommerce.content.tpl.php:39
msgid "Purge product and categories only when the stock status changes."
msgstr "Limpar produto e categorias apenas quando o status do estoque mudar."

#: thirdparty/woocommerce.content.tpl.php:38
msgid "Purge categories only when stock status changes."
msgstr "Limpar categorias apenas quando o status do estoque mudar."

#: thirdparty/woocommerce.content.tpl.php:38
msgid "Purge product on changes to the quantity or stock status."
msgstr "Limpar produto em caso de alterações na quantidade ou status do estoque."

#: tpl/cache/settings_inc.cache_mobile.tpl.php:47
msgid "Htaccess did not match configuration option."
msgstr "O arquivo .htaccess não correspondeu à opção de configuração."

#: tpl/cache/settings-ttl.tpl.php:75 tpl/cache/settings-ttl.tpl.php:90
msgid "If this is set to a number less than 30, feeds will not be cached."
msgstr "Se isso for definido como um número menor que 30, os feeds não são armazenados em cache."

#: tpl/cache/settings-ttl.tpl.php:74
msgid "Specify how long, in seconds, feeds are cached."
msgstr "Especifique por quanto tempo, em segundos, os feeds são armazenados em cache."

#: src/lang.cls.php:95
msgid "Default Feed TTL"
msgstr "TTL padrão do feed"

#: src/error.cls.php:167
msgid "Failed to get %s file contents."
msgstr "Falha ao obter o conteúdo do arquivo %s."

#: tpl/cache/settings-cache.tpl.php:102
msgid "Disabling this option may negatively affect performance."
msgstr "Desativar esta opção pode afetar negativamente o desempenho."

#: tpl/cache/settings_inc.login_cookie.tpl.php:63
msgid "Invalid login cookie. Invalid characters found."
msgstr "Cookie de acesso inválido. Encontrados caracteres inválidos."

#: tpl/cache/settings_inc.login_cookie.tpl.php:84
msgid "WARNING: The .htaccess login cookie and Database login cookie do not match."
msgstr "ATENÇÃO: O cookie de acesso do .htaccess e o cookie de acesso do banco de dados não correspondem."

#: src/error.cls.php:151
msgid "Invalid login cookie. Please check the %s file."
msgstr "Cookie de acesso inválido. Verifique o arquivo %s."

#: tpl/cache/settings_inc.login_cookie.tpl.php:57
msgid "The cache needs to distinguish who is logged into which WordPress site in order to cache correctly."
msgstr "O cache precisa distinguir quem está conectado a qual site do WordPress para armazenar em cache corretamente."

#. translators: %s: Example domain
#: tpl/cache/settings_inc.login_cookie.tpl.php:45
msgid "There is a WordPress installed for %s."
msgstr "Há uma instalação do WordPress para %s."

#: tpl/cache/settings_inc.login_cookie.tpl.php:41
msgid "Example use case:"
msgstr "Exemplo de caso de uso:"

#: tpl/cache/settings_inc.login_cookie.tpl.php:39
msgid "The cookie set here will be used for this WordPress installation."
msgstr "O cookie definido aqui será usado para esta instalação do WordPress."

#: tpl/cache/settings_inc.login_cookie.tpl.php:38
msgid "If every web application uses the same cookie, the server may confuse whether a user is logged in or not."
msgstr "Se todas as aplicações web usarem o mesmo cookie, o servidor pode ficar confuso sobre se um usuário está conectado ou não."

#: tpl/cache/settings_inc.login_cookie.tpl.php:37
msgid "This setting is useful for those that have multiple web applications for the same domain."
msgstr "Essa configuração é útil para aqueles que têm várias aplicações web para o mesmo domínio."

#. translators: %s: Default login cookie name
#: tpl/cache/settings_inc.login_cookie.tpl.php:32
msgid "The default login cookie is %s."
msgstr "O cookie de acesso padrão é %s."

#: src/lang.cls.php:225
msgid "Login Cookie"
msgstr "Cookie de acesso"

#: tpl/toolbox/settings-debug.tpl.php:73
msgid "More information about the available commands can be found here."
msgstr "Mais informações sobre os comandos disponíveis podem ser encontradas aqui."

#: tpl/cache/settings-advanced.tpl.php:22
msgid "These settings are meant for ADVANCED USERS ONLY."
msgstr "Essas configurações são destinadas APENAS PARA USUÁRIOS AVANÇADOS."

#: tpl/toolbox/edit_htaccess.tpl.php:88
msgid "Current %s Contents"
msgstr "Conteúdo atual de %s"

#: tpl/cache/entry.tpl.php:28 tpl/cache/entry_network.tpl.php:21
#: tpl/toolbox/settings-debug.tpl.php:86
msgid "Advanced"
msgstr "Avançado"

#: tpl/cache/network_settings-advanced.tpl.php:17
#: tpl/cache/settings-advanced.tpl.php:16
msgid "Advanced Settings"
msgstr "Configurações avançadas"

#: tpl/toolbox/purge.tpl.php:302
msgid "Purge List"
msgstr "Limpar lista"

#: tpl/toolbox/purge.tpl.php:176
msgid "Purge By..."
msgstr "Limpar por..."

#: tpl/crawler/blacklist.tpl.php:34 tpl/crawler/map.tpl.php:67
#: tpl/toolbox/purge.tpl.php:238
msgid "URL"
msgstr "URL"

#: tpl/toolbox/purge.tpl.php:229
msgid "Tag"
msgstr "Tag"

#: tpl/toolbox/purge.tpl.php:220
msgid "Post ID"
msgstr "ID do post"

#: tpl/toolbox/purge.tpl.php:211
msgid "Category"
msgstr "Categoria"

#: tpl/inc/show_error_cookie.php:7
msgid "NOTICE: Database login cookie did not match your login cookie."
msgstr "NOTIFICAÇÃO: O cookie de acesso do banco de dados não corresponde ao seu cookie de acesso."

#: src/purge.cls.php:785
msgid "Purge url %s"
msgstr "Limpar URL %s"

#: src/purge.cls.php:751
msgid "Purge tag %s"
msgstr "Limpar tag %s"

#: src/purge.cls.php:722
msgid "Purge category %s"
msgstr "Limpar categoria %s"

#: tpl/cache/settings-cache.tpl.php:42
msgid "When disabling the cache, all cached entries for this site will be purged."
msgstr "Ao desativar o cache, todas as entradas em cache deste site serão eliminadas."

#: tpl/cache/settings-cache.tpl.php:42 tpl/crawler/settings.tpl.php:104
#: tpl/crawler/settings.tpl.php:113 tpl/crawler/summary.tpl.php:183
#: tpl/page_optm/entry.tpl.php:33
msgid "NOTICE"
msgstr "NOTIFICAÇÃO"

#: src/doc.cls.php:138
msgid "This setting will edit the .htaccess file."
msgstr "Esta configuração irá editar o arquivo .htaccess."

#: tpl/toolbox/edit_htaccess.tpl.php:38
msgid "LiteSpeed Cache View .htaccess"
msgstr "Visualização do LiteSpeed Cache .htaccess"

#: src/error.cls.php:155
msgid "Failed to back up %s file, aborted changes."
msgstr "Falha ao fazer backup do arquivo %s, alterações abortadas."

#: src/lang.cls.php:223
msgid "Do Not Cache Cookies"
msgstr "Não armazenar cookies em cache"

#: src/lang.cls.php:224
msgid "Do Not Cache User Agents"
msgstr "Não armazenar agentes de usuário em cache"

#: tpl/cache/network_settings-cache.tpl.php:30
msgid "This is to ensure compatibility prior to enabling the cache for all sites."
msgstr "Isso é para garantir a compatibilidade antes de ativar o cache para todos os sites."

#: tpl/cache/network_settings-cache.tpl.php:24
msgid "Network Enable Cache"
msgstr "Ativar cache de rede"

#: thirdparty/woocommerce.content.tpl.php:22
#: tpl/cache/settings-advanced.tpl.php:21
#: tpl/cache/settings_inc.browser.tpl.php:23 tpl/toolbox/heartbeat.tpl.php:15
#: tpl/toolbox/report.tpl.php:38
msgid "NOTICE:"
msgstr "NOTIFICAÇÃO:"

#: tpl/cache/settings-purge.tpl.php:56
msgid "Other checkboxes will be ignored."
msgstr "As outras caixas de seleção serão ignoradas."

#: tpl/cache/settings-purge.tpl.php:55
msgid "Select \"All\" if there are dynamic widgets linked to posts on pages other than the front or home pages."
msgstr "Selecione \"Tudo\" se houver widgets dinâmicos vinculados a posts em páginas diferentes da página inicial ou principal."

#: src/lang.cls.php:109 tpl/cache/settings_inc.cache_mobile.tpl.php:92
msgid "List of Mobile User Agents"
msgstr "Lista de agentes de usuários móveis"

#: src/file.cls.php:167 src/file.cls.php:171
msgid "File %s is not writable."
msgstr "O arquivo %s não é gravável."

#: tpl/page_optm/entry.tpl.php:8 tpl/page_optm/settings_js.tpl.php:9
msgid "JS Settings"
msgstr "Configurações de JS"

#: src/gui.cls.php:612 tpl/db_optm/entry.tpl.php:7
msgid "Manage"
msgstr "Gerenciar"

#: src/lang.cls.php:94
msgid "Default Front Page TTL"
msgstr "TTL padrão da página inicial"

#: src/purge.cls.php:661
msgid "Notified LiteSpeed Web Server to purge the front page."
msgstr "Servidor Web LiteSpeed ​​notificado para limpar a página inicial."

#: tpl/toolbox/purge.tpl.php:9
msgid "Purge Front Page"
msgstr "Limpar página inicial"

#: tpl/page_optm/settings_localization.tpl.php:128
#: tpl/toolbox/beta_test.tpl.php:28
msgid "Example"
msgstr "Exemplo"

#: tpl/cache/settings-excludes.tpl.php:99
msgid "All tags are cached by default."
msgstr "Todas as tags são armazenadas em cache por padrão."

#: tpl/cache/settings-excludes.tpl.php:66
msgid "All categories are cached by default."
msgstr "Todas as categorias são armazenadas em cache por padrão."

#: src/admin-display.cls.php:1230
msgid "To do an exact match, add %s to the end of the URL."
msgstr "Para fazer uma correspondência exata, adicione %s ao final do URL."

#: src/admin-display.cls.php:1226
msgid "The URLs will be compared to the REQUEST_URI server variable."
msgstr "Os URLs serão comparados à variável de servidor REQUEST_URI."

#: tpl/cache/settings-purge.tpl.php:57
msgid "Select only the archive types that are currently used, the others can be left unchecked."
msgstr "Selecione apenas os tipos de arquivo usados ​​atualmente; os outros podem ser deixados desmarcados."

#: tpl/toolbox/report.tpl.php:109
msgid "Notes"
msgstr "Observações"

#: tpl/cache/settings-cache.tpl.php:28
msgid "Use Network Admin Setting"
msgstr "Usar configuração de administrador de rede"

#: tpl/esi_widget_edit.php:48
msgid "Disable"
msgstr "Desativar"

#: tpl/cache/network_settings-cache.tpl.php:28
msgid "Enabling LiteSpeed Cache for WordPress here enables the cache for the network."
msgstr "A ativação do LiteSpeed Cache para WordPress aqui ativa o cache para a rede."

#: tpl/cache/settings_inc.object.tpl.php:16 tpl/general/online.tpl.php:125
msgid "Disabled"
msgstr "Desativado"

#: tpl/cache/settings_inc.object.tpl.php:15 tpl/general/online.tpl.php:123
msgid "Enabled"
msgstr "Ativado"

#: src/lang.cls.php:137
msgid "Do Not Cache Roles"
msgstr "Não armazenar funções em cache"

#. Author URI of the plugin
#: litespeed-cache.php
msgid "https://www.litespeedtech.com"
msgstr "https://www.litespeedtech.com"

#. Author of the plugin
#: litespeed-cache.php
msgid "LiteSpeed Technologies"
msgstr "LiteSpeed Technologies"

#. Plugin Name of the plugin
#: litespeed-cache.php tpl/banner/new_version.php:57
#: tpl/banner/new_version_dev.tpl.php:21 tpl/cache/more_settings_tip.tpl.php:28
#: tpl/inc/admin_footer.php:11
msgid "LiteSpeed Cache"
msgstr "LiteSpeed Cache"

#: src/lang.cls.php:258
msgid "Debug Level"
msgstr "Nível de depuração"

#: tpl/general/settings.tpl.php:63 tpl/general/settings.tpl.php:70
#: tpl/general/settings.tpl.php:77 tpl/general/settings.tpl.php:94
#: tpl/page_optm/settings_media.tpl.php:244
#: tpl/page_optm/settings_vpi.tpl.php:37
msgid "Notice"
msgstr "Notificação"

#: tpl/cache/settings-purge.tpl.php:31
msgid "Term archive (include category, tag, and tax)"
msgstr "Arquivo de termos (incluindo categoria, tag e taxonomia)"

#: tpl/cache/settings-purge.tpl.php:30
msgid "Daily archive"
msgstr "Arquivo diário"

#: tpl/cache/settings-purge.tpl.php:29
msgid "Monthly archive"
msgstr "Arquivo mensal"

#: tpl/cache/settings-purge.tpl.php:28
msgid "Yearly archive"
msgstr "Arquivo anual"

#: tpl/cache/settings-purge.tpl.php:27
msgid "Post type archive"
msgstr "Arquivo de tipo de post"

#: tpl/cache/settings-purge.tpl.php:26
msgid "Author archive"
msgstr "Arquivo do autor"

#: tpl/cache/settings-purge.tpl.php:23
msgid "Home page"
msgstr "Página inicial"

#: tpl/cache/settings-purge.tpl.php:22
msgid "Front page"
msgstr "Página principal"

#: tpl/cache/settings-purge.tpl.php:21
msgid "All pages"
msgstr "Todas as páginas"

#: tpl/cache/settings-purge.tpl.php:73
msgid "Select which pages will be automatically purged when posts are published/updated."
msgstr "Selecione quais páginas serão automaticamente limpas quando posts forem publicados/atualizados."

#: tpl/cache/settings-purge.tpl.php:50
msgid "Auto Purge Rules For Publish/Update"
msgstr "Regras automáticas de limpeza para Publicar/Atualizar"

#: src/lang.cls.php:92
msgid "Default Public Cache TTL"
msgstr "TTL padrão de cache público"

#: src/admin-display.cls.php:1047 tpl/cache/settings_inc.object.tpl.php:162
#: tpl/crawler/settings.tpl.php:37 tpl/esi_widget_edit.php:70
msgid "seconds"
msgstr "segundos"

#: src/lang.cls.php:257
msgid "Admin IPs"
msgstr "IPs de administrador"

#: src/admin-display.cls.php:126
msgid "General"
msgstr "Geral"

#: tpl/cache/entry.tpl.php:50
msgid "LiteSpeed Cache Settings"
msgstr "Configurações do LiteSpeed Cache"

#: src/purge.cls.php:234
msgid "Notified LiteSpeed Web Server to purge all LSCache entries."
msgstr "Servidor Web LiteSpeed ​​notificado para limpar todas as entradas LSCache."

#: src/gui.cls.php:476 src/gui.cls.php:484 src/gui.cls.php:492
#: src/gui.cls.php:501 src/gui.cls.php:511 src/gui.cls.php:521
#: src/gui.cls.php:531 src/gui.cls.php:541 src/gui.cls.php:550
#: src/gui.cls.php:560 src/gui.cls.php:570 src/gui.cls.php:638
#: src/gui.cls.php:646 src/gui.cls.php:654 src/gui.cls.php:663
#: src/gui.cls.php:673 src/gui.cls.php:683 src/gui.cls.php:693
#: src/gui.cls.php:703 src/gui.cls.php:712 src/gui.cls.php:722
#: src/gui.cls.php:732 tpl/page_optm/settings_media.tpl.php:131
#: tpl/toolbox/purge.tpl.php:30 tpl/toolbox/purge.tpl.php:36
#: tpl/toolbox/purge.tpl.php:44 tpl/toolbox/purge.tpl.php:53
#: tpl/toolbox/purge.tpl.php:62 tpl/toolbox/purge.tpl.php:71
#: tpl/toolbox/purge.tpl.php:80 tpl/toolbox/purge.tpl.php:89
#: tpl/toolbox/purge.tpl.php:98 tpl/toolbox/purge.tpl.php:107
msgid "Purge All"
msgstr "Limpar tudo"

#: src/admin-display.cls.php:290 src/gui.cls.php:620
#: tpl/crawler/entry.tpl.php:11
msgid "Settings"
msgstr "Configurações"

#: tpl/banner/score.php:120
msgid "Support forum"
msgstr "Fórum de suporte"
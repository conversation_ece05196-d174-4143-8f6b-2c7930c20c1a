{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Align the last block to the bottom.": ["<PERSON><PERSON><PERSON> o último bloco para baixo."], "Align the last block to the bottom": ["<PERSON><PERSON><PERSON> o último bloco para baixo"], "An error has prevented the block from being updated.": ["Um erro impediu a atualização do bloco."], "Stock status \"%s\" hidden.": ["Status do estoque \"%s\" oculto."], "Stock status \"%s\" visible.": ["Status do estoque \"%s\" visível."], "Edit selected attribute": ["Editar atributo selecionado"], "%d term": ["%d termo", "%d termos"], "%1$s, has %2$d term": ["%1$s, possui %2$d termo", "%1$s, possui %2$d termos"], "%1$s, has %2$d product": ["%1$s, possui %2$d produto", "%1$s, possui %2$d produtos"], "Loading…": ["Carregando..."], "The last inner block will follow other content.": ["O último bloco interno seguirá outro conteúdo."], "The following error was returned": ["O seguinte erro foi retornado"], "The following error was returned from the API": ["O seguinte erro foi retornado da API"], "Search for items": ["Procurar por itens"], "%d item selected": ["%d item selecionado", "%d itens selecionados"], "Clear all selected items": ["Remover todos os itens selecionados"], "Clear all": ["Remover todos"], "No results for %s": ["Nenhum resultado para %s"], "No items found.": ["Nenhum item encontrado."], "Remove %s": ["Remover %s"], "Search results updated.": ["Resultados de pesquisa atualizados."], "Products by Attribute": ["Produtos por atributo"], "Filter by Product Attribute": ["Filtro por atributo de produto"], "Showing Products by Attribute block preview.": ["Exibindo a pré-visualização do bloco de \"Produtos por attributo\"."], "All selected attributes": ["Todos os atributos selecionados"], "Display a grid of products from your selected attributes.": ["Exibe uma grade de produtos com base em atributos selecionados."], "%d attribute selected": ["%d atributo selecionado", "%d atributos selecionados"], "Search for product attributes": ["Pesquisar por atributos de produto"], "Your store doesn't have any product attributes.": ["Sua loja não tem atributos de produto."], "Product attribute search results updated.": ["Resultados atualizados para pesquisa de atributos de produto."], "Done": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Order By": ["Ordenar por"], "Any selected attributes": ["Quaisquer atributos selecionados"], "Rows": ["<PERSON><PERSON>"], "Columns": ["Colunas"], "Layout": ["<PERSON><PERSON>"], "Add to Cart button": ["Botão \"Adicionar ao carrinho\""], "Clear all product attributes": ["Limpar todos os atributos do produto"], "Pick at least two attributes to use this setting.": ["Escolha pelo menos dois atributos para usar esta configuração."], "Menu Order": ["Ordem do menu"], "Title - alphabetical": ["Título - alfabético"], "Sales - most first": ["Vendas - mais vendas primeiro"], "Rating - highest first": ["Classificação - mais alta primeiro"], "Price - high to low": ["Preço - de alto para baixo"], "Price - low to high": ["Preço - de baixo para alto"], "Newness - newest first": ["Novidade - mais novo primeiro"], "Order products by": ["Ordenar produtos por"], "Display products matching": ["<PERSON><PERSON>r produtos correspondentes"], "Product rating": ["Classificação do produto"], "Product price": ["Preço do produto"], "Product title": ["Título do produto"], "Filter by stock status": ["Filtrar por status do estoque"], "Product image": ["Imagem do produto"], "%d product": ["%d produto", "%d produtos"], "Content": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/products-by-attribute.js"}}
# Design Document

## Overview

O tema "Construction Store" será um tema WordPress moderno e responsivo, especificamente projetado para lojas de materiais de construção e ferragens. Baseado na arquitetura de block themes do WordPress 6.8.2, o tema integrará perfeitamente com WooCommerce e oferecerá funcionalidades especializadas para o setor da construção civil.

O design seguirá uma abordagem mobile-first, priorizando usabilidade e conversão, com elementos visuais que transmitam confiança, profissionalismo e expertise técnica.

## Architecture

### Theme Structure
```
construction-store-theme/
├── style.css                 # Main theme stylesheet
├── functions.php             # Theme functions and hooks
├── theme.json               # Global styles and settings
├── index.php                # Fallback template
├── templates/
│   ├── index.html           # Main template
│   ├── front-page.html      # Homepage template
│   ├── single-product.html  # WooCommerce product page
│   ├── archive-product.html # Product category/archive
│   └── page-checkout.html   # Checkout page
├── parts/
│   ├── header.html          # Site header
│   ├── footer.html          # Site footer
│   ├── navigation.html      # Main navigation
│   └── product-filters.html # Product filtering sidebar
├── patterns/
│   ├── hero-banner.php      # Homepage hero section
│   ├── product-categories.php # Category showcase
│   ├── featured-products.php # Featured products grid
│   └── calculator-section.php # Material calculator
├── assets/
│   ├── css/
│   │   ├── blocks/          # Block-specific styles
│   │   ├── woocommerce/     # WooCommerce overrides
│   │   └── components/      # Reusable components
│   ├── js/
│   │   ├── calculator.js    # Material quantity calculator
│   │   ├── product-filters.js # Enhanced filtering
│   │   └── mobile-menu.js   # Mobile navigation
│   └── images/
│       ├── icons/           # SVG icons
│       └── placeholders/    # Default product images
└── inc/
    ├── woocommerce-customizations.php
    ├── custom-post-types.php
    ├── theme-customizer.php
    └── block-patterns.php
```

### Technology Stack
- **WordPress 6.8.2** com Full Site Editing (FSE)
- **WooCommerce** para funcionalidades de e-commerce
- **Block Theme Architecture** para máxima flexibilidade
- **CSS Grid e Flexbox** para layouts responsivos
- **Vanilla JavaScript** para interatividade (sem dependências jQuery)
- **SCSS** para organização de estilos (compilado via build process)

## Components and Interfaces

### 1. Header Component
**Funcionalidade:** Navegação principal, busca, carrinho e conta do usuário
```html
<!-- Header Structure -->
<header class="site-header">
  <div class="header-top">
    <div class="contact-info">
      <span>📞 (11) 1234-5678</span>
      <span>📧 <EMAIL></span>
    </div>
    <div class="user-actions">
      <a href="/minha-conta">Minha Conta</a>
      <a href="/carrinho">Carrinho (0)</a>
    </div>
  </div>
  <div class="header-main">
    <div class="logo">
      <img src="logo.svg" alt="Logo da Loja">
    </div>
    <nav class="main-navigation">
      <!-- Category-based navigation -->
    </nav>
    <div class="search-bar">
      <input type="search" placeholder="Buscar produtos...">
      <button type="submit">🔍</button>
    </div>
  </div>
</header>
```

### 2. Product Grid Component
**Funcionalidade:** Exibição de produtos com informações técnicas
```html
<div class="product-grid">
  <article class="product-card">
    <div class="product-image">
      <img src="product.jpg" alt="Nome do Produto">
      <div class="product-badges">
        <span class="badge sale">-20%</span>
        <span class="badge new">Novo</span>
      </div>
    </div>
    <div class="product-info">
      <h3 class="product-title">Nome do Produto</h3>
      <div class="product-specs">
        <span class="spec">Dimensão: 10x20cm</span>
        <span class="spec">Material: Aço</span>
      </div>
      <div class="product-pricing">
        <span class="price-current">R$ 45,90</span>
        <span class="price-original">R$ 57,90</span>
      </div>
      <button class="add-to-cart">Adicionar ao Carrinho</button>
    </div>
  </article>
</div>
```

### 3. Material Calculator Component
**Funcionalidade:** Calculadora de quantidade de materiais
```javascript
class MaterialCalculator {
  constructor(productType, coverage) {
    this.productType = productType;
    this.coverage = coverage; // m² por unidade
  }
  
  calculateQuantity(area, safetyMargin = 0.1) {
    const baseQuantity = Math.ceil(area / this.coverage);
    const totalQuantity = Math.ceil(baseQuantity * (1 + safetyMargin));
    return {
      base: baseQuantity,
      recommended: totalQuantity,
      waste: totalQuantity - baseQuantity
    };
  }
}
```

### 4. Product Filter Sidebar
**Funcionalidade:** Filtros avançados para produtos de construção
```html
<aside class="product-filters">
  <div class="filter-group">
    <h4>Categoria</h4>
    <ul class="filter-list">
      <li><input type="checkbox" id="ferramentas"> <label for="ferramentas">Ferramentas</label></li>
      <li><input type="checkbox" id="materiais"> <label for="materiais">Materiais Básicos</label></li>
    </ul>
  </div>
  
  <div class="filter-group">
    <h4>Faixa de Preço</h4>
    <div class="price-range">
      <input type="range" min="0" max="1000" class="price-slider">
      <div class="price-display">R$ 0 - R$ 1000</div>
    </div>
  </div>
  
  <div class="filter-group">
    <h4>Marca</h4>
    <select class="brand-filter">
      <option value="">Todas as marcas</option>
      <option value="bosch">Bosch</option>
      <option value="makita">Makita</option>
    </select>
  </div>
  
  <div class="filter-group">
    <h4>Disponibilidade</h4>
    <label><input type="checkbox"> Em estoque</label>
    <label><input type="checkbox"> Entrega rápida</label>
  </div>
</aside>
```

## Data Models

### 1. Product Extensions
Campos customizados para produtos de construção:
```php
// Custom fields for construction products
$construction_fields = [
    'technical_specs' => [
        'dimensions' => 'string',
        'weight' => 'float',
        'material' => 'string',
        'application' => 'text',
        'coverage_area' => 'float', // for paints, cement, etc.
        'installation_guide' => 'url'
    ],
    'certifications' => [
        'inmetro' => 'boolean',
        'iso_certification' => 'string',
        'safety_rating' => 'string'
    ],
    'shipping_info' => [
        'requires_special_delivery' => 'boolean',
        'max_quantity_per_order' => 'integer',
        'delivery_restrictions' => 'text'
    ]
];
```

### 2. Category Taxonomy
Estrutura hierárquica de categorias:
```
Materiais Básicos
├── Cimento e Argamassa
├── Tijolos e Blocos
├── Areia e Brita
└── Madeira

Ferramentas
├── Ferramentas Manuais
├── Ferramentas Elétricas
├── Equipamentos de Segurança
└── Medição e Nivelamento

Elétrica
├── Fios e Cabos
├── Interruptores e Tomadas
├── Disjuntores
└── Iluminação

Hidráulica
├── Tubos e Conexões
├── Registros e Válvulas
├── Torneiras e Chuveiros
└── Caixas d'Água

Tintas e Acabamentos
├── Tintas para Parede
├── Tintas para Madeira
├── Vernizes e Seladores
└── Pincéis e Rolos
```

## Error Handling

### 1. Product Availability
```php
function check_product_availability($product_id, $quantity) {
    $product = wc_get_product($product_id);
    
    if (!$product || !$product->is_in_stock()) {
        return new WP_Error('out_of_stock', 'Produto fora de estoque');
    }
    
    if ($product->get_stock_quantity() < $quantity) {
        return new WP_Error('insufficient_stock', 
            sprintf('Apenas %d unidades disponíveis', $product->get_stock_quantity())
        );
    }
    
    return true;
}
```

### 2. Calculator Validation
```javascript
function validateCalculatorInput(area, productType) {
    const errors = [];
    
    if (!area || area <= 0) {
        errors.push('Área deve ser maior que zero');
    }
    
    if (area > 10000) {
        errors.push('Para áreas muito grandes, consulte nossos especialistas');
    }
    
    if (!productType) {
        errors.push('Selecione o tipo de produto');
    }
    
    return errors;
}
```

### 3. Shipping Calculation
```php
function calculate_construction_shipping($package) {
    $heavy_items = [];
    $total_weight = 0;
    
    foreach ($package['contents'] as $item) {
        $product = $item['data'];
        $weight = $product->get_weight();
        $total_weight += $weight * $item['quantity'];
        
        if ($weight > 50) { // kg
            $heavy_items[] = $product->get_name();
        }
    }
    
    if (!empty($heavy_items)) {
        return [
            'requires_special_delivery' => true,
            'heavy_items' => $heavy_items,
            'estimated_delivery' => '5-7 dias úteis'
        ];
    }
    
    return ['standard_delivery' => true];
}
```

## Testing Strategy

### 1. Unit Tests
- Testes para calculadora de materiais
- Validação de campos customizados de produtos
- Funções de cálculo de frete

### 2. Integration Tests
- Integração com WooCommerce
- Compatibilidade com plugins de pagamento brasileiros
- Testes de responsividade em diferentes dispositivos

### 3. User Acceptance Tests
- Fluxo completo de compra
- Usabilidade da calculadora de materiais
- Performance em dispositivos móveis
- Acessibilidade (WCAG 2.1)

### 4. Performance Tests
- Tempo de carregamento da página inicial
- Performance com catálogo grande (>1000 produtos)
- Otimização de imagens de produtos
- Cache de consultas de filtros

### 5. Browser Compatibility
- Chrome, Firefox, Safari, Edge (últimas 2 versões)
- Dispositivos móveis iOS e Android
- Testes em conexões lentas (3G)

## Design System

### Color Palette
```css
:root {
  /* Primary Colors - Construction Theme */
  --color-primary: #FF6B35;        /* Orange - energia, ação */
  --color-primary-dark: #E55A2B;   /* Orange escuro */
  --color-primary-light: #FF8A5C;  /* Orange claro */
  
  /* Secondary Colors */
  --color-secondary: #2C3E50;      /* Azul escuro - confiança */
  --color-secondary-light: #34495E; /* Azul médio */
  
  /* Neutral Colors */
  --color-gray-100: #F8F9FA;       /* Background claro */
  --color-gray-200: #E9ECEF;       /* Borders */
  --color-gray-300: #DEE2E6;       /* Disabled */
  --color-gray-600: #6C757D;       /* Text secundário */
  --color-gray-800: #343A40;       /* Text principal */
  --color-gray-900: #212529;       /* Headings */
  
  /* Status Colors */
  --color-success: #28A745;        /* Em estoque */
  --color-warning: #FFC107;        /* Estoque baixo */
  --color-danger: #DC3545;         /* Fora de estoque */
  --color-info: #17A2B8;          /* Informações */
}
```

### Typography
```css
:root {
  /* Font Families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Roboto Slab', serif;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}
```

### Spacing System
```css
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
}
```

## Responsive Breakpoints
```css
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}
```

## Accessibility Features
- Navegação por teclado completa
- Contraste adequado (WCAG AA)
- Textos alternativos para imagens
- Landmarks semânticos
- Suporte a leitores de tela
- Foco visível em elementos interativos
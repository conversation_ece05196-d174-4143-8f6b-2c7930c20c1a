<!-- wp:template-part {"slug":"header","className":"site-header"} /-->

<!-- wp:group {"tagName":"main","className":"site-main single-product-page","layout":{"type":"constrained"}} -->
<main class="wp-block-group site-main single-product-page">
    
    <!-- wp:woocommerce/breadcrumbs {"className":"product-breadcrumbs"} /-->
    
    <!-- wp:group {"className":"product-container","layout":{"type":"flex","flexWrap":"wrap"}} -->
    <div class="wp-block-group product-container">
        
        <!-- Product Images Section -->
        <!-- wp:group {"className":"product-images","layout":{"type":"default"}} -->
        <div class="wp-block-group product-images">
            <!-- wp:woocommerce/product-image-gallery {"className":"product-gallery"} /-->
        </div>
        <!-- /wp:group -->
        
        <!-- Product Information Section -->
        <!-- wp:group {"className":"product-summary","layout":{"type":"default"}} -->
        <div class="wp-block-group product-summary">
            
            <!-- wp:post-title {"level":1,"className":"product-title"} /-->
            
            <!-- wp:woocommerce/product-rating {"className":"product-rating"} /-->
            
            <!-- wp:woocommerce/product-price {"className":"product-price"} /-->
            
            <!-- wp:post-excerpt {"className":"product-short-description"} /-->
            
            <!-- Technical Specifications Section -->
            <!-- wp:group {"className":"technical-specs","layout":{"type":"default"}} -->
            <div class="wp-block-group technical-specs">
                <!-- wp:heading {"level":3} -->
                <h3 class="wp-block-heading">Especificações Técnicas</h3>
                <!-- /wp:heading -->
                
                <!-- wp:html -->
                <div class="specs-grid">
                    <div class="spec-item">
                        <strong>Dimensões:</strong>
                        <span class="spec-value" data-spec="dimensions">-</span>
                    </div>
                    <div class="spec-item">
                        <strong>Peso:</strong>
                        <span class="spec-value" data-spec="weight">-</span>
                    </div>
                    <div class="spec-item">
                        <strong>Material:</strong>
                        <span class="spec-value" data-spec="material">-</span>
                    </div>
                    <div class="spec-item">
                        <strong>Aplicação:</strong>
                        <span class="spec-value" data-spec="application">-</span>
                    </div>
                </div>
                <!-- /wp:html -->
            </div>
            <!-- /wp:group -->
            
            <!-- Stock and Availability -->
            <!-- wp:woocommerce/product-stock-indicator {"className":"stock-indicator"} /-->
            
            <!-- Material Calculator (for applicable products) -->
            <!-- wp:group {"className":"material-calculator","layout":{"type":"default"}} -->
            <div class="wp-block-group material-calculator" style="display:none;">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">🧮 Calculadora de Quantidade</h4>
                <!-- /wp:heading -->
                
                <!-- wp:html -->
                <div class="calculator-form">
                    <div class="calc-input-group">
                        <label for="calc-area">Área (m²):</label>
                        <input type="number" id="calc-area" placeholder="Ex: 25" min="0" step="0.1">
                    </div>
                    <div class="calc-result">
                        <p><strong>Quantidade recomendada:</strong> <span id="calc-quantity">-</span></p>
                        <p><small>Inclui margem de segurança de 10%</small></p>
                    </div>
                    <button type="button" class="calc-button" onclick="calculateMaterial()">Calcular</button>
                </div>
                <!-- /wp:html -->
            </div>
            <!-- /wp:group -->
            
            <!-- Add to Cart Form -->
            <!-- wp:woocommerce/add-to-cart-form {"className":"product-add-to-cart"} /-->
            
            <!-- Product Meta Information -->
            <!-- wp:woocommerce/product-meta {"className":"product-meta"} /-->
            
            <!-- Shipping Information -->
            <!-- wp:group {"className":"shipping-info","layout":{"type":"default"}} -->
            <div class="wp-block-group shipping-info">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">🚚 Informações de Entrega</h4>
                <!-- /wp:heading -->
                
                <!-- wp:html -->
                <div class="shipping-calculator">
                    <input type="text" placeholder="Digite seu CEP" class="shipping-cep" maxlength="9">
                    <button type="button" class="shipping-calc-btn">Calcular Frete</button>
                    <div class="shipping-results"></div>
                </div>
                <!-- /wp:html -->
            </div>
            <!-- /wp:group -->
            
        </div>
        <!-- /wp:group -->
        
    </div>
    <!-- /wp:group -->
    
    <!-- Product Tabs Section -->
    <!-- wp:group {"className":"product-tabs-section","layout":{"type":"constrained"}} -->
    <div class="wp-block-group product-tabs-section">
        
        <!-- wp:html -->
        <div class="product-tabs">
            <nav class="tabs-nav">
                <button class="tab-button active" data-tab="description">Descrição</button>
                <button class="tab-button" data-tab="specifications">Especificações</button>
                <button class="tab-button" data-tab="installation">Instalação</button>
                <button class="tab-button" data-tab="reviews">Avaliações</button>
            </nav>
            
            <div class="tab-content">
                <div class="tab-panel active" id="description">
                    <!-- wp:post-content {"className":"product-description"} /-->
                </div>
                
                <div class="tab-panel" id="specifications">
                    <div class="detailed-specs">
                        <h3>Especificações Detalhadas</h3>
                        <div class="specs-table">
                            <!-- Technical specifications will be populated via PHP -->
                        </div>
                    </div>
                </div>
                
                <div class="tab-panel" id="installation">
                    <div class="installation-guide">
                        <h3>Guia de Instalação</h3>
                        <div class="installation-content">
                            <!-- Installation guide content -->
                        </div>
                    </div>
                </div>
                
                <div class="tab-panel" id="reviews">
                    <!-- wp:woocommerce/reviews-by-product {"className":"product-reviews"} /-->
                </div>
            </div>
        </div>
        <!-- /wp:html -->
        
    </div>
    <!-- /wp:group -->
    
    <!-- Related Products Section -->
    <!-- wp:group {"className":"related-products-section","layout":{"type":"constrained"}} -->
    <div class="wp-block-group related-products-section">
        <!-- wp:heading {"textAlign":"center"} -->
        <h2 class="wp-block-heading has-text-align-center">Produtos Relacionados</h2>
        <!-- /wp:heading -->
        
        <!-- wp:woocommerce/product-collection {"query":{"perPage":4,"pages":0,"offset":0,"postType":"product","order":"desc","orderBy":"date","search":"","exclude":[],"inherit":false,"taxQuery":{},"isProductCollectionBlock":true,"featured":false,"woocommerceOnSale":false,"woocommerceStockStatus":["instock","outofstock","onbackorder"],"woocommerceAttributes":[],"woocommerceHandPickedProducts":[]},"tagName":"div","displayLayout":{"type":"flex","columns":4},"className":"related-products-grid"} /-->
    </div>
    <!-- /wp:group -->
    
    <!-- Complementary Products Section -->
    <!-- wp:group {"className":"complementary-products-section","layout":{"type":"constrained"}} -->
    <div class="wp-block-group complementary-products-section">
        <!-- wp:heading {"textAlign":"center"} -->
        <h2 class="wp-block-heading has-text-align-center">Produtos Complementares</h2>
        <!-- /wp:heading -->
        
        <!-- wp:paragraph {"textAlign":"center"} -->
        <p class="has-text-align-center">Outros itens que você pode precisar para completar seu projeto</p>
        <!-- /wp:paragraph -->
        
        <!-- wp:html -->
        <div class="complementary-products-grid">
            <!-- Complementary products will be populated via PHP based on product category -->
        </div>
        <!-- /wp:html -->
    </div>
    <!-- /wp:group -->
    
</main>
<!-- /wp:group -->

<!-- wp:template-part {"slug":"footer","className":"site-footer"} /-->
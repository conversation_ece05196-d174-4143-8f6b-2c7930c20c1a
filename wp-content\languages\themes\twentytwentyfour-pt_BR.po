# Translation of Themes - Twenty Twenty-Four in Portuguese (Brazil)
# This file is distributed under the same license as the Themes - Twenty Twenty-Four package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-09-08 21:55:32+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Themes - Twenty Twenty-Four\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website. Its collection of templates and patterns tailor to different needs, such as presenting a business, blogging and writing or showcasing work. A multitude of possibilities open up with just a few adjustments to color and typography. Twenty Twenty-Four comes with style variations and full page designs to help speed up the site building process, is fully compatible with the site editor, and takes advantage of new design tools introduced in WordPress 6.4."
msgstr "Twenty Twenty-Four é projetado para ser flexível, versátil e aplicável para qualquer site. Sua coleção de modelos e padrões se molda para diferentes necessidades, como apresentar um negócio, escrever um blog ou mostrar um trabalho. Uma multidão de possibilidades se abrem com apenas alguns ajustes de cor e tipografia. Twenty Twenty-Four vem com variações de estilo e designs de página completa para ajudar a acelerar o processo de montar o site, é totalmente compatível com o editor do site e tem a vantagem das novas ferramentas de design introduzidas no WordPress 6.4."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four"
msgstr "Twenty Twenty-Four"

#: patterns/text-title-left-image-right.php
msgctxt "Pattern description"
msgid "A title, a paragraph and a CTA button on the left with an image on the right."
msgstr "Um título, um parágrafo e um botão CTA à esquerda com uma imagem à direita."

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern description"
msgid "A feature grid of 2 rows and 3 columns with headings and text."
msgstr "Uma grade de recursos de 2 linhas e 3 colunas com títulos e texto."

#: patterns/text-faq.php:35
msgctxt "Question in the FAQ pattern"
msgid "Who is behind Études?"
msgstr "Quem está por trás dos Études?"

#: patterns/team-4-col.php
msgctxt "Pattern description"
msgid "A team section, with a heading, a paragraph, and 4 columns for team members."
msgstr "Uma seção de equipe, com um título, um parágrafo e 4 colunas para os membros da equipe."

#: patterns/posts-list.php
msgctxt "Pattern description"
msgid "A list of posts without images, 1 column."
msgstr "Uma lista de posts sem imagens, 1 coluna."

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern description"
msgid "A list of posts with featured images only, 4 columns."
msgstr "Uma lista de posts apenas com imagens em destaque, 4 colunas."

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern description"
msgid "A list of posts with featured images only, 3 columns."
msgstr "Uma lista de posts apenas com imagens em destaque, 3 colunas."

#: patterns/posts-grid-2-col.php
msgctxt "Pattern description"
msgid "A grid of posts featuring the first post, 2 columns."
msgstr "Uma grade de posts apresentando o primeiro post, 2 colunas."

#: patterns/posts-3-col.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns."
msgstr "Uma lista de posts, 3 colunas."

#: patterns/posts-1-col.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column."
msgstr "Uma lista de posts, 1 coluna."

#: patterns/page-portfolio-overview.php
msgctxt "Pattern description"
msgid "A full portfolio page with a section for project description, project details, a full screen image, and a gallery section with two images."
msgstr "Uma página de portfólio completa com uma seção para descrição do projeto, detalhes do projeto, uma imagem em tela cheia e uma seção de galeria com duas imagens."

#: patterns/page-newsletter-landing.php
msgctxt "Pattern description"
msgid "A block with a newsletter subscription CTA for a landing page."
msgstr "Um bloco com um botão de assinatura de boletim informativo para uma página de conversão."

#: patterns/page-home-portfolio.php
msgctxt "Pattern description"
msgid "A portfolio home page with a description and a 4-column post section with only feature images."
msgstr "Uma página inicial de portfólio com uma descrição e uma seção de posts em 4 colunas com apenas imagens de destaque."

#: patterns/page-home-business.php
msgctxt "Pattern description"
msgid "A business home page with a hero section, a text section, a services section, a team section, a clients section, a FAQ section, and a CTA section."
msgstr "Uma página inicial empresarial com uma seção de imagem grande, uma seção de texto, uma seção de serviços, uma seção de equipe, uma seção de clientes, uma seção de perguntas frequentes e uma seção de CTA."

#: patterns/page-home-blogging.php
msgctxt "Pattern description"
msgid "A blogging home page with a hero section, a text section, a blog section, and a CTA section."
msgstr "Uma página inicial de blog com uma seção de imagem grande, uma seção de texto, uma seção de blog e uma seção de CTA."

#: patterns/page-about-business.php
msgctxt "Pattern description"
msgid "A business about page with a hero section, a text section, a services section, a team section, a clients section, a FAQ section, and a CTA section."
msgstr "Uma página sobre uma empresa com uma seção de imagem grande, uma seção de texto, uma seção de serviços, uma seção de equipe, uma seção de clientes, uma seção de perguntas frequentes e uma seção de CTA."

#: patterns/gallery-project-layout.php
msgctxt "Pattern description"
msgid "A gallery section with a project layout with 2 images."
msgstr "Uma seção de galeria com um leiaute de projeto com 2 imagens."

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern description"
msgid "A gallery section with 4 columns and offset images."
msgstr "Uma seção de galeria com 4 colunas e imagens deslocadas."

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern description"
msgid "A gallery section with 3 columns and offset images."
msgstr "Uma seção de galeria com 3 colunas e imagens deslocadas."

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern description"
msgid "A gallery section with 2 columns and offset images."
msgstr "Uma seção de galeria com 2 colunas e imagens deslocadas."

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern description"
msgid "A cover image section that covers the entire width."
msgstr "Uma seção de imagem de capa que cobre toda a largura."

#: patterns/footer.php
msgctxt "Pattern description"
msgid "A footer section with a colophon and 4 columns."
msgstr "Uma seção de rodapé com um colofão e 4 colunas."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern description"
msgid "A footer section with a colophon and 3 columns."
msgstr "Uma seção de rodapé com um colofão e 3 colunas."

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern description"
msgid "A footer section with a centered logo, navigation, and WordPress credits."
msgstr "Uma seção de rodapé com logo centralizado, navegação e créditos do WordPress."

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern description"
msgid "Subscribers CTA section with a title, a paragraph and a CTA button."
msgstr "Seção de chamada à ação para assinantes com um título, um parágrafo e um botão."

#: patterns/cta-services-image-left.php
msgctxt "Pattern description"
msgid "An image, title, paragraph and a CTA button to describe services."
msgstr "Uma imagem, título, parágrafo e um botão CTA para descrever serviços."

#: patterns/cta-rsvp.php patterns/page-rsvp-landing.php
msgctxt "Pattern description"
msgid "A large RSVP heading sideways, a description, and a CTA button."
msgstr "Um grande título de confirmação de presença na lateral, uma descrição e um botão."

#: patterns/cta-pricing.php
msgctxt "Pattern description"
msgid "A pricing section with a title, a paragraph and three pricing levels."
msgstr "Uma seção de preços com um título, um parágrafo e três níveis de preços."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern description"
msgid "A title, paragraph, two CTA buttons, and an image for a general CTA section."
msgstr "Um título, parágrafo, dois botões e uma imagem para uma seção geral de chamada à ação."

#: patterns/banner-project-description.php
msgctxt "Pattern description"
msgid "Project description section with title, paragraph, and an image."
msgstr "Seção de descrição do projeto com título, parágrafo e uma imagem."

#: patterns/banner-hero.php
msgctxt "Pattern description"
msgid "A hero section with a title, a paragraph, a CTA button, and an image."
msgstr "Uma seção de herói com um título, um parágrafo, um botão e uma imagem."

#: patterns/footer.php:96
msgid "Twitter/X"
msgstr "Twitter/X"

#: patterns/footer.php:74
msgid "Contact Us"
msgstr "Fale conosco"

#: patterns/footer.php:73
msgid "Terms and Conditions"
msgstr "Termos e condições"

#: patterns/footer.php:72
msgid "Privacy Policy"
msgstr "Política de privacidade"

#: patterns/footer.php:51
msgid "Careers"
msgstr "Carreiras"

#: patterns/footer.php:50
msgid "History"
msgstr "História"

#: patterns/footer.php:49
msgid "Team"
msgstr "Equipe"

#: patterns/posts-list.php
msgctxt "Pattern title"
msgid "List of posts without images, 1 column"
msgstr "Lista de posts sem imagens, 1 coluna"

#: functions.php:200
msgid "A collection of full page layouts."
msgstr "Uma coleção de modelos de página completa."

#: functions.php:199
msgctxt "Block pattern category"
msgid "Pages"
msgstr "Páginas"

#: patterns/hidden-portfolio-hero.php:16
msgid "I’m <em>Leia Acosta</em>, a passionate photographer who finds inspiration in capturing the fleeting beauty of life."
msgstr "Sou <em>Leia Acosta</em>, uma fotógrafa apaixonada que encontra inspiração em capturar a beleza passageira da vida."

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Rodapé"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Cabeçalho"

#. Author URI of the theme
#. Translators: WordPress link.
#: style.css patterns/footer-centered-logo-nav.php:22
#: patterns/footer-colophon-3-col.php:91 patterns/footer.php:117
msgid "https://wordpress.org"
msgstr "https://br.wordpress.org"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Metadados do post"

#: styles/fossil.json styles/ice.json theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Extra grande"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "Grande"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Média"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Contraste"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Base"

#: theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: patterns/cta-content-image-on-right.php:24
#: patterns/text-alternating-images.php:44
msgctxt "Sample list item"
msgid "Collaborate with fellow architects."
msgstr "Colabore com outros arquitetos."

#: patterns/cta-content-image-on-right.php:18
msgctxt "Sample heading"
msgid "Enhance your architectural journey with the Études Architect app."
msgstr "Melhore sua jornada arquitetônica com o aplicativo Études Architect."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern title"
msgid "Call to action with image on right"
msgstr "Chamada para ação com imagem à direita"

#: patterns/banner-project-description.php:41
msgid "Hyatt Regency San Francisco, San Francisco, United States"
msgstr "Hyatt Regency San Francisco, São Francisco, Estados Unidos"

#: patterns/banner-project-description.php:26
msgctxt "Sample descriptive text for a project or post."
msgid "This transformative project seeks to enhance the gallery's infrastructure, accessibility, and exhibition spaces while preserving its rich cultural heritage."
msgstr "Este projeto transformador busca melhorar a infraestrutura, a acessibilidade e os espaços de exposição da galeria, ao mesmo tempo em que preserva sua rica herança cultural."

#: patterns/banner-project-description.php:17
msgctxt "Sample title for a project or post"
msgid "Art Gallery — Overview"
msgstr "Galeria de arte — Visão geral"

#: patterns/banner-project-description.php
msgctxt "Pattern title"
msgid "Project description"
msgstr "Descrição do projeto"

#: patterns/banner-hero.php:52
msgid "Building exterior in Toronto, Canada"
msgstr "Exterior de edifício em Toronto, Canadá"

#: patterns/banner-hero.php:37
msgctxt "Button text of the hero section"
msgid "About us"
msgstr "Quem somos"

#: patterns/banner-hero.php:26
msgctxt "Content of the hero section"
msgid "Études is a pioneering firm that seamlessly merges creativity and functionality to redefine architectural excellence."
msgstr "A Études é uma empresa pioneira que une criatividade e funcionalidade para redefinir a excelência arquitetônica."

#: patterns/banner-hero.php:18
msgctxt "Heading of the hero section"
msgid "A commitment to innovation and sustainability"
msgstr "Um compromisso com a inovação e a sustentabilidade"

#: patterns/banner-hero.php
msgctxt "Pattern title"
msgid "Hero"
msgstr "Herói"

#: functions.php:111
msgid "With asterisk"
msgstr "Com asterisco"

#: functions.php:93
msgid "With arrow"
msgstr "Com seta"

#: functions.php:74
msgid "Checkmark"
msgstr "Marca de seleção"

#: functions.php:51
msgid "Pill"
msgstr "Pílula"

#: functions.php:28
msgid "Arrow icon"
msgstr "Ícone de seta"

#: patterns/cta-rsvp.php:50 patterns/text-title-left-image-right.php:51
msgid "A ramp along a curved wall in the Kiasma Museu, Helsinki, Finland"
msgstr "Uma rampa ao longo de uma parede curva no Museu Kiasma, Helsinque, Finlândia"

#: patterns/cta-rsvp.php:34 patterns/page-rsvp-landing.php:34
msgctxt "Call to action button text for the reservation button"
msgid "Reserve your spot"
msgstr "Reserve seu lugar"

#: patterns/cta-rsvp.php:27 patterns/page-rsvp-landing.php:28
msgctxt "RSVP call to action description"
msgid "Experience the fusion of imagination and expertise with Études Arch Summit, February 2025."
msgstr "Experimente a fusão de imaginação e experiência com o Études Arch Summit, em fevereiro de 2025."

#: patterns/cta-rsvp.php:21 patterns/page-rsvp-landing.php:23
msgctxt "Initials for ´please respond´"
msgid "RSVP"
msgstr "Confirmar presença"

#: patterns/cta-rsvp.php:11
msgctxt "Name of RSVP pattern"
msgid "RSVP"
msgstr "Confirmar presença"

#: patterns/cta-rsvp.php
msgctxt "Pattern title"
msgid "RSVP"
msgstr "Confirmar presença"

#: patterns/cta-pricing.php:203
msgctxt "Button text for the third pricing level"
msgid "Subscribe"
msgstr "Assinar"

#: patterns/cta-pricing.php:189
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android"
msgstr "Acesso exclusivo ao aplicativo <em>Études</em> para iOS e Android"

#: patterns/cta-pricing.php:173
msgctxt "Feature for pricing level"
msgid "Exclusive, unlimited access to <em>Études Articles</em>."
msgstr "Acesso exclusivo e ilimitado aos <em>artigos do Études</em>."

#: patterns/cta-pricing.php:162
msgctxt "Sample price for the third pricing level"
msgid "$28"
msgstr "$28"

#: patterns/cta-pricing.php:145
msgctxt "Button text for the second pricing level"
msgid "Subscribe"
msgstr "Assinar"

#: patterns/cta-pricing.php:115
msgctxt "Feature for pricing level"
msgid "Access to 20 exclusive <em>Études Articles</em> per month."
msgstr "Acesso a 20 artigos exclusivos do <em>Études</em> por mês."

#: patterns/cta-pricing.php:104
msgctxt "Sample price for the second pricing level"
msgid "$12"
msgstr "$12"

#: patterns/cta-pricing.php:157
msgctxt "Sample heading for the third pricing level"
msgid "Expert"
msgstr "Avançado"

#: patterns/cta-pricing.php:99
msgctxt "Sample heading for the second pricing level"
msgid "Connoisseur"
msgstr "Intermediário"

#: patterns/cta-pricing.php:87
msgctxt "Button text for the first pricing level"
msgid "Subscribe"
msgstr "Assinar"

#: patterns/cta-pricing.php:72 patterns/cta-pricing.php:131
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android."
msgstr "Acesso exclusivo ao aplicativo <em>Études</em> para iOS e Android."

#: patterns/cta-pricing.php:62 patterns/cta-pricing.php:123
#: patterns/cta-pricing.php:181
msgctxt "Feature for pricing level"
msgid "Weekly print edition."
msgstr "Edição impressa semanal."

#: patterns/cta-pricing.php:53
msgctxt "Feature for pricing level"
msgid "Access to 5 exclusive <em>Études Articles</em> per month."
msgstr "Acesso a 5 artigos exclusivos do <em>Études</em> por mês."

#: patterns/cta-pricing.php:42
msgctxt "Sample price for the first pricing level"
msgid "$0"
msgstr "$0"

#: patterns/cta-pricing.php:37
msgctxt "Sample heading for the first pricing level"
msgid "Free"
msgstr "Grátis"

#: patterns/cta-pricing.php:22
msgctxt "Sample description for a pricing table"
msgid "We offer flexible options, which you can adapt to the different needs of each project."
msgstr "Oferecemos opções flexíveis, que você pode adaptar às diferentes necessidades de cada projeto."

#: patterns/cta-pricing.php:18
msgctxt "Sample heading for pricing pattern"
msgid "Our Services"
msgstr "Nossos serviços"

#: patterns/cta-pricing.php:11
msgctxt "Name for the pricing pattern"
msgid "Pricing Table"
msgstr "Tabela de preços"

#: patterns/cta-pricing.php
msgctxt "Pattern title"
msgid "Pricing"
msgstr "Preços"

#: patterns/cta-content-image-on-right.php:59
#: patterns/cta-services-image-left.php:19
msgid "White abstract geometric artwork from Dresden, Germany"
msgstr "Arte geométrica abstrata branca de Dresden, Alemanha"

#: patterns/cta-content-image-on-right.php:47
msgctxt "Button text of this section"
msgid "How it works"
msgstr "Como funciona"

#: patterns/cta-content-image-on-right.php:41
msgctxt "Button text of this section"
msgid "Download app"
msgstr "Baixar o aplicativo"

#: patterns/cta-content-image-on-right.php:32
#: patterns/text-alternating-images.php:52
msgctxt "Sample list item"
msgid "Experience the world of architecture."
msgstr "Experimente o mundo da arquitetura."

#: patterns/cta-content-image-on-right.php:28
#: patterns/text-alternating-images.php:48
msgctxt "Sample list item"
msgid "Showcase your projects."
msgstr "Mostre os seus projetos."

#: patterns/gallery-project-layout.php:26
msgctxt "Sample text for the feature area"
msgid "1. Through Études, we aspire to redefine architectural boundaries and usher in a new era of design excellence that leaves an indelible mark on the built environment."
msgstr "1. Por meio do Études, queremos redefinir os limites arquitetônicos e inaugurar uma nova era de excelência em design que deixe uma marca indelével no ambiente construído."

#: patterns/gallery-project-layout.php:21
msgid "An empty staircase under an angular roof in Darling Harbour, Sydney, Australia"
msgstr "Uma escada vazia sob um telhado angular em Darling Harbour, Sydney, Austrália"

#: patterns/gallery-project-layout.php
msgctxt "Pattern title"
msgid "Project layout"
msgstr "Leiaute dos projetos"

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 4 columns"
msgstr "Galeria com deslocamento, 4 colunas"

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 3 columns"
msgstr "Galeria com deslocamento, 3 colunas"

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 2 columns"
msgstr "Galeria com deslocamento, 2 colunas"

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern title"
msgid "Full screen image"
msgstr "Imagem em tela cheia"

#: patterns/footer.php:92
msgid "Social Media"
msgstr "Mídia social"

#: patterns/footer.php:86
msgid "Social"
msgstr "Redes sociais"

#: patterns/footer.php:64 patterns/footer.php:70
msgid "Privacy"
msgstr "Privacidade"

#: patterns/footer.php:41 patterns/footer.php:47
msgid "About"
msgstr "Sobre"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer with colophon, 4 columns"
msgstr "Rodapé com colofão, 4 colunas"

#: patterns/footer-colophon-3-col.php:82
msgid "&copy;"
msgstr "&copy;"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:94
msgid "Facebook"
msgstr "Facebook"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:95
msgid "Instagram"
msgstr "Instagram"

#: patterns/footer-colophon-3-col.php:57
msgid "Follow"
msgstr "Seguir"

#: patterns/footer-colophon-3-col.php:42
msgctxt "Example email in site footer"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/footer-colophon-3-col.php:39
msgid "Contact"
msgstr "Contato"

#: patterns/footer-colophon-3-col.php:30
msgid "Keep up, get in touch."
msgstr "Mantenha-se informado, entre em contato."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern title"
msgid "Footer with colophon, 3 columns"
msgstr "Rodapé com colofão, 3 colunas"

#. Translators: Designed with WordPress
#: patterns/footer-centered-logo-nav.php:25
#: patterns/footer-colophon-3-col.php:94 patterns/footer.php:120
msgid "Designed with %1$s"
msgstr "Criado com %1$s"

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern title"
msgid "Footer with centered logo and navigation"
msgstr "Rodapé com logo centralizado e navegação"

#: patterns/cta-subscribe-centered.php:31
msgctxt "Sample text for Sign Up Button"
msgid "Sign up"
msgstr "Registrar"

#: patterns/cta-subscribe-centered.php:24
msgctxt "Sample text for Subscriber Description"
msgid "Stay in the loop with everything you need to know."
msgstr "Fique por dentro de tudo o que você precisa saber."

#: patterns/cta-subscribe-centered.php:20
msgctxt "Sample text for Subscriber Heading with numbers"
msgid "Join 900+ subscribers"
msgstr "Junte-se a mais de 900 inscritos"

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern title"
msgid "Centered call to action"
msgstr "Chamada para ação centralizada"

#: patterns/cta-services-image-left.php:39
msgctxt "Sample button text to view the services"
msgid "Our services"
msgstr "Nossos serviços"

#: patterns/cta-services-image-left.php:32
msgctxt "Sample description of the services pattern"
msgid "Experience the fusion of imagination and expertise with Études—the catalyst for architectural transformations that enrich the world around us."
msgstr "Experimente a fusão de imaginação e experiência com Études, o catalisador para transformações arquitetônicas que enriquecem o mundo ao nosso redor."

#: patterns/cta-services-image-left.php:28
msgctxt "Sample heading of the services pattern"
msgid "Guiding your business through the project"
msgstr "Guiando seu negócio através do projeto"

#: patterns/cta-services-image-left.php
msgctxt "Pattern title"
msgid "Services call to action with image on left"
msgstr "Chamada para ação de serviços com imagem à esquerda"

#: patterns/hidden-sidebar.php:75
msgctxt "search form placeholder"
msgid "Search..."
msgstr "Pesquisar..."

#: patterns/hidden-sidebar.php:72
msgid "Search the website"
msgstr "Pesquisar no site"

#: patterns/hidden-sidebar.php:60
msgid "Financial apps for families"
msgstr "Aplicativos financeiros para famílias"

#: patterns/hidden-sidebar.php:59
msgid "Latest inflation report"
msgstr "Relatório de inflação mais recente"

#: patterns/hidden-sidebar.php:53
msgid "Links I found useful and wanted to share."
msgstr "Links que achei úteis e queria compartilhar."

#: patterns/hidden-sidebar.php:49
msgid "Useful Links"
msgstr "Links úteis"

#: patterns/hidden-sidebar.php:33
msgid "Popular Categories"
msgstr "Categorias populares"

#: patterns/hidden-sidebar.php:17
msgid "About the author"
msgstr "Sobre o autor"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Barra lateral"

#: patterns/hidden-search.php:9
msgctxt "search button text"
msgid "Search"
msgstr "Pesquisar"

#: patterns/hidden-search.php:9 patterns/hidden-sidebar.php:75
msgctxt "search form label"
msgid "Search"
msgstr "Pesquisa"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Pesquisa"

#: patterns/hidden-post-navigation.php:12
msgctxt "Label before the title of the next post. There is a space after the colon."
msgid "Next: "
msgstr "Próximo: "

#: patterns/hidden-post-navigation.php:11
msgctxt "Label before the title of the previous post. There is a space after the colon."
msgid "Previous: "
msgstr "Anterior: "

#: patterns/hidden-post-navigation.php:9 patterns/hidden-post-navigation.php:10
#: patterns/hidden-posts-heading.php:10
#: patterns/template-index-portfolio.php:16
msgid "Posts"
msgstr "Posts"

#: patterns/hidden-post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Navegação de posts"

#: patterns/hidden-post-meta.php:25
msgctxt "Prefix for the post category block: in category name"
msgid "in "
msgstr "em "

#: patterns/hidden-post-meta.php:20
msgctxt "Prefix for the post author block: By author name"
msgid "by"
msgstr "por"

#: patterns/hidden-post-meta.php
msgctxt "Pattern title"
msgid "Post meta"
msgstr "Informações do post"

#: patterns/hidden-no-results.php:9
msgctxt "Message explaining that there are no results returned from a search"
msgid "No posts were found."
msgstr "Nenhum post foi encontrado."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "No results"
msgstr "Sem resultados"

#: patterns/hidden-comments.php:12
msgid "Comments"
msgstr "Comentários"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Comentários"

#: patterns/hidden-404.php:13
msgctxt "Message to convey that a webpage could not be found"
msgid "The page you are looking for does not exist, or it has been moved. Please try searching using the form below."
msgstr "A página que você está procurando não existe ou foi movida. Tente pesquisar usando o formulário abaixo."

#: patterns/hidden-404.php:10
msgctxt "Heading for a webpage that is not found"
msgid "Page Not Found"
msgstr "Página não encontrada"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/gallery-project-layout.php:54
msgid "Art Gallery of Ontario, Toronto, Canada"
msgstr "Galeria de arte de Ontário, Toronto, Canadá"

#: patterns/gallery-project-layout.php:49
msgctxt "Sample text for the feature area"
msgid "2. Case studies that celebrate the artistry can fuel curiosity and ignite inspiration."
msgstr "2. Estudos de caso que celebram a arte podem alimentar a curiosidade e despertar inspiração."

#: patterns/gallery-project-layout.php:38
msgctxt "Sample text for the feature area"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers. With a commitment to innovation and sustainability, Études is the bridge that transforms architectural dreams into remarkable built realities."
msgstr "Nosso conjunto abrangente de serviços profissionais atende a uma clientela diversificada, que vai de proprietários de imóveis a desenvolvedores comerciais. Com um compromisso com a inovação e a sustentabilidade, a Études é a ponte que transforma sonhos arquitetônicos em realidades construídas notáveis."

#: patterns/team-4-col.php:121
msgctxt "Sample role of a team member"
msgid "Project Manager"
msgstr "Gerente de projeto"

#: patterns/team-4-col.php:116
msgctxt "Sample name of a team member"
msgid "Ivan Lawrence"
msgstr "Ivan Lawrence"

#: patterns/team-4-col.php:97
msgctxt "Sample role of a team member"
msgid "Architect"
msgstr "Arquiteto"

#: patterns/team-4-col.php:92
msgctxt "Sample name of a team member"
msgid "Helga Steiner"
msgstr "Helga Steiner"

#: patterns/team-4-col.php:73
msgctxt "Sample role of a team member"
msgid "Engineering Manager"
msgstr "Diretor de engenharia"

#: patterns/team-4-col.php:68
msgctxt "Sample name of a team member"
msgid "Rhye Moore"
msgstr "Rhye Moore"

#: patterns/team-4-col.php:49
msgctxt "Sample role of a team member"
msgid "Founder, CEO & Architect"
msgstr "Fundador, diretor executivo e arquiteto"

#: patterns/team-4-col.php:44
msgctxt "Sample name of a team member"
msgid "Francesca Piovani"
msgstr "Francesca Piovani"

#: patterns/team-4-col.php:20
msgctxt "Sample descriptive text of the team pattern"
msgid "Our comprehensive suite of professionals caters to a diverse team, ranging from seasoned architects to renowned engineers."
msgstr "Nosso abrangente conjunto de profissionais atende a uma equipe diversificada, que vai de arquitetos experientes a engenheiros renomados."

#: patterns/team-4-col.php:16
msgctxt "Sample heading for the team pattern"
msgid "Meet our team"
msgstr "Conheça nossa equipe"

#: patterns/team-4-col.php:11
msgctxt "Name of team pattern"
msgid "Team members"
msgstr "Membros da equipe"

#: patterns/team-4-col.php
msgctxt "Pattern title"
msgid "Team members, 4 columns"
msgstr "Membros da equipe, 4 colunas"

#: patterns/posts-grid-2-col.php:14 patterns/posts-list.php:14
#: patterns/template-index-blogging.php:16
msgid "Watch, Read, Listen"
msgstr "Veja, leia, ouça"

#: theme.json
msgctxt "Custom template name"
msgid "Single with Sidebar"
msgstr "Conteúdo com barra lateral"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Wide Image"
msgstr "Página com imagem ampla"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Sidebar"
msgstr "Página com barra lateral"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Página sem título"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Barra lateral"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to white"
msgstr "Vertical, sem transição, cinza para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard mint to white"
msgstr "Vertical, sem transição, menta para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard rust to white"
msgstr "Vertical, sem transição, ferrugem para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard beige to white"
msgstr "Vertical, sem transição, bege para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sandstone to white"
msgstr "Vertical, sem transição, areia para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to white"
msgstr "Vertical, cinza para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft mint to white"
msgstr "Vertical, menta para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft rust to white"
msgstr "Vertical, ferrugem para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sandstone to white"
msgstr "Vertical, areia para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft beige to white"
msgstr "Vertical, bege para branco"

#: theme.json
msgctxt "Duotone name"
msgid "Black and pastel blue"
msgstr "Preto e azul pastel"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sage to white"
msgstr "Vertical, sem transição, sálvia para branco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sage to white"
msgstr "Vertical, sálvia para branco"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sage"
msgstr "Preto e sálvia"

#: theme.json
msgctxt "Duotone name"
msgid "Black and rust"
msgstr "Preto e ferrugem"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sandstone"
msgstr "Preto e areia"

#: theme.json
msgctxt "Duotone name"
msgid "Black and white"
msgstr "Preto e branco"

#: styles/rust.json
msgctxt "Color name"
msgid "Base / 2"
msgstr "Base / 2"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard rust to beige"
msgstr "Vertical, sem transição, ferrugem para bege"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical rust to beige"
msgstr "Vertical, ferrugem para bege"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard transparent rust to beige"
msgstr "Vertical, sem transição, ferrugem transparente para bege"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical transparent rust to beige"
msgstr "Vertical, ferrugem transparente para bege"

#: styles/rust.json
msgctxt "Duotone name"
msgid "Dark rust to beige"
msgstr "Ferrugem escura para bege"

#: styles/rust.json
msgctxt "Style variation name"
msgid "Rust"
msgstr "Ferrugem"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Five"
msgstr "Realce / Cinco"

#: patterns/text-title-left-image-right.php:35
msgctxt "Call to Action button text"
msgid "About us"
msgstr "Quem somos"

#: patterns/text-title-left-image-right.php:28
msgctxt "Description for the About pattern"
msgid "Leaving an indelible mark on the landscape of tomorrow."
msgstr "Deixando uma marca indelével na paisagem do amanhã."

#: patterns/text-title-left-image-right.php:21
msgctxt "Headline for the About pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Every architectural endeavor is an opportunity to shape the future."
msgstr "A Études oferece soluções abrangentes de consultoria, gestão, design e pesquisa. Cada empreendimento arquitetônico é uma oportunidade de moldar o futuro."

#: patterns/text-title-left-image-right.php
msgctxt "Pattern title"
msgid "Title text and button on left with image on right"
msgstr "Texto do título e botão à esquerda com imagem à direita"

#: patterns/text-project-details.php:35 patterns/text-project-details.php:43
msgctxt "Descriptive text for the feature area"
msgid "The revitalized Art Gallery is set to redefine the cultural landscape of Toronto, serving as a nexus of artistic expression, community engagement, and architectural marvel. The expansion and renovation project pay homage to the Art Gallery's rich history while embracing the future, ensuring that the gallery remains a beacon of inspiration."
msgstr "A revitalizada Art Gallery está pronta para redefinir o cenário cultural de Toronto, servindo como um nexo de expressão artística, engajamento comunitário e maravilha arquitetônica. O projeto de expansão e renovação homenageia a rica história da Art Gallery ao mesmo tempo em que abraça o futuro, garantindo que a galeria continue sendo um farol de inspiração."

#: patterns/text-project-details.php:27
msgctxt "Descriptive title for the feature area"
msgid "With meticulous attention to detail and a commitment to excellence, we create spaces that inspire, elevate, and enrich the lives of those who inhabit them."
msgstr "Com atenção meticulosa aos detalhes e compromisso com a excelência, criamos espaços que inspiram, elevam e enriquecem a vida daqueles que os habitam."

#: patterns/text-project-details.php:18
msgctxt "Title text for the feature area"
msgid "The revitalized art gallery is set to redefine cultural landscape."
msgstr "A galeria de arte revitalizada pretende redefinir a paisagem cultural."

#: patterns/text-project-details.php
msgctxt "Pattern title"
msgid "Project details"
msgstr "Detalhes do projeto"

#: patterns/text-feature-grid-3-col.php:112
msgctxt "Sample content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Experimente a fusão de imaginação e experiência com a Études Architectural Solutions."

#: patterns/text-feature-grid-3-col.php:108
msgctxt "Sample heading"
msgid "Architectural Solutions"
msgstr "Soluções arquitetônicas"

#: patterns/text-feature-grid-3-col.php:96
msgctxt "Sample feature heading"
msgid "Project Management"
msgstr "Gerenciamento de projetos"

#: patterns/text-feature-grid-3-col.php:84
msgctxt "Sample feature heading"
msgid "Consulting"
msgstr "Consultoria"

#: patterns/text-feature-grid-3-col.php:63
msgctxt "Sample feature heading"
msgid "App Access"
msgstr "Acesso ao aplicativo"

#: patterns/text-feature-grid-3-col.php:51
msgctxt "Sample feature heading"
msgid "Continuous Support"
msgstr "Suporte contínuo"

#: patterns/text-feature-grid-3-col.php:43
#: patterns/text-feature-grid-3-col.php:55
#: patterns/text-feature-grid-3-col.php:67
#: patterns/text-feature-grid-3-col.php:88
#: patterns/text-feature-grid-3-col.php:100
msgctxt "Sample feature content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Experimente a fusão de imaginação e experiência com a Études Architectural Solutions."

#: patterns/text-feature-grid-3-col.php:39
msgctxt "Sample feature heading"
msgid "Renovation and restoration"
msgstr "Renovação e restauração"

#: patterns/text-feature-grid-3-col.php:24
msgctxt "Sub-heading of the features"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Nosso conjunto abrangente de serviços profissionais atende a uma clientela diversificada, desde proprietários de imóveis até incorporadores comerciais."

#: patterns/text-feature-grid-3-col.php:16
msgctxt "Heading of the features"
msgid "A passion for creating spaces"
msgstr "Uma paixão por criar espaços"

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern title"
msgid "Feature grid, 3 columns"
msgstr "Grade de recursos, 3 colunas"

#: patterns/text-faq.php:57
msgctxt "Question in the FAQ pattern"
msgid "Can I apply to be a part of the team or work as a contractor?"
msgstr "Posso me candidatar para fazer parte da equipe ou trabalhar como terceirizado?"

#: patterns/text-faq.php:46
msgctxt "Question in the FAQ pattern"
msgid "I'd like to get to meet fellow architects, how can I do that?"
msgstr "Gostaria de conhecer outros arquitetos. Como posso fazer isso?"

#: patterns/text-faq.php:27 patterns/text-faq.php:38 patterns/text-faq.php:49
#: patterns/text-faq.php:60
msgctxt "Answer in the FAQ pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future."
msgstr "A Études oferece soluções abrangentes de consultoria, gestão, design e pesquisa. Nossa visão é estar na vanguarda da inovação arquitetônica, fomentando uma comunidade global de arquitetos e entusiastas unidos pela paixão de criar espaços. Cada empreendimento arquitetônico é uma oportunidade de moldar o futuro."

#: patterns/text-faq.php:24
msgctxt "Question in the FAQ pattern"
msgid "What is your process working in smaller projects?"
msgstr "Qual é o seu processo de trabalho em projetos menores?"

#: patterns/text-faq.php:15
msgctxt "Heading of the FAQs"
msgid "FAQs"
msgstr "Perguntas frequentes"

#: patterns/text-faq.php:12
msgctxt "Name of the FAQ pattern"
msgid "FAQs"
msgstr "Perguntas frequentes"

#: patterns/text-centered-statement.php
msgctxt "Pattern title"
msgid "Centered statement"
msgstr "Declaração centralizada"

#. Translators: About text placeholder
#: patterns/text-centered-statement-small.php:23
msgid "I write about finance, management and economy, my book “%1$s” is out now."
msgstr "Escrevo sobre finanças, gestão e economia, meu livro \"%1$s\" já foi lançado."

#. Translators: About link placeholder
#: patterns/text-centered-statement-small.php:20
msgid "Money Studies"
msgstr "Estudos financeiros"

#: patterns/text-centered-statement-small.php
msgctxt "Pattern title"
msgid "Centered statement, small"
msgstr "Declaração centralizada, pequena"

#: patterns/text-alternating-images.php:105
msgctxt "Sample list item"
msgid "Exclusive access to design insights."
msgstr "Acesso exclusivo a informações sobre design."

#: patterns/text-alternating-images.php:101
msgctxt "Sample list item"
msgid "Case studies that celebrate architecture."
msgstr "Estudos de caso que celebram a arquitetura."

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Four"
msgstr "Realce / Quatro"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Three"
msgstr "Realce / Três"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Two"
msgstr "Realce / Dois"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent"
msgstr "Realce"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to dark gray"
msgstr "Vertical, sem transição, prateado para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard steel to dark gray"
msgstr "Vertical, sem transição, aço para cinza escuro"

#: patterns/text-centered-statement.php:21
msgid "<em>Études</em> is not confined to the past—we are passionate about the cutting edge designs shaping our world today."
msgstr "<em>A Études</em> não se limita ao passado: somos apaixonados pelos designs de vanguarda que moldam o mundo de hoje."

#: patterns/text-alternating-images.php:97
msgctxt "Sample list item"
msgid "A world of thought-provoking articles."
msgstr "Um mundo de artigos instigantes."

#: patterns/text-alternating-images.php:91
msgctxt "Sample heading"
msgid "Études Newsletter"
msgstr "Boletim da Études"

#: patterns/text-alternating-images.php:82
msgid "Windows of a building in Nuremberg, Germany"
msgstr "Janelas de um edifício em Nuremberg, Alemanha"

#: patterns/text-alternating-images.php:64
msgid "Tourist taking photo of a building"
msgstr "Turista tirando foto de um prédio"

#: patterns/text-alternating-images.php:37
msgctxt "Sample list heading"
msgid "Études Architect App"
msgstr "Aplicativo Études Architect"

#: patterns/text-alternating-images.php:19
msgctxt "Sample heading content"
msgid "An array of resources"
msgstr "Um conjunto de recursos"

#: patterns/text-alternating-images.php
msgctxt "Pattern title"
msgid "Text with alternating images"
msgstr "Texto com imagens alternadas"

#: patterns/text-alternating-images.php:23
msgctxt "Sample subheading content"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "Nosso conjunto abrangente de serviços profissionais atende a uma clientela diversificada, desde proprietários de imóveis até incorporadores comerciais."

#: patterns/testimonial-centered.php:39
msgctxt "Designation of Person Provided Testimonial"
msgid "CEO, Greenprint"
msgstr "CEO, Greenprint"

#: patterns/testimonial-centered.php:35
msgctxt "Name of Person Provided the Testimonial"
msgid "Annie Steiner"
msgstr "Annie Steiner"

#: patterns/testimonial-centered.php:26
msgctxt "Name of testimonial citation group"
msgid "Testimonial source"
msgstr "Fonte do depoimento"

#: patterns/testimonial-centered.php:18
msgctxt "Testimonial Text or Review Text Got From the Person"
msgid "“Études has saved us thousands of hours of work and has unlocked insights we never thought possible.”"
msgstr "“Études nos poupou milhares de horas de trabalho e revelou insights que nunca imaginamos possíveis.”"

#: patterns/testimonial-centered.php:12
msgctxt "Name of testimonial pattern"
msgid "Testimonial"
msgstr "Depoimento"

#: patterns/testimonial-centered.php
msgctxt "Pattern title"
msgid "Centered testimonial"
msgstr "Depoimento centralizado"

#: patterns/template-single-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio single post template"
msgstr "Modelo de post de portfólio"

#: patterns/template-search-blogging.php
msgctxt "Pattern title"
msgid "Blogging search template"
msgstr "Modelo de pesquisa de blog"

#: patterns/template-search-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio search template"
msgstr "Modelo de pesquisa de portfólio"

#: patterns/template-index-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio index template"
msgstr "Modelo de índice de portfólio"

#: patterns/template-index-blogging.php
msgctxt "Pattern title"
msgid "Blogging index template"
msgstr "Modelo de índice de blog"

#: patterns/template-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home template with post featured images"
msgstr "Modelo de página inicial de portfólio com imagens destacadas dos posts"

#: patterns/template-home-business.php
msgctxt "Pattern title"
msgid "Business home template"
msgstr "Modelo de página inicial de negócios"

#: patterns/template-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home template"
msgstr "Modelo de página inicial de blog"

#: patterns/template-archive-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio archive template"
msgstr "Modelo de arquivo de portfólio"

#: patterns/template-archive-blogging.php
msgctxt "Pattern title"
msgid "Blogging archive template"
msgstr "Modelo de arquivo de blog"

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern title"
msgid "Offset posts with featured images only, 4 columns"
msgstr "Posts deslocados com apenas imagens destacadas, 4 colunas"

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern title"
msgid "Posts with featured images only, 3 columns"
msgstr "Posts apenas com imagens destacadas, 3 colunas"

#: patterns/posts-grid-2-col.php
msgctxt "Pattern title"
msgid "Grid of posts featuring the first post, 2 columns"
msgstr "Grade de posts apresentando o primeiro post, 2 colunas"

#: patterns/posts-3-col.php
msgctxt "Pattern title"
msgid "List of posts, 3 columns"
msgstr "Lista de posts, 3 colunas"

#: patterns/posts-1-col.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Lista de posts, 1 coluna"

#: patterns/page-newsletter-landing.php:40
msgctxt "Sample content for newsletter subscribe button"
msgid "Sign up"
msgstr "Assinar"

#: patterns/page-newsletter-landing.php:29
msgctxt "sample content for newsletter subscription"
msgid "Subscribe to the newsletter and stay connected with our community"
msgstr "Assine a newsletter e fique conectado com a nossa comunidade"

#: patterns/page-about-business.php
msgctxt "Pattern title"
msgid "About"
msgstr "Sobre"

#: patterns/page-portfolio-overview.php
msgctxt "Pattern title"
msgid "Portfolio project overview"
msgstr "Visão geral do projeto de portfólio"

#: patterns/page-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home with post featured images"
msgstr "Página inicial de portfólio com imagens destacadas dos posts"

#: patterns/page-home-portfolio-gallery.php
msgctxt "Pattern title"
msgid "Portfolio home image gallery"
msgstr "Página inicial de portfólio com galeria de imagens"

#: patterns/page-home-business.php
msgctxt "Pattern title"
msgid "Business home"
msgstr "Página inicial de negócios"

#: patterns/page-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home"
msgstr "Página inicial de blog"

#: patterns/hidden-portfolio-hero.php
msgctxt "Pattern title"
msgid "Portfolio hero"
msgstr "Imagem grande de portfólio"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Extra extra grande"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "Pequeno"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Three"
msgstr "Contraste / Três"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Two"
msgstr "Contraste / Dois"

#: styles/fossil.json
msgctxt "Style variation name"
msgid "Fossil"
msgstr "Fóssil"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Serif"
msgstr "System Serif"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Sans-serif"
msgstr "System Sans-serif"

#: styles/ember.json styles/ice.json styles/maelstrom.json styles/mint.json
msgctxt "Font family name"
msgid "Jost"
msgstr "Jost"

#: styles/ember.json styles/mint.json
msgctxt "Font family name"
msgid "Instrument Sans"
msgstr "Instrument Sans"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json theme.json
msgctxt "Color name"
msgid "Base / Two"
msgstr "Base / Dois"

#: styles/ember.json styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 2"
msgstr "Contraste / 2"

#: styles/ember.json
msgctxt "Duotone name"
msgid "Orange and white"
msgstr "Laranja e branco"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard olive to dark gray"
msgstr "Vertical, sem transição, oliva para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard cinnamon to dark gray"
msgstr "Vertical, sem transição, canela para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard walnut to dark gray"
msgstr "Vertical, sem transição, nozes para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard beige to dark gray"
msgstr "Vertical, sem transição, bege para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to dark gray"
msgstr "Vertical, prateado para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft steel to dark gray"
msgstr "Vertical, aço para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft olive to dark gray"
msgstr "Vertical, oliva para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft cinnamon to dark gray"
msgstr "Vertical, canela para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft walnut to dark gray"
msgstr "Vertical, nozes para cinza escuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft driftwood to dark gray"
msgstr "Vertical, madeira para cinza escuro"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and steel"
msgstr "Cinza escuro e aço"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and olive"
msgstr "Cinza escuro e oliva"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and cinnamon"
msgstr "Cinza escuro e canela"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and walnut"
msgstr "Cinza escuro e nozes"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and white"
msgstr "Cinza escuro e branco"

#: styles/onyx.json
msgctxt "Style variation name"
msgid "Onyx"
msgstr "Onix"

#: styles/mint.json
msgctxt "Style variation name"
msgid "Mint"
msgstr "Menta"

#: styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 3"
msgstr "Contraste / 3"

#: patterns/text-faq.php
msgctxt "Pattern title"
msgid "FAQ"
msgstr "Perguntas frequentes"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "a equipe do WordPress"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfour/"
msgstr "https://br.wordpress.org/themes/twentytwentyfour/"

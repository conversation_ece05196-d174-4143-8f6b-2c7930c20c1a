# Technology Stack

## Core Technologies
- **WordPress**: 6.8.2
- **PHP**: Minimum version 7.2.24 required
- **MySQL**: Minimum version 5.5.5 required
- **Language**: Brazilian Portuguese (pt_BR)

## Required PHP Extensions
- `json` - JSON processing
- `hash` - Hashing functions

## Key Components
- **WooCommerce**: E-commerce functionality
- **LiteSpeed Cache**: Performance optimization plugin
- **Akismet**: Spam protection
- **TinyMCE**: 49110-20250317 (Rich text editor)

## Server Configuration
- **Web Server**: Apache with mod_rewrite enabled
- **URL Rewriting**: Configured via .htaccess for pretty permalinks
- **Authorization**: HTTP Authorization header support

## Development Environment
- Local MySQL database (`trae`)
- Dynamic site URL detection
- Debug mode disabled for production-like environment

## Common Commands

### WordPress CLI (if WP-CLI is installed)
```bash
# Download WordPress core
wp core download --locale=pt_BR

# Update WordPress
wp core update

# Install/activate plugins
wp plugin install woocommerce --activate
wp plugin install litespeed-cache --activate

# Database operations
wp db export backup.sql
wp db import backup.sql

# Cache operations (LiteSpeed)
wp litespeed-purge all
```

### File Permissions (Linux/Unix)
```bash
# Set proper WordPress file permissions
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
chmod 600 wp-config.php
```

### Database Management
```bash
# MySQL backup
mysqldump -u root trae > backup.sql

# MySQL restore
mysql -u root trae < backup.sql
```

## Build Process
WordPress is a runtime CMS - no build process required. Content and configuration changes are made through the admin interface or direct file modifications.
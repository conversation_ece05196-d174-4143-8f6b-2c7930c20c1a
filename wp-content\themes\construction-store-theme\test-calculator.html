<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Calculadora de Materiais</title>
    
    <!-- CSS Variables from theme.json -->
    <style>
        :root {
            /* Primary Colors */
            --color-primary: #FF6B35;
            --color-primary-dark: #E55A2B;
            --color-primary-light: #FF8A5C;
            
            /* Secondary Colors */
            --color-secondary: #2C3E50;
            --color-secondary-light: #34495E;
            
            /* Neutral Colors */
            --color-gray-100: #F8F9FA;
            --color-gray-200: #E9ECEF;
            --color-gray-300: #DEE2E6;
            --color-gray-600: #6C757D;
            --color-gray-800: #343A40;
            --color-gray-900: #212529;
            
            /* Status Colors */
            --color-success: #28A745;
            --color-warning: #FFC107;
            --color-danger: #DC3545;
            --color-info: #17A2B8;
            
            /* Font Sizes */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            
            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.5;
            color: var(--color-gray-800);
            background: #fff;
            margin: 0;
            padding: var(--space-4);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: var(--color-gray-900);
            text-align: center;
            margin-bottom: var(--space-8);
        }
        
        .test-info {
            background: var(--color-info);
            color: white;
            padding: var(--space-4);
            border-radius: 6px;
            margin-bottom: var(--space-6);
        }
        
        .test-info h2 {
            margin: 0 0 var(--space-2) 0;
            font-size: var(--font-size-lg);
        }
        
        .test-info p {
            margin: 0;
            font-size: var(--font-size-sm);
        }
    </style>
    
    <!-- Calculator CSS -->
    <link rel="stylesheet" href="assets/css/components/material-calculator.css">
</head>
<body>
    <div class="container">
        <h1>Teste da Calculadora de Materiais</h1>
        
        <div class="test-info">
            <h2>Como testar:</h2>
            <p>1. Selecione um tipo de material (tinta, cimento, piso, etc.)</p>
            <p>2. Escolha entre área única ou múltiplas áreas</p>
            <p>3. Insira as dimensões do projeto</p>
            <p>4. Clique em "Calcular Quantidade" para ver o resultado</p>
            <p>5. Teste as opções avançadas para personalizar rendimento e margem de segurança</p>
        </div>
        
        <!-- Calculator will be inserted here -->
        <div id="calculatorContainer"></div>
        
        <div style="margin-top: var(--space-8); padding: var(--space-4); background: var(--color-gray-100); border-radius: 6px;">
            <h3>Exemplos de Teste:</h3>
            <ul>
                <li><strong>Tinta:</strong> Área de 50m² = ~4.2 litros (base) + margem = 5 litros</li>
                <li><strong>Cimento:</strong> Área de 20m² = 5 sacos (base) + margem = 6 sacos</li>
                <li><strong>Piso:</strong> Área de 30m² = 30m² (base) + margem = 33m²</li>
            </ul>
        </div>
    </div>
    
    <!-- Calculator JavaScript -->
    <script src="assets/js/material-calculator.js"></script>
    
    <script>
        // Initialize calculator when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for MaterialCalculator to be available
            setTimeout(function() {
                if (window.materialCalculator) {
                    window.materialCalculator.initializeOn('#calculatorContainer');
                }
            }, 100);
        });
    </script>
</body>
</html>
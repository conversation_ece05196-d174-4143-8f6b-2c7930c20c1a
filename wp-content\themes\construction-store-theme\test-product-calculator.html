<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Calculadora em Produto</title>
    
    <!-- CSS Variables from theme.json -->
    <style>
        :root {
            /* Primary Colors */
            --color-primary: #FF6B35;
            --color-primary-dark: #E55A2B;
            --color-primary-light: #FF8A5C;
            
            /* Secondary Colors */
            --color-secondary: #2C3E50;
            --color-secondary-light: #34495E;
            
            /* Neutral Colors */
            --color-gray-50: #FAFAFA;
            --color-gray-100: #F8F9FA;
            --color-gray-200: #E9ECEF;
            --color-gray-300: #DEE2E6;
            --color-gray-600: #6C757D;
            --color-gray-700: #495057;
            --color-gray-800: #343A40;
            --color-gray-900: #212529;
            
            /* Status Colors */
            --color-success: #28A745;
            --color-warning: #FFC107;
            --color-danger: #DC3545;
            --color-info: #17A2B8;
            
            /* Font Sizes */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            
            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            
            /* Line Heights */
            --line-height-tight: 1.25;
            --line-height-normal: 1.5;
            --line-height-relaxed: 1.75;
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: var(--line-height-normal);
            color: var(--color-gray-800);
            background: #fff;
            margin: 0;
            padding: var(--space-4);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .product-page {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-8);
            margin-bottom: var(--space-8);
        }
        
        .product-image {
            background: var(--color-gray-100);
            border-radius: 8px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-lg);
            color: var(--color-gray-600);
        }
        
        .product-info h1 {
            color: var(--color-gray-900);
            font-size: var(--font-size-3xl);
            margin: 0 0 var(--space-4) 0;
        }
        
        .product-price {
            font-size: var(--font-size-2xl);
            color: var(--color-primary);
            font-weight: 700;
            margin: 0 0 var(--space-6) 0;
        }
        
        .product-description {
            color: var(--color-gray-700);
            margin: 0 0 var(--space-6) 0;
            line-height: var(--line-height-relaxed);
        }
        
        .product-specs {
            background: var(--color-gray-50);
            border: 1px solid var(--color-gray-200);
            border-radius: 6px;
            padding: var(--space-4);
            margin: 0 0 var(--space-6) 0;
        }
        
        .product-specs h3 {
            margin: 0 0 var(--space-3) 0;
            color: var(--color-gray-900);
        }
        
        .product-specs ul {
            margin: 0;
            padding-left: var(--space-5);
        }
        
        .product-specs li {
            margin-bottom: var(--space-1);
            color: var(--color-gray-700);
        }
        
        .test-info {
            background: var(--color-info);
            color: white;
            padding: var(--space-4);
            border-radius: 6px;
            margin-bottom: var(--space-6);
        }
        
        .test-info h2 {
            margin: 0 0 var(--space-2) 0;
            font-size: var(--font-size-lg);
        }
        
        .test-info p {
            margin: 0;
            font-size: var(--font-size-sm);
        }
        
        @media (max-width: 768px) {
            .product-page {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }
            
            .product-image {
                height: 250px;
            }
        }
    </style>
    
    <!-- Calculator CSS -->
    <link rel="stylesheet" href="assets/css/components/material-calculator.css">
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h2>🧪 Teste de Integração - Calculadora em Página de Produto</h2>
            <p>Esta página simula como a calculadora apareceria integrada em uma página de produto WooCommerce.</p>
        </div>
        
        <div class="product-page">
            <div class="product-image">
                🎨 Imagem do Produto
            </div>
            
            <div class="product-info">
                <h1>Tinta Acrílica Premium Branca</h1>
                <div class="product-price">R$ 89,90</div>
                
                <div class="product-description">
                    Tinta acrílica de alta qualidade para paredes internas e externas. 
                    Excelente cobertura e durabilidade. Ideal para ambientes residenciais e comerciais.
                </div>
                
                <div class="product-specs">
                    <h3>Especificações Técnicas</h3>
                    <ul>
                        <li><strong>Rendimento:</strong> 12 m² por litro</li>
                        <li><strong>Volume:</strong> 3,6 litros</li>
                        <li><strong>Acabamento:</strong> Fosco</li>
                        <li><strong>Aplicação:</strong> Pincel, rolo ou spray</li>
                        <li><strong>Secagem:</strong> 4 horas (toque)</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Product Calculator Section (simulating WooCommerce integration) -->
        <div class="product-calculator-section">
            <h3>Calculadora de Quantidade</h3>
            <p>Calcule a quantidade necessária para seu projeto:</p>
            
            <div class="material-calculator single-product" id="materialCalculator">
                <div class="calculator-header">
                    <h3>Calculadora para Tinta Acrílica Premium Branca</h3>
                    <p>Calcule a quantidade necessária para seu projeto</p>
                </div>
                
                <div class="calculator-form">
                    <div class="form-group">
                        <label for="productType">Tipo de Material:</label>
                        <select id="productType" class="form-control">
                            <option value="">Selecione o material</option>
                            <option value="paint" selected>Tinta</option>
                            <option value="cement">Cimento</option>
                            <option value="flooring">Piso</option>
                            <option value="tiles">Azulejo</option>
                            <option value="mortar">Argamassa</option>
                        </select>
                    </div>

                    <div class="calculation-method">
                        <div class="method-tabs">
                            <button type="button" class="tab-button active" data-method="single">Área Única</button>
                            <button type="button" class="tab-button" data-method="multiple">Múltiplas Áreas</button>
                        </div>

                        <div class="method-content" id="singleAreaMethod">
                            <div class="form-group">
                                <label for="singleArea">Área Total (m²):</label>
                                <input type="number" id="singleArea" class="form-control" min="0" step="0.01" placeholder="Ex: 25.5">
                            </div>
                        </div>

                        <div class="method-content hidden" id="multipleAreasMethod">
                            <div class="areas-container" id="areasContainer">
                                <div class="area-input">
                                    <input type="text" placeholder="Nome do ambiente" class="area-name">
                                    <input type="number" placeholder="Área (m²)" class="area-value" min="0" step="0.01">
                                    <button type="button" class="remove-area">×</button>
                                </div>
                            </div>
                            <button type="button" id="addArea" class="btn-secondary">+ Adicionar Ambiente</button>
                        </div>
                    </div>

                    <div class="advanced-options">
                        <button type="button" class="toggle-advanced">Opções Avançadas</button>
                        <div class="advanced-content hidden">
                            <div class="form-group">
                                <label for="customCoverage">Rendimento Personalizado:</label>
                                <input type="number" id="customCoverage" class="form-control" min="0" step="0.1" placeholder="m² por unidade" value="12">
                            </div>
                            <div class="form-group">
                                <label for="customSafetyMargin">Margem de Segurança (%):</label>
                                <input type="number" id="customSafetyMargin" class="form-control" min="0" max="50" step="1" placeholder="10">
                            </div>
                        </div>
                    </div>

                    <button type="button" id="calculateButton" class="btn-primary">Calcular Quantidade</button>
                </div>

                <div class="calculator-results hidden" id="calculatorResults">
                    <div class="results-header">
                        <h4>Resultado do Cálculo</h4>
                    </div>
                    <div class="results-content" id="resultsContent">
                        <!-- Results will be populated here -->
                    </div>
                    <div class="results-actions">
                        <button type="button" id="addToCartButton" class="btn-primary">Adicionar ao Carrinho</button>
                        <button type="button" id="newCalculationButton" class="btn-secondary">Nova Calculação</button>
                    </div>
                </div>

                <div class="calculator-errors hidden" id="calculatorErrors">
                    <div class="error-content" id="errorContent">
                        <!-- Errors will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Safety Margin Info -->
        <div class="safety-margin-info">
            <h4>💡 Dicas Importantes</h4>
            <ul>
                <li>Nossa calculadora já inclui uma margem de segurança recomendada</li>
                <li>Para superfícies irregulares, considere aumentar a margem em 5-10%</li>
                <li>Sempre consulte as instruções do fabricante para rendimento específico</li>
                <li>Em caso de dúvidas, nossos especialistas estão disponíveis para ajudar</li>
            </ul>
        </div>
        
        <div style="margin-top: var(--space-8); padding: var(--space-4); background: var(--color-gray-100); border-radius: 6px;">
            <h3>Como Testar:</h3>
            <ol>
                <li>O tipo "Tinta" já está pré-selecionado</li>
                <li>O rendimento está configurado para 12 m²/litro (valor real do produto)</li>
                <li>Teste com uma área de 50m² - deve resultar em ~5 litros recomendados</li>
                <li>Teste múltiplas áreas: Sala (20m²) + Quarto (15m²) = 35m² total</li>
                <li>Clique em "Adicionar ao Carrinho" para ver a funcionalidade de fallback</li>
            </ol>
        </div>
    </div>
    
    <!-- Calculator JavaScript -->
    <script src="assets/js/material-calculator.js"></script>
    
    <script>
        // Initialize calculator when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for MaterialCalculator to be available
            setTimeout(function() {
                if (window.materialCalculator) {
                    window.materialCalculator.attachEventListeners();
                }
            }, 100);
        });
    </script>
</body>
</html>
{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Upgrade to Product Collection": ["Atualizar para coleção de produtos"], "Upgrade all Products (Beta) blocks on this page to <strongText /> for more features!": ["Atualize todos os blocos de Produtos (Beta) nesta página para <strongText /> para obter mais recursos!"], "Product Collection": ["Coleção de produtos"], "Pick some products": ["Escolha alguns produtos"], "Manage attributes": ["Gerenciar atributos"], "Related Products Controls": ["Controles de produtos relacionados"], "Display related products.": ["Exposição produtos relacionados."], "Contains the block elements used to render a product, like its name, featured image, rating, and more.": ["Contém os elementos de bloco usados ​​para renderizar um produto, como nome, imagem em destaque, classificação e muito mais."], "Product template": ["Modelo de produto"], "Products (Beta)": ["<PERSON><PERSON><PERSON> (Beta)"], "A block that displays a selection of products in your store.": ["Um bloco que exibe uma seleção de produtos em sua loja."], "Arrange products by popular pre-sets.": ["Organize os produtos por predefinições populares."], "Inherit query from template": ["Herdar consulta do modelo"], "Popular Filters": ["Filtros Populares"], "Sorted by title": ["Ordenado por título"], "Newest": ["O mais novo"], "Best Selling": ["<PERSON><PERSON> vendido"], "Top Rated": ["<PERSON><PERSON> votado"], "Choose among these pre-sets": ["Escolha entre estas predefinições"], "Toggle to use the global query context that is set with the current template, such as variations of the product catalog or search. Disable to customize the filtering independently.": ["Alterne para usar o contexto de consulta global definido com o modelo atual, como variações do catálogo de produtos ou pesquisa. Desative para personalizar a filtragem de forma independente."], "Show only products on sale": ["Mostrar somente produtos em promoção"], "Sale status": ["Status da promoção"], "An error has prevented the block from being updated.": ["Um erro impediu a atualização do bloco."], "%d term": ["%d termo", "%d termos"], "%1$s, has %2$d term": ["%1$s, possui %2$d termo", "%1$s, possui %2$d termos"], "%1$s, has %2$d product": ["%1$s, possui %2$d produto", "%1$s, possui %2$d produtos"], "Loading…": ["Carregando..."], "Advanced Filters": ["Filtros a<PERSON>"], "Display a short description about a product.": ["Exiba uma breve descrição sobre um produto."], "Product Summary": ["Sumário de produtos"], "The following error was returned": ["O seguinte erro foi retornado"], "The following error was returned from the API": ["O seguinte erro foi retornado da API"], "Search for items": ["Procurar por itens"], "%d item selected": ["%d item selecionado", "%d itens selecionados"], "Clear all selected items": ["Remover todos os itens selecionados"], "Clear all": ["Remover todos"], "No results for %s": ["Nenhum resultado para %s"], "No items found.": ["Nenhum item encontrado."], "Remove %s": ["Remover %s"], "Search results updated.": ["Resultados de pesquisa atualizados."], "Hand-picked Products": ["Produtos escolhidos a dedo"], "All selected attributes": ["Todos os atributos selecionados"], "%d attribute selected": ["%d atributo selecionado", "%d atributos selecionados"], "Search for product attributes": ["Pesquisar por atributos de produto"], "Your store doesn't have any product attributes.": ["Sua loja não tem atributos de produto."], "Product attribute search results updated.": ["Resultados atualizados para pesquisa de atributos de produto."], "Any selected attributes": ["Quaisquer atributos selecionados"], "Clear all product attributes": ["Limpar todos os atributos do produto"], "Pick at least two attributes to use this setting.": ["Escolha pelo menos dois atributos para usar esta configuração."], "Display products matching": ["<PERSON><PERSON>r produtos correspondentes"], "Product Attributes": ["Atributos do produto"], "Related products": ["Produtos relacionados"], "%d product": ["%d produto", "%d produtos"], "Stock status": ["Status do estoque"], "Attributes": ["Atributos"]}}, "comment": {"reference": "assets/client/blocks/product-query.js"}}
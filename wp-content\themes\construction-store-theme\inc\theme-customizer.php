<?php
/**
 * Theme Customizer settings for Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add customizer settings
 */
function construction_store_customize_register($wp_customize) {
    
    // Add Construction Store Panel
    $wp_customize->add_panel('construction_store_panel', array(
        'title' => __('Configurações da Loja', 'construction-store-theme'),
        'description' => __('Personalize sua loja de materiais de construção', 'construction-store-theme'),
        'priority' => 30,
    ));
    
    // Header Section
    $wp_customize->add_section('construction_store_header', array(
        'title' => __('Cabeçalho', 'construction-store-theme'),
        'panel' => 'construction_store_panel',
        'priority' => 10,
    ));
    
    // Contact Information
    $wp_customize->add_setting('construction_store_phone', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('construction_store_phone', array(
        'label' => __('Telefone', 'construction-store-theme'),
        'section' => 'construction_store_header',
        'type' => 'text',
        'description' => __('Telefone exibido no cabeçalho', 'construction-store-theme'),
    ));
    
    $wp_customize->add_setting('construction_store_email', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_email',
    ));
    
    $wp_customize->add_control('construction_store_email', array(
        'label' => __('E-mail', 'construction-store-theme'),
        'section' => 'construction_store_header',
        'type' => 'email',
        'description' => __('E-mail exibido no cabeçalho', 'construction-store-theme'),
    ));
    
    // Homepage Section
    $wp_customize->add_section('construction_store_homepage', array(
        'title' => __('Página Inicial', 'construction-store-theme'),
        'panel' => 'construction_store_panel',
        'priority' => 20,
    ));
    
    // Hero Banner Settings
    $wp_customize->add_setting('construction_store_hero_title', array(
        'default' => __('Materiais de Construção de Qualidade', 'construction-store-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('construction_store_hero_title', array(
        'label' => __('Título do Banner Principal', 'construction-store-theme'),
        'section' => 'construction_store_homepage',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('construction_store_hero_subtitle', array(
        'default' => __('Encontre tudo o que precisa para sua obra', 'construction-store-theme'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('construction_store_hero_subtitle', array(
        'label' => __('Subtítulo do Banner Principal', 'construction-store-theme'),
        'section' => 'construction_store_homepage',
        'type' => 'textarea',
    ));
    
    $wp_customize->add_setting('construction_store_hero_image', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'construction_store_hero_image', array(
        'label' => __('Imagem do Banner Principal', 'construction-store-theme'),
        'section' => 'construction_store_homepage',
        'description' => __('Recomendado: 1920x600 pixels', 'construction-store-theme'),
    )));
    
    $wp_customize->add_setting('construction_store_hero_button_text', array(
        'default' => __('Ver Produtos', 'construction-store-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('construction_store_hero_button_text', array(
        'label' => __('Texto do Botão do Banner', 'construction-store-theme'),
        'section' => 'construction_store_homepage',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('construction_store_hero_button_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('construction_store_hero_button_url', array(
        'label' => __('URL do Botão do Banner', 'construction-store-theme'),
        'section' => 'construction_store_homepage',
        'type' => 'url',
    ));
    
    // Featured Categories
    $wp_customize->add_setting('construction_store_featured_categories_title', array(
        'default' => __('Principais Categorias', 'construction-store-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('construction_store_featured_categories_title', array(
        'label' => __('Título das Categorias em Destaque', 'construction-store-theme'),
        'section' => 'construction_store_homepage',
        'type' => 'text',
    ));
    
    // Promotional Section
    $wp_customize->add_section('construction_store_promotions', array(
        'title' => __('Promoções', 'construction-store-theme'),
        'panel' => 'construction_store_panel',
        'priority' => 30,
    ));
    
    $wp_customize->add_setting('construction_store_promo_banner_enable', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('construction_store_promo_banner_enable', array(
        'label' => __('Exibir Banner Promocional', 'construction-store-theme'),
        'section' => 'construction_store_promotions',
        'type' => 'checkbox',
    ));
    
    $wp_customize->add_setting('construction_store_promo_text', array(
        'default' => __('Oferta Especial: 20% de desconto em ferramentas!', 'construction-store-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('construction_store_promo_text', array(
        'label' => __('Texto Promocional', 'construction-store-theme'),
        'section' => 'construction_store_promotions',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('construction_store_promo_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('construction_store_promo_url', array(
        'label' => __('URL da Promoção', 'construction-store-theme'),
        'section' => 'construction_store_promotions',
        'type' => 'url',
    ));
    
    // Footer Section
    $wp_customize->add_section('construction_store_footer', array(
        'title' => __('Rodapé', 'construction-store-theme'),
        'panel' => 'construction_store_panel',
        'priority' => 40,
    ));
    
    $wp_customize->add_setting('construction_store_footer_text', array(
        'default' => sprintf(__('© %s Loja de Materiais de Construção. Todos os direitos reservados.', 'construction-store-theme'), date('Y')),
        'sanitize_callback' => 'wp_kses_post',
    ));
    
    $wp_customize->add_control('construction_store_footer_text', array(
        'label' => __('Texto do Rodapé', 'construction-store-theme'),
        'section' => 'construction_store_footer',
        'type' => 'textarea',
    ));
    
    // Social Media
    $social_networks = array(
        'facebook' => 'Facebook',
        'instagram' => 'Instagram',
        'whatsapp' => 'WhatsApp',
        'youtube' => 'YouTube',
        'linkedin' => 'LinkedIn'
    );
    
    foreach ($social_networks as $network => $label) {
        $wp_customize->add_setting("construction_store_social_{$network}", array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));
        
        $wp_customize->add_control("construction_store_social_{$network}", array(
            'label' => sprintf(__('URL do %s', 'construction-store-theme'), $label),
            'section' => 'construction_store_footer',
            'type' => 'url',
        ));
    }
    
    // Store Information Section
    $wp_customize->add_section('construction_store_info', array(
        'title' => __('Informações da Loja', 'construction-store-theme'),
        'panel' => 'construction_store_panel',
        'priority' => 50,
    ));
    
    $wp_customize->add_setting('construction_store_address', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('construction_store_address', array(
        'label' => __('Endereço da Loja', 'construction-store-theme'),
        'section' => 'construction_store_info',
        'type' => 'textarea',
    ));
    
    $wp_customize->add_setting('construction_store_hours', array(
        'default' => __('Segunda a Sexta: 8h às 18h\nSábado: 8h às 12h', 'construction-store-theme'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('construction_store_hours', array(
        'label' => __('Horário de Funcionamento', 'construction-store-theme'),
        'section' => 'construction_store_info',
        'type' => 'textarea',
    ));
    
    $wp_customize->add_setting('construction_store_cnpj', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('construction_store_cnpj', array(
        'label' => __('CNPJ', 'construction-store-theme'),
        'section' => 'construction_store_info',
        'type' => 'text',
    ));
}
add_action('customize_register', 'construction_store_customize_register');

/**
 * Helper function to get customizer option
 */
function construction_store_get_option($option_name, $default = '') {
    return get_theme_mod($option_name, $default);
}
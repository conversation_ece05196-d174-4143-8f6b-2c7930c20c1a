/**
 * Product Grid Component Styles
 * Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

/* Product Grid Layout */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 20px;
}

/* Product Card */
.product-item {
    position: relative;
    background: var(--wp--preset--color--white);
    border: 1px solid var(--wp--preset--color--gray-200);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--wp--preset--color--primary);
}

/* Product Image Container */
.product-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
    background-color: var(--wp--preset--color--gray-50);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-item:hover .product-image img {
    transform: scale(1.05);
}

/* Product Badges */
.product-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
    line-height: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.badge-sale {
    background-color: #DC3545;
    color: white;
}

.badge-new {
    background-color: #28A745;
    color: white;
}

.badge-out-of-stock {
    background-color: #6C757D;
    color: white;
}

.badge-featured {
    background-color: #FF6B35;
    color: white;
}

.badge-promotion {
    background-color: #FFC107;
    color: #212529;
}

/* Product Info Section */
.product-info {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: calc(100% - 280px); /* Adjust based on image height */
}

/* Product Title */
.product-title {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 8px 0;
    color: var(--wp--preset--color--gray-900);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
}

.product-title a:hover {
    color: var(--wp--preset--color--primary);
}

/* Product Rating */
.product-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;
}

.star-rating {
    display: flex;
    gap: 1px;
}

.star-rating .star {
    color: #FFC107;
    font-size: 0.875rem;
}

.rating-count {
    font-size: 0.75rem;
    color: var(--wp--preset--color--gray-600);
    margin-left: 4px;
}

/* Technical Specifications Preview */
.product-specs-preview {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 8px 0;
    padding: 8px 0;
    border-top: 1px solid var(--wp--preset--color--gray-200);
    border-bottom: 1px solid var(--wp--preset--color--gray-200);
}

.spec-item {
    font-size: 0.8125rem;
    color: var(--wp--preset--color--gray-600);
    line-height: 1.3;
    display: flex;
    align-items: center;
    gap: 6px;
}

.spec-item:before {
    content: "•";
    color: var(--wp--preset--color--primary);
    font-weight: bold;
}

.spec-item.dimensions:before {
    content: "📏";
}

.spec-item.material:before {
    content: "🔧";
}

.spec-item.weight:before {
    content: "⚖️";
}

.spec-item.application:before {
    content: "🏗️";
}

/* Product Price */
.product-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
}

.price-current {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--wp--preset--color--primary);
}

.price-original {
    font-size: 0.875rem;
    color: var(--wp--preset--color--gray-500);
    text-decoration: line-through;
}

.price-installments {
    font-size: 0.75rem;
    color: var(--wp--preset--color--gray-600);
    margin-top: 2px;
}

/* Stock Status */
.stock-status {
    font-size: 0.8125rem;
    font-weight: 500;
    margin-bottom: 8px;
}

.stock-status.in-stock {
    color: var(--wp--preset--color--success);
}

.stock-status.out-of-stock {
    color: var(--wp--preset--color--danger);
}

.stock-status.low-stock {
    color: var(--wp--preset--color--warning);
}

/* Add to Cart Button */
.add-to-cart-button {
    margin-top: auto;
}

.add-to-cart-button .wp-block-button__link {
    width: 100%;
    text-align: center;
    background-color: var(--wp--preset--color--primary);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.add-to-cart-button .wp-block-button__link:hover {
    background-color: var(--wp--preset--color--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.add-to-cart-button .wp-block-button__link:active {
    transform: translateY(0);
}

/* Quick View Button */
.quick-view-button {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--wp--preset--color--gray-300);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
    transform: translateY(-10px);
}

.product-item:hover .quick-view-button {
    opacity: 1;
    transform: translateY(0);
}

.quick-view-button:hover {
    background: white;
    border-color: var(--wp--preset--color--primary);
    color: var(--wp--preset--color--primary);
}

/* List View Layout */
.products-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.products-grid.list-view .product-item {
    display: flex;
    flex-direction: row;
    align-items: stretch;
}

.products-grid.list-view .product-image {
    width: 200px;
    flex-shrink: 0;
    aspect-ratio: 1;
}

.products-grid.list-view .product-info {
    flex: 1;
    padding: 20px;
    height: auto;
}

.products-grid.list-view .product-specs-preview {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
    border: none;
    padding: 0;
    margin: 12px 0;
}

.products-grid.list-view .add-to-cart-button {
    margin-top: 16px;
    align-self: flex-start;
}

.products-grid.list-view .add-to-cart-button .wp-block-button__link {
    width: auto;
    padding: 10px 20px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 16px;
    }
    
    .product-info {
        padding: 12px;
        gap: 6px;
    }
    
    .product-title {
        font-size: 0.9375rem;
    }
    
    .spec-item {
        font-size: 0.75rem;
    }
    
    .price-current {
        font-size: 1rem;
    }
    
    .add-to-cart-button .wp-block-button__link {
        padding: 10px 12px;
        font-size: 0.8125rem;
    }
    
    /* List view adjustments for mobile */
    .products-grid.list-view .product-item {
        flex-direction: column;
    }
    
    .products-grid.list-view .product-image {
        width: 100%;
        aspect-ratio: 16/9;
    }
    
    .products-grid.list-view .product-specs-preview {
        flex-direction: column;
        gap: 4px;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 12px;
    }
    
    .product-info {
        padding: 10px;
    }
    
    .product-title {
        font-size: 0.875rem;
        -webkit-line-clamp: 3;
    }
    
    .badge {
        padding: 3px 6px;
        font-size: 0.6875rem;
    }
    
    .spec-item {
        font-size: 0.6875rem;
    }
    
    .price-current {
        font-size: 0.9375rem;
    }
}

/* Loading States */
.product-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.product-item.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--wp--preset--color--gray-300);
    border-top-color: var(--wp--preset--color--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* No Results State */
.no-products-found {
    text-align: center;
    padding: 60px 20px;
    color: var(--wp--preset--color--gray-600);
}

.no-products-found h3 {
    font-size: 1.5rem;
    margin-bottom: 16px;
    color: var(--wp--preset--color--gray-800);
}

.no-products-found p {
    font-size: 1rem;
    margin-bottom: 24px;
    line-height: 1.6;
}

.clear-filters-suggestion .wp-block-button__link {
    background-color: var(--wp--preset--color--secondary);
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.clear-filters-suggestion .wp-block-button__link:hover {
    background-color: var(--wp--preset--color--secondary-light);
}
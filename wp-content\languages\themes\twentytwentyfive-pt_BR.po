# Translation of Themes - Twenty Twenty-Five in Portuguese (Brazil)
# This file is distributed under the same license as the Themes - Twenty Twenty-Five package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-01-29 01:27:12+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Themes - Twenty Twenty-Five\n"

#. Theme Name of the theme
#: style.css patterns/footer-columns.php:66 patterns/footer-newsletter.php:42
#: patterns/footer.php:75 patterns/page-portfolio-home.php:226
#, gp-priority: high
msgid "Twenty Twenty-Five"
msgstr "Twenty Twenty-Five"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Five emphasizes simplicity and adaptability. It offers flexible design options, supported by a variety of patterns for different page types, such as services and landing pages, making it ideal for building personal blogs, professional portfolios, online magazines, or business websites. Its templates cater to various blog styles, from text-focused to image-heavy layouts. Additionally, it supports international typography and diverse color palettes, ensuring accessibility and customization for users worldwide."
msgstr "O Twenty Twenty-Five enfatiza simplicidade e adaptabilidade. Oferece opções de design flexíveis, apoiadas por uma variedade de padrões para diferentes tipos de página, como serviços e páginas de entrada, tornando-o ideal para a criação de blogs pessoais, portfólios profissionais, revistas on-line ou sites de negócios. Os seus modelos satisfazem vários estilos de blog, desde leiautes focados em textos até aqueles largamente baseados em imagens. Além disso, oferece suporte a tipografia internacional e a diversas palhetas de cores, garantindo acessibilidade e personalização para usuários de todo o mundo."

#: patterns/text-faqs.php:35 patterns/text-faqs.php:51
#: patterns/text-faqs.php:71 patterns/text-faqs.php:87
msgctxt "Answer in the FAQs pattern."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist."
msgstr "Esta primorosa compilação reúne uma diversa variedade de fotografias que capturam a essência de diferentes épocas e culturas, refletindo estilo e perspectivas únicas de cada artista."

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Rodapé"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Cabeçalho"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Extra grande"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Large"
msgstr "Grande"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Média"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Contraste"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Base"
msgstr "Base"

#: functions.php:106
msgid "A collection of full page layouts."
msgstr "Uma coleção de modelos de página completa."

#: functions.php:76
msgid "Checkmark"
msgstr "Marca de seleção"

#: patterns/footer-columns.php:37 patterns/footer.php:47
msgid "About"
msgstr "Sobre"

#: patterns/contact-info-locations.php:34 patterns/footer-social.php:21
msgid "Facebook"
msgstr "Facebook"

#: patterns/contact-info-locations.php:33 patterns/footer-social.php:22
#: patterns/media-instagram-grid.php:24 patterns/page-cv-bio.php:47
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:36
#: patterns/page-link-in-bio-with-tight-margins.php:48
msgid "Instagram"
msgstr "Instagram"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Barra lateral"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Pesquisa"

#: patterns/post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Navegação de posts"

#: patterns/comments.php:18
msgid "Comments"
msgstr "Comentários"

#: patterns/comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Comentários"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Página sem título"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Barra lateral"

#: patterns/template-query-loop.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Lista de posts, 1 coluna"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Extra extra grande"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Small"
msgstr "Pequeno"

#: patterns/page-portfolio-home.php:229
msgctxt "Phone number."
msgid "****** 349 1806"
msgstr "+55 (91) 2033-9473"

#: patterns/pricing-2-col.php:38 patterns/pricing-3-col.php:49
msgid "0€"
msgstr "R$ 0"

#: patterns/contact-info-locations.php:54
#: patterns/contact-info-locations.php:66
#: patterns/contact-info-locations.php:78
#: patterns/contact-info-locations.php:90
msgid "123 Example St. Manhattan, NY 10300 United States"
msgstr "Rua das Flores, 1313 - 02345-020 São Paulo, SP - Brasil"

#: patterns/testimonials-2-col.php
msgctxt "Pattern title"
msgid "2 columns with avatar"
msgstr "2 colunas com avatar"

#: patterns/pricing-3-col.php:85
msgid "20€"
msgstr "R$ 20"

#: patterns/pricing-2-col.php:82
msgid "20€/month"
msgstr "R$ 20 / mês"

#: patterns/testimonials-6-col.php
msgctxt "Pattern title"
msgid "3 column layout with 6 testimonials"
msgstr "Leiaute de 3 colunas com 6 depoimentos"

#: patterns/cta-grid-products-link.php:59
msgid "30€"
msgstr "R$ 30"

#: patterns/pricing-3-col.php:125
msgid "40€"
msgstr "R$ 40"

#: patterns/event-schedule.php:60 patterns/event-schedule.php:92
#: patterns/event-schedule.php:145 patterns/event-schedule.php:177
msgctxt "Example event time in pattern."
msgid "9 AM — 11 AM"
msgstr "9:00 - 11:00"

#: patterns/media-instagram-grid.php:28
msgctxt "Example username for social media account."
msgid "@example"
msgstr "@exemplo"

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern description"
msgid "A banner with a short paragraph, and two images displayed in a grid layout."
msgstr "Um banner com um parágrafo curto e duas imagens apresentadas num leiaute em grade."

#: patterns/page-business-home.php
msgctxt "Pattern description"
msgid "A business homepage pattern."
msgstr "Um padrão de página inicial para negócios."

#: patterns/cta-grid-products-link.php
msgctxt "Pattern description"
msgid "A call to action featuring product images."
msgstr "Uma chamada para ação com imagens de produtos."

#: patterns/cta-book-links.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in different websites."
msgstr "Uma seção de chamada para ação com ligações para obter o livro em diferentes sites."

#: patterns/cta-book-locations.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in the most popular locations."
msgstr "Uma seção de chamada para ação com ligações para obter o livro nos locais mais populares."

#: functions.php:114
msgid "A collection of post format patterns."
msgstr "Uma coleção de padrões de formatos de posts."

#: patterns/hero-book.php:42
msgctxt "Content of the hero section."
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "Uma fina coleção de instantâneos com fotografias de Louis Fleckenstein, Paul Strand e Asahachi Kōno."

#: patterns/hero-overlapped-book-cover-with-links.php:34
msgctxt "Hero - Overlapped book cover pattern subline text"
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "Uma fina coleção de instantâneos com fotografias de Louis Fleckenstein, Paul Strand e Asahachi Kōno."

#: patterns/banner-poster.php:59
msgid "#stories"
msgstr "#historias"

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-with-description-and-images-grid.php:31
#: patterns/overlapped-images.php:47
msgid "%s is a flower delivery and subscription business. Based in the EU, our mission is not only to deliver stunning flower arrangements across but also foster knowledge and enthusiasm on the beautiful gift of nature: flowers."
msgstr "%s é uma empresa de assinatura de entrega de flores. Com sede no Brasil, a nossa missão é, não só entregar deslumbrantes arranjos de flores, mas também fomentar o conhecimento e entusiasmo pelo belo presente da natureza: as flores."

#: patterns/page-coming-soon.php
msgctxt "Pattern description"
msgid "A full-width cover banner that can be applied to a page or it can work as a single landing page."
msgstr "Um banner de largura completa que pode ser aplicado pode ser uma seção de uma página ou pode funcionar como uma página de destino completa."

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern description"
msgid "A full-width cover section with a large background image and an oversized heading."
msgstr "Uma seção de largura completa com uma grande imagem de fundo e um título superdimensionado."

#: patterns/grid-with-categories.php
msgctxt "Pattern description"
msgid "A grid section with different categories."
msgstr "Um seção de grade com diferentes categorias."

#: patterns/text-faqs.php
msgctxt "Pattern description"
msgid "A FAQs section with a FAQ heading and list of questions and answers."
msgstr "Uma seção de perguntas frequentes com um título e uma lista de perguntas e respostas."

#: patterns/testimonials-large.php:24
msgctxt "Testimonial heading."
msgid "What people are saying"
msgstr "O que as pessoas estão falando"

#: patterns/text-faqs.php:67
msgctxt "Question in the FAQs pattern."
msgid "When will The Stories Book be released?"
msgstr "Quando o Livro de Histórias será lançado?"

#: patterns/page-cv-bio.php:43 patterns/page-link-in-bio-wide-margins.php:24
#: patterns/services-team-photos.php:32
msgid "Woman on beach, splashing water."
msgstr "Mulher na praia, jogando água."

#: patterns/template-query-loop-news-blog.php:30
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "Written by"
msgstr "Escrito por"

#: patterns/hidden-written-by.php
msgctxt "Pattern title"
msgid "Written by"
msgstr "Escrito por"

#: patterns/hidden-written-by.php:16
msgid "Written by "
msgstr "Escrito por "

#: patterns/contact-info-locations.php:32 patterns/footer-social.php:23
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:40
#: patterns/page-link-in-bio-with-tight-margins.php:52
msgctxt "Refers to the social media platform formerly known as Twitter."
msgid "X"
msgstr "X"

#: theme.json
msgctxt "Space size name"
msgid "XX-Large"
msgstr "XX-Grande"

#: theme.json
msgctxt "Space size name"
msgid "X-Small"
msgstr "X-Pequeno"

#: theme.json
msgctxt "Space size name"
msgid "X-Large"
msgstr "X-Grande"

#: patterns/hero-podcast.php:49
msgctxt "Button text"
msgid "YouTube"
msgstr "YouTube"

#: styles/04-afternoon.json styles/06-morning.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-5.json
msgctxt "Font family name"
msgid "Ysabeau Office"
msgstr "Ysabeau Office"

#: patterns/cta-book-locations.php:131
msgid "United Kingdom"
msgstr "Reino Unido"

#: patterns/cta-book-locations.php:119
msgid "United States"
msgstr "Estados Unidos"

#: patterns/cta-events-list.php:19
msgid "Upcoming events"
msgstr "Próximos eventos"

#: patterns/event-schedule.php:78 patterns/media-instagram-grid.php:44
msgid "View of the deep ocean."
msgstr "Vista do fundo do oceano."

#: patterns/services-subscriber-only-section.php:55
msgid "View plans"
msgstr "Ver planos"

#: patterns/contact-location-and-link.php:22
msgid "Visit us at 123 Example St. Manhattan, NY 10300, United States"
msgstr "Visite-nos na Rua das Flores, 1313 - Centro - Santa Cruz, RS - Brasil"

#: styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Vollkorn"
msgstr "Vollkorn"

#: styles/typography/typography-preset-2.json
msgctxt "Style variation name"
msgid "Vollkorn & Fira Code"
msgstr "Vollkorn e Fira Code"

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-intro.php:21
msgctxt "Pattern placeholder text."
msgid "We're %s, our mission is to deliver exquisite flower arrangements that not only adorn living spaces but also inspire a deeper appreciation for natural beauty."
msgstr "Somos %s, a nossa missão é entregar arranjos de flores requintados que não só adornam espaços de convivência, mas também inspiram uma apreciação mais profunda pela beleza natural."

#: patterns/cta-heading-search.php:18
msgid "What are you looking for?"
msgstr "O que você está procurando?"

#: patterns/text-faqs.php:31
msgctxt "Question in the FAQs pattern."
msgid "What is The Stories Book about?"
msgstr "De que se trata o Livro de Histórias?"

#: patterns/testimonials-6-col.php:18
msgctxt "Testimonial section heading."
msgid "What people are saying"
msgstr "O que as pessoas estão falando"

#: patterns/event-3-col.php:24 patterns/event-schedule.php:23
msgid "These are some of the upcoming events."
msgstr "Estes são alguns eventos próximos."

#: patterns/event-schedule.php:89
msgid "Things you didn’t know about the deep ocean"
msgstr "Coisas que você não sabia sobre o oceano profundo."

#: patterns/pricing-2-col.php:56 patterns/pricing-2-col.php:100
#: patterns/services-subscriber-only-section.php:39
msgid "An elegant addition of home decor collection."
msgstr "Uma adição elegante à coleção de decoração para casa."

#: patterns/event-schedule.php:174
msgid "An introduction to African dialects"
msgstr "Uma introdução aos dialetos africanos"

#: patterns/event-schedule.php:142
msgid "Ancient buildings and symbols"
msgstr "Edifícios e símbolos antigos"

#: styles/blocks/03-annotation.json
msgctxt "Style variation name"
msgid "Annotation"
msgstr "Anotação"

#: patterns/grid-with-categories.php:36
msgid "Anthuriums"
msgstr "Antúrios"

#: patterns/cta-book-links.php:35
#: patterns/hero-overlapped-book-cover-with-links.php:62
msgctxt "Example brand name."
msgid "Apple Books"
msgstr "Apple Books"

#: patterns/hero-podcast.php:53
msgctxt "Button text"
msgid "Apple Podcasts"
msgstr "Apple Podcasts"

#: patterns/template-home-posts-grid-news-blog.php:114
msgid "Architecture"
msgstr "Arquitetura"

#: patterns/text-faqs.php:83
msgctxt "Question in the FAQs pattern."
msgid "Are signed copies available?"
msgstr "Estão disponíveis exemplares autografados?"

#: patterns/cta-events-list.php:37
msgid "Atlanta, GA, USA"
msgstr "Porto Alegre, RS - Brasil"

#: patterns/cta-book-links.php:27
#: patterns/hero-overlapped-book-cover-with-links.php:77
msgctxt "Example brand name."
msgid "Audible"
msgstr "Audible"

#: patterns/format-audio.php
msgctxt "Pattern title"
msgid "Audio format"
msgstr "Formato de áudio"

#: patterns/banner-poster.php:39
msgctxt "Example event date in pattern."
msgid "Aug 08—10 2025"
msgstr "08–10 de agosto de 2025"

#: patterns/event-schedule.php:46 patterns/media-instagram-grid.php:60
msgid "Birds on a lake."
msgstr "Pássaros em um lago."

#: patterns/cta-grid-products-link.php:26
#: patterns/cta-grid-products-link.php:126
msgid "Black and white flower"
msgstr "Flor em preto e branco"

#: patterns/page-link-in-bio-with-tight-margins.php:27
msgid "Black and white photo focusing on a woman and a child from afar."
msgstr "Fotografia em preto e branco que foca uma mulher e uma criança ao longe."

#: patterns/event-schedule.php:163
msgid "Black and white photo of an African woman."
msgstr "Fotografia em preto e branco de uma mulher africana."

#: patterns/banner-with-description-and-images-grid.php:48
#: patterns/overlapped-images.php:26
msgid "Black and white photography close up of a flower."
msgstr "Fotografia em preto e branco de uma flor em primeiro plano."

#: patterns/footer-columns.php:36 patterns/footer.php:45
#: patterns/hidden-blog-heading.php:15 patterns/template-home-text-blog.php:20
msgid "Blog"
msgstr "Blog"

#: patterns/hero-overlapped-book-cover-with-links.php:113
msgid "Book Image"
msgstr "Imagem de livro"

#: patterns/cta-book-locations.php:47 patterns/cta-book-locations.php:59
#: patterns/cta-book-locations.php:71 patterns/cta-book-locations.php:83
#: patterns/cta-book-locations.php:99 patterns/cta-book-locations.php:111
#: patterns/cta-book-locations.php:123 patterns/cta-book-locations.php:135
msgid "Book Store"
msgstr "Livraria"

#: patterns/cta-book-links.php:39
msgctxt "Example brand name."
msgid "Bookshop.org"
msgstr "Livraria.org"

#: patterns/cta-grid-products-link.php:114
msgid "Botany flowers"
msgstr "Flores botânicas"

#: patterns/cta-book-locations.php:55
msgid "Brazil"
msgstr "Brasil"

#: patterns/page-business-home.php
msgctxt "Pattern title"
msgid "Business homepage"
msgstr "Página inicial de empresa"

#: patterns/cta-events-list.php:51 patterns/cta-events-list.php:89
#: patterns/cta-events-list.php:120 patterns/cta-events-list.php:158
msgid "Buy Tickets"
msgstr "Comprar ingressos"

#: patterns/cta-book-links.php:17
msgid "Buy your copy of The Stories Book"
msgstr "Compre a sua cópia de O Livro de Histórias"

#: patterns/template-single-left-aligned-content.php:31
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "by"
msgstr "por"

#: patterns/cta-events-list.php:23
msgid "These are some of the upcoming events"
msgstr "Estes são alguns dos próximos eventos"

#: patterns/event-rsvp.php:73
msgid "This immersive event celebrates the universal human experience through the lenses of history and ancestry, featuring a diverse array of photographers whose works capture the essence of different cultures and historical moments."
msgstr "Este evento imersivo celebra a experiência humana universal através das lentes da história e da ancestralidade, apresentando uma diversa seleção de fotógrafos cujos trabalhos capturam a essência de diferentes culturas e momentos históricos."

#: patterns/banner-about-book.php:26
msgctxt "Pattern placeholder text."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist. Fleckenstein’s evocative imagery, Strand’s groundbreaking modernist approach, and Kōno’s meticulous documentation of Japanese life come together in a harmonious blend that celebrates the art of photography. Each image in “The Stories Book” is accompanied by insightful commentary, providing historical context and revealing the stories behind the photographs. This collection is not only a visual feast but also a tribute to the power of photography to preserve and narrate the multifaceted experiences of humanity."
msgstr "Esta primorosa compilação reúne uma diversa variedade de fotografias que capturam a essência de diferentes épocas e culturas, refletindo estilo e perspectivas únicas de cada artista. As imagens evocativas de Fleckenstein, a abordagem modernista inovadora de Strand e a meticulosa documentação da vida japonesa de Kōno se unem em uma seleção harmoniosa que celebra a arte da fotografia. Cada imagem em \"O Livro de Histórias\" é acompanhada de comentários informativos, que fornecem o contexto histórico e revelam as histórias por trás das fotografias. Esta coleção não é só uma celebração visual mas também um tributo à força da fotografia em preservar e narrar as multifacetadas experiências da humanidade."

#: patterns/cta-events-list.php:106 patterns/cta-events-list.php:144
msgid "Thornville, OH, USA"
msgstr "Belo Horizonte, MG - Brasil"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search input field placeholder text."
msgid "Type here..."
msgstr "Digite aqui..."

#: patterns/testimonials-2-col.php
msgctxt "Pattern description"
msgid "Two columns with testimonials and avatars."
msgstr "Duas colunas com depoimentos e avatares."

#: patterns/services-3-col.php
msgctxt "Pattern description"
msgid "Three columns with images and text to showcase services."
msgstr "Três colunas com imagens e texto para destacar os serviços."

#: patterns/contact-info-locations.php:35
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:44
#: patterns/page-link-in-bio-with-tight-margins.php:56
msgid "TikTok"
msgstr "TikTok"

#: theme.json
msgctxt "Space size name"
msgid "Tiny"
msgstr "Minúsculo"

#: patterns/grid-with-categories.php:22
msgid "Top Categories"
msgstr "Categorias principais"

#: styles/05-twilight.json styles/colors/05-twilight.json
msgctxt "Style variation name"
msgid "Twilight"
msgstr "Crepúsculo"

#: patterns/footer-columns.php:52 patterns/footer.php:61
msgid "Themes"
msgstr "Temas"

#: patterns/hero-book.php:38
msgctxt "Heading of the hero section."
msgid "The Stories Book"
msgstr "O Livro de Histórias"

#: patterns/hero-overlapped-book-cover-with-links.php:28
msgctxt "Hero - Overlapped book cover pattern headline text"
msgid "The Stories Book"
msgstr "O Livro de Histórias"

#: patterns/cta-book-locations.php:27
msgid "The Stories Book will be available from these international retailers."
msgstr "O Livro de Histórias estará disponível nestes distribuidores internacionais."

#: patterns/hero-podcast.php:32
msgid "The Stories Podcast"
msgstr "O Podcast de Histórias"

#: patterns/logos.php:17
msgid "The Stories Podcast is sponsored by"
msgstr "O Podcast de Histórias é patrocinado por"

#: patterns/hero-podcast.php:36
msgctxt "Podcast description"
msgid "Storytelling, expert analysis, and vivid descriptions. The Stories Podcast brings history to life, making it accessible and engaging for a global audience."
msgstr "Contação de histórias, análises de especialistas e descrições vívidas. O Podcast de Histórias dá vida à história, tornando-a acessível e envolvente a uma audiência global."

#: patterns/event-schedule.php:132 patterns/media-instagram-grid.php:52
msgid "The Acropolis of Athens."
msgstr "A Acrópole de Atenas."

#: patterns/contact-location-and-link.php:36
msgid "The business location"
msgstr "Localização da empresa"

#: patterns/template-home-with-sidebar-news-blog.php:42
msgid "The Latest"
msgstr "Os mais recentes"

#: patterns/hidden-404.php:36
msgctxt "404 error message"
msgid "The page you are looking for doesn't exist, or it has been moved. Please try searching using the form below."
msgstr "A página que você está procurando não existe ou foi movida. Tente pesquisar usando o formulário abaixo."

#: styles/sections/section-5.json
msgctxt "Style variation name"
msgid "Style 5"
msgstr "Estilo 5"

#: patterns/cta-newsletter.php:32 patterns/footer-newsletter.php:30
#: patterns/page-coming-soon.php:39
#: patterns/services-subscriber-only-section.php:51
msgid "Subscribe"
msgstr "Assinar"

#: patterns/hero-podcast.php:43
msgid "Subscribe on your favorite platform"
msgstr "Assine na sua plataforma favorita"

#: patterns/page-coming-soon.php:33
msgid "Subscribe to get notified when our website is ready."
msgstr "Assine para ser notificado quando nosso site estiver pronto."

#: patterns/services-subscriber-only-section.php:21
msgid "Subscribe to get unlimited access"
msgstr "Assine para obter acesso ilimitado"

#: styles/blocks/02-subtitle.json
msgctxt "Style variation name"
msgid "Subtitle"
msgstr "Subtítulo"

#: patterns/grid-with-categories.php:64
msgid "Sunflowers"
msgstr "Girassóis"

#: styles/07-sunrise.json styles/colors/07-sunrise.json
msgctxt "Style variation name"
msgid "Sunrise"
msgstr "Nascer do sol"

#: patterns/cta-book-locations.php:107
msgid "Switzerland"
msgstr "Suíça"

#: patterns/template-single-photo-blog.php:61
msgctxt "Prefix before one or more tags. The tags are displayed in a separate block on the next line."
msgid "Tagged:"
msgstr "Marcado como:"

#: patterns/cta-grid-products-link.php:76
msgid "Tailored to your needs"
msgstr "Feito sob medida para você"

#: patterns/cta-centered-heading.php:19 patterns/cta-events-list.php:33
#: patterns/cta-events-list.php:102 patterns/event-3-col.php:40
#: patterns/event-3-col.php:64 patterns/event-3-col.php:88
#: patterns/template-home-photo-blog.php:27
msgid "Tell your story"
msgstr "Conte a sua história"

#: patterns/hero-full-width-image.php:23
msgctxt "Sample hero heading"
msgid "Tell your story"
msgstr "Conte a sua história"

#: patterns/pricing-2-col.php:78 patterns/pricing-3-col.php:73
msgctxt "Name of membership package."
msgid "Single"
msgstr "Individual"

#: theme.json
msgctxt "Space size name"
msgid "Small"
msgstr "Pequeno"

#: patterns/hidden-404.php:21
msgctxt "image description"
msgid "Small totara tree on ridge above Long Point"
msgstr "Pequena árvore totara no topo acima de Long Point"

#: patterns/services-subscriber-only-section.php:69
msgid "Smartphones capturing a scenic wildflower meadow with trees"
msgstr "Telefones captando um pitoresco prado de flores silvestres com árvores"

#: patterns/contact-info-locations.php:29
#: patterns/contact-info-locations.php:31 patterns/footer-social.php:20
msgid "Social media"
msgstr "Redes sociais"

#: patterns/page-coming-soon.php:29
msgid "Something great is coming soon"
msgstr "Algo grande está por vir"

#: patterns/cta-book-links.php:43
msgctxt "Example brand name."
msgid "Spotify"
msgstr "Spotify"

#: patterns/hero-podcast.php:57
msgctxt "Button text"
msgid "Spotify"
msgstr "Spotify"

#. translators: %s: Starting price, split into three rows using HTML <br> tags.
#. The price value has a font size set.
#: patterns/cta-grid-products-link.php:58
msgid "Starting at%s/month"
msgstr "A partir de %s/mês"

#: patterns/banner-cover-big-heading.php:27 patterns/footer-columns.php:33
#: patterns/footer-columns.php:35 patterns/footer-newsletter.php:20
#: patterns/template-home-photo-blog.php:22
msgid "Stories"
msgstr "Histórias"

#: styles/sections/section-1.json
msgctxt "Style variation name"
msgid "Style 1"
msgstr "Estilo 1"

#: styles/sections/section-2.json
msgctxt "Style variation name"
msgid "Style 2"
msgstr "Estilo 2"

#: styles/sections/section-3.json
msgctxt "Style variation name"
msgid "Style 3"
msgstr "Estilo 3"

#: styles/sections/section-4.json
msgctxt "Style variation name"
msgid "Style 4"
msgstr "Estilo 4"

#: patterns/event-rsvp.php
msgctxt "Pattern description"
msgid "RSVP for an upcoming event with a cover image and event details."
msgstr "RSVP para um evento próximo com uma imagem de capa e detalhes do evento."

#: patterns/hidden-sidebar.php:38 patterns/page-portfolio-home.php:65
#: patterns/page-portfolio-home.php:87 patterns/page-portfolio-home.php:121
#: patterns/page-portfolio-home.php:154 patterns/page-portfolio-home.php:176
#: patterns/page-portfolio-home.php:203 patterns/template-home-news-blog.php:40
#: patterns/template-home-posts-grid-news-blog.php:35
#: patterns/template-home-posts-grid-news-blog.php:60
#: patterns/template-home-posts-grid-news-blog.php:78
#: patterns/template-home-posts-grid-news-blog.php:103
#: patterns/template-home-with-sidebar-news-blog.php:62
#: patterns/template-home-with-sidebar-news-blog.php:119
#: patterns/template-query-loop-news-blog.php:55
#: patterns/template-query-loop-photo-blog.php:22
#: patterns/template-query-loop-text-blog.php:19
#: patterns/template-query-loop-vertical-header-blog.php:47
#: patterns/template-query-loop.php:31
msgctxt "Message explaining that there are no results returned from a search."
msgid "Sorry, but nothing was found. Please try a search with different keywords."
msgstr "Nada foi encontrado. Tente pesquisar palavras diferentes."

#: patterns/contact-info-locations.php:74
msgid "Salt Lake City"
msgstr "Salt Lake City"

#: patterns/contact-info-locations.php:62
msgid "San Diego"
msgstr "San Diego"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search form label."
msgid "Search"
msgstr "Pesquisa"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Button text. Verb."
msgid "Search"
msgstr "Pesquisar"

#: patterns/services-3-col.php
msgctxt "Pattern title"
msgid "Services, 3 columns"
msgstr "Serviços, 3 colunas"

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern title"
msgid "Services, subscriber only section"
msgstr "Serviços, seção apenas para assinantes"

#: patterns/services-team-photos.php
msgctxt "Pattern title"
msgid "Services, team photos"
msgstr "Serviços, fotos da equipe"

#: patterns/footer-columns.php:50 patterns/footer.php:57
msgid "Shop"
msgstr "Loja"

#: patterns/page-shop-home.php
msgctxt "Pattern title"
msgid "Shop homepage"
msgstr "Página inicial da loja"

#: patterns/cta-grid-products-link.php:134
msgid "Shop now"
msgstr "Compre agora"

#: patterns/banner-intro-image.php
msgctxt "Pattern title"
msgid "Short heading and paragraph and image on the left"
msgstr "Título curto, parágrafo e imagem à esquerda"

#: patterns/logos.php
msgctxt "Pattern description"
msgid "Showcasing the podcast's clients with a heading and a series of client logos."
msgstr "Exibe os clientes do podcast com um título e uma série de logotipos dos clientes."

#: patterns/cta-newsletter.php:19
msgid "Sign up to get daily stories"
msgstr "Registre-se para receber histórias diariamente"

#: patterns/cta-book-links.php:51
msgctxt "Example brand name."
msgid "Simon &amp; Schuster"
msgstr "Simon &amp; Schuster"

#: patterns/pricing-3-col.php
msgctxt "Pattern title"
msgid "Pricing, 3 columns"
msgstr "Preços, 3 colunas"

#: patterns/binding-format.php
msgctxt "Pattern description"
msgid "Prints the name of the post format with the help of the Block Bindings API."
msgstr "Imprime o nome do formato de post com a ajuda da API de ligações de blocos."

#: patterns/media-instagram-grid.php:40
msgid "Profile portrait of a native person."
msgstr "Retrato de perfil de uma pessoa nativa."

#: patterns/template-single-offset.php:40
#: patterns/template-single-photo-blog.php:36
msgctxt "Prefix before the post date block."
msgid "Published on"
msgstr "Publicado em"

#: patterns/footer-newsletter.php:24
msgid "Receive our articles in your inbox."
msgstr "Receba os nossos artigos na sua caixa de entrada."

#: theme.json
msgctxt "Space size name"
msgid "Regular"
msgstr "Normal"

#: patterns/testimonials-large.php
msgctxt "Pattern title"
msgid "Review with large image on right"
msgstr "Avaliação com imagem grande à direita"

#: patterns/template-search-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned blog, search"
msgstr "Blog alinhado à direita, pesquisa"

#: patterns/template-single-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned single post"
msgstr "Post individual alinhado à direita"

#: styles/05-twilight.json styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Roboto Slab"
msgstr "Roboto Slab"

#: styles/typography/typography-preset-4.json
msgctxt "Style variation name"
msgid "Roboto Slab & Manrope"
msgstr "Roboto Slab e Manrope"

#: patterns/hero-podcast.php:65
msgctxt "Button text"
msgid "RSS"
msgstr "RSS"

#: patterns/event-rsvp.php:81
msgctxt "Abbreviation for \"Please respond\"."
msgid "RSVP"
msgstr "RSVP"

#: patterns/page-portfolio-home.php
msgctxt "Pattern title"
msgid "Portfolio homepage"
msgstr "Página inicial do portfólio"

#: patterns/contact-info-locations.php:86
msgid "Portland"
msgstr "Portland"

#: patterns/services-team-photos.php:38
msgid "Portrait of a nurse"
msgstr "Retrato de uma enfermeira"

#: patterns/media-instagram-grid.php:48
msgid "Portrait of an African Woman dressed in traditional costume, wearing decorative jewelry."
msgstr "Retrato de uma mulher africana vestida com trajes tradicionais, usando joias decorativas."

#: patterns/binding-format.php
msgctxt "Pattern title"
msgid "Post format name"
msgstr "Nome do formato de post"

#: functions.php:134
msgctxt "Label for the block binding placeholder in the editor"
msgid "Post format name"
msgstr "Nome do formato de post"

#: functions.php:113
msgid "Post formats"
msgstr "Formatos de post"

#: patterns/post-navigation.php:17 patterns/post-navigation.php:18
#: patterns/template-single-left-aligned-content.php:78
#: patterns/template-single-left-aligned-content.php:79
#: patterns/template-single-news-blog.php:95
#: patterns/template-single-news-blog.php:96
#: patterns/template-single-offset.php:61
#: patterns/template-single-offset.php:62
#: patterns/template-single-photo-blog.php:76
#: patterns/template-single-photo-blog.php:77
#: patterns/template-single-text-blog.php:36
#: patterns/template-single-text-blog.php:37
#: patterns/template-single-vertical-header-blog.php:82
#: patterns/template-single-vertical-header-blog.php:83
msgid "Post navigation"
msgstr "Navegação de posts"

#: patterns/template-single-left-aligned-content.php
msgctxt "Pattern title"
msgid "Post with left-aligned content"
msgstr "Post com conteúdo alinhado à esquerda"

#: patterns/template-single-photo-blog.php:42
msgctxt "Prefix before the author name. The post author name is displayed in a separate block on the next line."
msgid "Posted by"
msgstr "Publicado por"

#: patterns/banner-poster.php
msgctxt "Pattern title"
msgid "Poster-like section"
msgstr "Seção em estilo de pôster"

#: patterns/template-single-photo-blog.php:78
msgid "Previous Photo"
msgstr "Imagem anterior"

#: patterns/pricing-2-col.php:18 patterns/pricing-3-col.php:23
msgid "Pricing"
msgstr "Preços"

#: patterns/pricing-2-col.php
msgctxt "Pattern description"
msgid "Pricing section with two columns, pricing plan, description, and call-to-action buttons."
msgstr "Seção de preços com duas colunas, plano de preços, descrição e botões de chamada para ação."

#: patterns/pricing-2-col.php
msgctxt "Pattern title"
msgid "Pricing, 2 columns"
msgstr "Preços, 2 colunas"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:57
msgid "Photo of a woman worker."
msgstr "Foto de uma mulher trabalhadora."

#: patterns/banner-with-description-and-images-grid.php:42
#: patterns/overlapped-images.php:21
msgid "Photography close up of a red flower."
msgstr "Fotografia em primeiro plano de uma flor vermelha."

#: patterns/banner-intro-image.php:22
msgctxt "Alt text for intro picture."
msgid "Picture of a flower"
msgstr "Imagem de uma flor"

#: patterns/hero-full-width-image.php:18
msgctxt "Alt text for cover image."
msgid "Picture of a flower"
msgstr "Imagem de uma flor"

#: patterns/banner-poster.php:15
msgid "Picture of a historical building in ruins."
msgstr "Imagem de um edifício histórico em ruínas."

#: patterns/testimonials-2-col.php:26 patterns/testimonials-2-col.php:55
msgctxt "Alt text for testimonial image."
msgid "Picture of a person"
msgstr "Imagem de uma pessoa"

#: patterns/hero-podcast.php:22
msgctxt "Alt text for hero image."
msgid "Picture of a person"
msgstr "Imagem de uma pessoa"

#: patterns/services-team-photos.php:44
msgid "Picture of a person typing on a typewriter."
msgstr "Imagem de uma pessoa digitando em uma máquina de escrever."

#: patterns/testimonials-large.php:47
msgctxt "Alt text for testimonial image."
msgid "Picture of a person typing on a typewriter."
msgstr "Imagem de uma pessoa digitando em uma máquina de escrever."

#: styles/blocks/post-terms-1.json
msgctxt "Style variation name"
msgid "Pill shaped"
msgstr "Arredondado"

#: styles/04-afternoon.json styles/07-sunrise.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-6.json
msgctxt "Font family name"
msgid "Platypi"
msgstr "Platypi"

#: styles/typography/typography-preset-6.json
msgctxt "Style variation name"
msgid "Platypi & Literata"
msgstr "Platypi e Literata"

#: styles/typography/typography-preset-3.json
msgctxt "Style variation name"
msgid "Platypi & Ysabeau Office"
msgstr "Platypi e Ysabeau Office"

#: patterns/hero-podcast.php:61
msgctxt "Button text"
msgid "Pocket Casts"
msgstr "Pocket Casts"

#: patterns/grid-videos.php:23
msgid "Podcast"
msgstr "Podcast"

#: patterns/services-3-col.php:17
msgid "Our services"
msgstr "Nossos serviços"

#: patterns/services-team-photos.php:21
msgid "Our small team is a group of driven, detail-oriented people who are passionate about their customers."
msgstr "A nossa pequena equipe é um grupo de pessoas motivadas e orientadas aos detalhes que são apaixonadas por seus clientes."

#: patterns/cta-book-links.php:57
#: patterns/hero-overlapped-book-cover-with-links.php:100
msgctxt "Pattern placeholder text with link."
msgid "Outside Europe? View <a href=\"#\" rel=\"nofollow\">international editions</a>."
msgstr "Fora da Europa? Conheça <a href=\"#\" rel=\"nofollow\">as edições internacionais</a>."

#: patterns/overlapped-images.php
msgctxt "Pattern title"
msgid "Overlapping images and paragraph on right"
msgstr "Imagens sobrepostas e parágrafo à direita"

#: patterns/hidden-404.php:32
msgctxt "404 error message"
msgid "Page not found"
msgstr "Página não encontrada"

#: functions.php:105
msgid "Pages"
msgstr "Páginas"

#: patterns/footer-columns.php:51 patterns/footer.php:59
msgid "Patterns"
msgstr "Padrões"

#: patterns/template-page-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog page"
msgstr "Página de blog de fotografias"

#: patterns/template-search-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog search results"
msgstr "Resultados de pesquisa de blog de fotografias"

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog posts"
msgstr "Posts de blog de fotografias"

#: patterns/template-archive-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog archive"
msgstr "Arquivo de blog de fotografias"

#: patterns/template-home-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog home"
msgstr "Página inicial de blog de fotografias"

#: patterns/template-single-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog single post"
msgstr "Post individual de blog de fotografias"

#: patterns/banner-cover-big-heading.php:20
#: patterns/media-instagram-grid.php:36 patterns/page-coming-soon.php:19
msgid "Photo of a field full of flowers, a blue sky and a tree."
msgstr "Foto de um campo repleto de flores, um céu azul e uma árvore."

#: theme.json
msgctxt "Template part name"
msgid "Footer Columns"
msgstr "Colunas do rodapé"

#: styles/02-noon.json styles/typography/typography-preset-1.json
msgctxt "Font family name"
msgid "Beiruti"
msgstr "Beiruti"

#: styles/08-midnight.json styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Fira Sans"
msgstr "Fira Sans"

#: theme.json styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Fira Code"
msgstr "Fira Code"

#: theme.json
msgctxt "Space size name"
msgid "Large"
msgstr "Grande"

#: patterns/template-home-with-sidebar-news-blog.php
msgctxt "Pattern title"
msgid "News blog with sidebar"
msgstr "Blog de notícias com barra lateral"

#: patterns/template-home-news-blog.php
msgctxt "Pattern title"
msgid "News blog home"
msgstr "Página inicial do blog de notícias"

#: patterns/services-team-photos.php:50
msgid "Man in hat, standing in front of a building."
msgstr "Homem de chapéu, em pé em frente a um prédio."

#: patterns/services-3-col.php:68
msgid "Deliver"
msgstr "Entregar"

#: patterns/services-3-col.php:36 patterns/services-3-col.php:54
#: patterns/services-3-col.php:72
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience"
msgstr "Como flores que desabrocham em lugares inesperados, cada história se desenrola com beleza e resiliência"

#: patterns/services-3-col.php:50
msgid "Assemble"
msgstr "Montar"

#: patterns/services-3-col.php:27 patterns/services-3-col.php:45
#: patterns/services-3-col.php:63
msgid "Image for service"
msgstr "Imagem para serviço"

#: patterns/pricing-3-col.php:89 patterns/pricing-3-col.php:129
msgid "Month"
msgstr "Mês"

#: patterns/pricing-3-col.php
msgctxt "Pattern description"
msgid "A three-column boxed pricing table designed to showcase services, descriptions, and pricing options."
msgstr "Uma tabela de preços em formato de caixas, dividida em três colunas, projetada para destacar os serviços, descrições e opções de preço."

#: patterns/pricing-2-col.php:68 patterns/pricing-2-col.php:112
#: patterns/pricing-3-col.php:59 patterns/pricing-3-col.php:99
#: patterns/pricing-3-col.php:139
msgctxt "Button text, refers to joining a community. Verb."
msgid "Join"
msgstr "Participe"

#: patterns/pricing-2-col.php:60 patterns/pricing-2-col.php:104
#: patterns/services-subscriber-only-section.php:43
msgid "Join our forums."
msgstr "Participe dos nossos fóruns."

#: patterns/pricing-2-col.php:48 patterns/pricing-2-col.php:92
#: patterns/services-subscriber-only-section.php:31
msgid "Join our IRL events."
msgstr "Participe dos nossos eventos presenciais."

#: patterns/pricing-2-col.php:22
#: patterns/services-subscriber-only-section.php:61
msgid "Cancel or pause anytime."
msgstr "Cancele ou pause a qualquer momento."

#: patterns/page-portfolio-home.php:229
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/page-portfolio-home.php:27
msgid "My name is Anna Möller and these are some of my photo projects."
msgstr "Meu nome é Anna Möller e estes são alguns dos meus projetos fotográficos."

#: patterns/page-link-in-bio-with-tight-margins.php:42
msgid "I’m Asahachi Kōno, a Japanese photographer, a member of Los Angeles’s Japanese Camera Pictorialists of California. Before returning to Japan, I worked as a photo retoucher."
msgstr "Sou Asahachi Kōno, um fotógrafo japonês e membro do grupo Japanese Camera Pictorialists of California, em Los Angeles. Antes de retornar ao Japão, trabalhei como retocador de fotos."

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns, with only featured images."
msgstr "Uma lista de posts, 3 colunas, apenas com imagens em destaque."

#: patterns/template-query-loop.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column, with featured image and post date."
msgstr "Uma lista de posts, 1 coluna, com imagem em destaque e data da publicação."

#: patterns/page-portfolio-home.php
msgctxt "Pattern description"
msgid "A portfolio homepage pattern."
msgstr "Um padrão para a página inicial de portfólio."

#: patterns/page-shop-home.php
msgctxt "Pattern description"
msgid "A shop homepage pattern."
msgstr "Um padrão para a página inicial de uma loja."

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern description"
msgid "A subscriber-only section highlighting exclusive services and offerings."
msgstr "Uma seção apenas para assinantes destacando serviços e ofertas exclusivas."

#: patterns/template-single-photo-blog.php:53
msgctxt "Prefix before one or more categories. The categories are displayed in a separate block on the next line."
msgid "Categories:"
msgstr "Categorias:"

#: patterns/pricing-3-col.php:19
msgid "Choose your membership"
msgstr "Escolha sua assinatura"

#: patterns/services-3-col.php:32
msgid "Collect"
msgstr "Coletar"

#: patterns/services-team-photos.php
msgctxt "Pattern description"
msgid "Display team photos in a services section with grid layout."
msgstr "Exiba fotos da equipe em uma seção de serviços com leiaute em grade."

#: theme.json
msgctxt "Template part name"
msgid "Footer Newsletter"
msgstr "Informativo no rodapé"

#: patterns/text-faqs.php:21
msgctxt "Heading of the FAQs pattern."
msgid "Frequently Asked Questions"
msgstr "Perguntas mais frequentes"

#: patterns/pricing-2-col.php:52 patterns/pricing-2-col.php:96
#: patterns/services-subscriber-only-section.php:35
msgid "Get a free tote bag."
msgstr "Receba grátis uma sacola ecológica."

#: patterns/pricing-3-col.php:41
msgid "Get access to our free articles and weekly newsletter."
msgstr "Tenha acesso aos nossos artigos gratuitos e ao nosso informativo semanal."

#: patterns/pricing-2-col.php:44 patterns/pricing-2-col.php:88
#: patterns/services-subscriber-only-section.php:27
msgid "Get access to our paid articles and weekly newsletter."
msgstr "Tenha acesso aos nossos artigos pagos e ao nosso informativo semanal."

#: patterns/pricing-3-col.php:77
msgid "Get access to our paid newsletter and a limited pass for one event."
msgstr "Tenha acesso ao nosso informativo pago e obtenha um ingresso para participar de um evento."

#: patterns/pricing-3-col.php:117
msgid "Get access to our paid newsletter and an unlimited pass."
msgstr "Tenha acesso ao nosso informativo pago e um acesso total."

#: theme.json
msgctxt "Template part name"
msgid "Header with large title"
msgstr "Cabeçalho com título grande."

#: patterns/template-query-loop-news-blog.php:49
msgid "Older Posts"
msgstr "Posts anteriores"

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern title"
msgid "Link in bio with tight margins"
msgstr "Link na bio com margens reduzidas"

#: patterns/template-query-loop-news-blog.php:45
msgid "Newer Posts"
msgstr "Posts recentes"

#: patterns/template-archive-news-blog.php
msgctxt "Pattern title"
msgid "News blog archive"
msgstr "Arquivo do blog de notícias"

#: patterns/template-search-news-blog.php
msgctxt "Pattern title"
msgid "News blog search results"
msgstr "Resultados da pesquisa no blog de notícias"

#: patterns/template-home-posts-grid-news-blog.php
msgctxt "Pattern title"
msgid "News blog with featured posts grid"
msgstr "Blog de notícias com grade de posts em destaque"

#: patterns/post-navigation.php
msgctxt "Pattern description"
msgid "Next and previous post links."
msgstr "Links para o post anterior e o próximo post."

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern description"
msgid "A full-width, full-height link in bio section with an image, a paragraph and social links."
msgstr "Um link de largura e altura total com uma imagem, um parágrafo e links sociais na seção de biografia."

#: patterns/media-instagram-grid.php
msgctxt "Pattern description"
msgid "A grid section with photos and a link to an Instagram profile."
msgstr "Uma seção com uma grade de fotos e um link para um perfil do Instagram."

#: patterns/grid-videos.php
msgctxt "Pattern description"
msgid "A grid with videos."
msgstr "Uma grade com vídeos."

#: patterns/event-3-col.php
msgctxt "Pattern description"
msgid "A header with title and text and three columns that show 3 events with their images and titles."
msgstr "Um cabeçalho com título e texto, e três colunas que mostram três eventos, com respectivas imagens e títulos."

#: patterns/hero-full-width-image.php
msgctxt "Pattern description"
msgid "A hero with a full width image, heading, short paragraph and button."
msgstr "Um destaque com uma imagem de largura total, um título, um parágrafo curto e um botão."

#: patterns/cta-centered-heading.php
msgctxt "Pattern description"
msgid "A hero with a centered heading, paragraph and button."
msgstr "Um destaque com um título, um parágrafo e um botão centralizados."

#: patterns/hero-book.php
msgctxt "Pattern description"
msgid "A hero section for the book with a description and pre-order link."
msgstr "Um destaque para um livro com uma descrição e um link para pré-compra."

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern description"
msgid "A hero with an overlapped book cover and links."
msgstr "Um destaque com uma capa de livro sobreposta e links."

#: patterns/banner-intro-image.php
msgctxt "Pattern description"
msgid "A Intro pattern with Short heading, paragraph and image on the left."
msgstr "Um padrão de introdução com um título curto, um parágrafo e uma imagem à esquerda."

#: patterns/page-landing-book.php
msgctxt "Pattern description"
msgid "A landing page for the book with a hero section, pre-order links, locations, FAQs and newsletter signup."
msgstr "Uma página de captação para o livro com uma seção de destaque, links para pré-venda, locais, perguntas frequentes e inscrição para o informativo."

#: patterns/page-landing-event.php
msgctxt "Pattern description"
msgid "A landing page for the event with a hero section, description, FAQs and call to action."
msgstr "Uma página de captação para o evento com uma seção de destaque, descrição, perguntas frequentes e uma chamada para ação."

#: patterns/page-landing-podcast.php
msgctxt "Pattern description"
msgid "A landing page for the podcast with a hero section, description, logos, grid with videos and newsletter signup."
msgstr "Uma página de captação para um podcast com uma seção de destaque, descrição, logotipos, grade de vídeos e inscrição para receber informativo."

#: patterns/banner-intro.php
msgctxt "Pattern description"
msgid "A large left-aligned heading with a brand name emphasized in bold."
msgstr "Um título grande alinhado à esquerda com um nome da marca realçado em negrito."

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern description"
msgid "A link in bio landing page with a heading, paragraph, links and a full height image."
msgstr "Um link na página de biografia com um título, parágrafo, links e uma imagem de altura total."

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern description"
msgid "A link in bio landing page with social links, a profile photo and a brief description."
msgstr "Um link na página de biografia com redes sociais, uma foto de perfil e uma breve descrição."

#: patterns/banner-intro-image.php:31
msgctxt "Heading for banner pattern."
msgid "New arrivals"
msgstr "Novas chegadas"

#: patterns/contact-info-locations.php:51
msgid "New York"
msgstr "Nova Iorque"

#: patterns/cta-book-locations.php:95
msgid "New Zealand"
msgstr "Nova Zelândia"

#: patterns/template-query-loop-news-blog.php
msgctxt "Pattern title"
msgid "News blog query loop"
msgstr "Loop de consulta do blog de notícias"

#: patterns/template-single-news-blog.php
msgctxt "Pattern title"
msgid "News blog single post with sidebar"
msgstr "Post de blog de notícias com barra lateral"

#: patterns/cta-newsletter.php
msgctxt "Pattern title"
msgid "Newsletter sign-up"
msgstr "Assinar o informativo"

#: patterns/template-single-photo-blog.php:79
msgid "Next Photo"
msgstr "Foto seguinte"

#: styles/02-noon.json styles/colors/02-noon.json
msgctxt "Style variation name"
msgid "Noon"
msgstr "Meio-dia"

#: patterns/page-link-in-bio-wide-margins.php:34
msgid "Nora Winslow Keene"
msgstr "Nora Winslow Keene"

#: patterns/page-cv-bio.php:47
msgctxt "Link to a page with information about what the person is working on right now."
msgid "Now"
msgstr "Agora"

#: patterns/template-single-offset.php
msgctxt "Pattern title"
msgid "Offset post without featured image"
msgstr "Post deslocado sem imagem de destaque"

#: patterns/hidden-sidebar.php:14
msgid "Other Posts"
msgstr "Outros posts"

#: patterns/testimonials-2-col.php:67 patterns/testimonials-6-col.php:34
#: patterns/testimonials-6-col.php:51 patterns/testimonials-6-col.php:68
#: patterns/testimonials-6-col.php:89 patterns/testimonials-6-col.php:104
#: patterns/testimonials-6-col.php:119
msgctxt "Sample testimonial citation."
msgid "Otto Reid <br><sub>Springfield, IL</sub>"
msgstr "Otto Reid <br><sub>Springfield, IL</sub>"

#: patterns/cta-grid-products-link.php:20
msgid "Our online store."
msgstr "A nossa loja on-line."

#: patterns/format-link.php
msgctxt "Pattern description"
msgid "A link post format with a description and an emphasized link for key content."
msgstr "Um formato de publicação com uma descrição e um link enfatizado para o conteúdo principal."

#: patterns/cta-events-list.php
msgctxt "Pattern description"
msgid "A list of events with call to action."
msgstr "Uma lista de eventos com uma chamada à ação."

#: patterns/page-cv-bio.php
msgctxt "Pattern description"
msgid "A pattern for a CV/Bio landing page."
msgstr "Um padrão para uma página de biografia ou currículo."

#: patterns/banner-poster.php
msgctxt "Pattern description"
msgid "A section that can be used as a banner or a landing page to announce an event."
msgstr "Uma seção que pode ser usada como um cartaz ou uma página para anunciar um evento."

#: patterns/overlapped-images.php
msgctxt "Pattern description"
msgid "A section with overlapping images, and a description."
msgstr "Uma seção com imagens sobrepostas e uma descrição."

#: patterns/event-schedule.php
msgctxt "Pattern description"
msgid "A section with specified dates and times for an event."
msgstr "Uma seção com datas e horários específicos de um evento."

#: patterns/testimonials-6-col.php
msgctxt "Pattern description"
msgid "A section with three columns and two rows, each containing a testimonial and citation."
msgstr "Uma seção com três colunas e duas linhas, cada uma contendo um depoimento e uma citação."

#: patterns/testimonials-large.php
msgctxt "Pattern description"
msgid "A testimonial with a large image on the right."
msgstr "Um depoimento com uma imagem grande à direita."

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern description"
msgid "A two-column section with a heading and paragraph on the left, and an image on the right."
msgstr "Uma seção de duas colunas com um título e um parágrafo à esquerda, e uma imagem à direita."

#: patterns/banner-about-book.php:22
msgid "About the book"
msgstr "Sobre o livro"

#: patterns/heading-and-paragraph-with-image.php:23
msgid "About the event"
msgstr "Sobre o evento"

#: patterns/banner-with-description-and-images-grid.php:23
#: patterns/overlapped-images.php:37
msgid "About Us"
msgstr "Sobre nós"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 2"
msgstr "Realce 2"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 1"
msgstr "Realce 1"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 3"
msgstr "Realce 3"

#: patterns/template-home-with-sidebar-news-blog.php:88
#: patterns/template-single-left-aligned-content.php:56
#: patterns/template-single-news-blog.php:39
msgctxt "Separator between date and categories."
msgid "·"
msgstr "·"

#: patterns/cta-book-links.php:47
msgctxt "Example brand name."
msgid "BAM!"
msgstr "BAM!"

#: patterns/banner-about-book.php
msgctxt "Pattern title"
msgid "Banner with book description"
msgstr "Cartaz com descrição do livro"

#: patterns/banner-about-book.php
msgctxt "Pattern description"
msgid "Banner with book description and accompanying image for promotion."
msgstr "Cartaz com descrição do livro e uma imagem promocional."

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern title"
msgid "Banner with description and images grid"
msgstr "Cartaz com descrição e grade de imagens"

#: patterns/cta-book-links.php:31
#: patterns/hero-overlapped-book-cover-with-links.php:84
msgctxt "Example brand name."
msgid "Barnes &amp; Noble"
msgstr "Barnes &amp; Noble"

#: styles/typography/typography-preset-1.json
msgctxt "Style variation name"
msgid "Beiruti & Literata"
msgstr "Beiruti e Literata"

#: patterns/grid-with-categories.php:50
msgid "Cactus"
msgstr "Cacto"

#: patterns/cta-book-links.php
msgctxt "Pattern title"
msgid "Call to action with book links"
msgstr "Chamada para ação com links do livro"

#: patterns/cta-grid-products-link.php
msgctxt "Pattern title"
msgid "Call to action with grid layout with products and link"
msgstr "Chamada para ação com uma grade de produtos e link"

#: patterns/cta-book-locations.php
msgctxt "Pattern title"
msgid "Call to action with locations"
msgstr "Chamada para ação com locais"

#: patterns/cta-book-locations.php:67
msgid "Canada"
msgstr "Canadá"

#: patterns/cta-grid-products-link.php:100
msgid "Cancel anytime"
msgstr "Cancele quando quiser"

#: patterns/contact-centered-social-link.php
msgctxt "Pattern description"
msgid "Centered contact section with a prominent message and social media links."
msgstr "Seção de contatos centralizada com uma mensagem proeminente e links para redes sociais."

#: patterns/footer-centered.php
msgctxt "Pattern title"
msgid "Centered footer"
msgstr "Rodapé centralizado"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 4"
msgstr "Realce 4"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 5"
msgstr "Realce 5"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 6"
msgstr "Realce 6"

#: patterns/format-audio.php:30
msgid "Acoma Pueblo, in New Mexico, stands as a testament to the resilience and cultural heritage of the Acoma people"
msgstr "Acoma Pueblo, no Novo México, é um testemunho da resiliência e do patrimônio cultural do povo Acoma"

#: patterns/hidden-sidebar.php:37
#: patterns/template-home-posts-grid-news-blog.php:34
#: patterns/template-home-with-sidebar-news-blog.php:61
msgid "Add text or blocks that will display when a query returns no results."
msgstr "Adicione textos ou blocos que serão exibidos quando a consulta não retornar nenhum resultado."

#: styles/04-afternoon.json styles/colors/04-afternoon.json
msgctxt "Style variation name"
msgid "Afternoon"
msgstr "Tarde"

#: patterns/event-schedule.php:20
msgid "Agenda"
msgstr "Agenda"

#: patterns/cta-book-links.php:23
#: patterns/hero-overlapped-book-cover-with-links.php:55
msgctxt "Example brand name."
msgid "Amazon"
msgstr "Amazon"

#: patterns/format-audio.php
msgctxt "Pattern description"
msgid "An audio post format with an image, title, audio player, and description."
msgstr "Um formato de post de áudio com uma imagem, título, reprodutor de áudio e descrição."

#: patterns/cta-book-locations.php:43
msgid "Australia"
msgstr "Austrália"

#: patterns/footer-columns.php:39 patterns/footer.php:51
msgid "Authors"
msgstr "Autores"

#: patterns/hero-book.php:46
msgctxt "CTA text of the hero section."
msgid "Available for pre-order now."
msgstr "Já disponível em pré-venda."

#: patterns/testimonials-2-col.php:65 patterns/testimonials-6-col.php:30
#: patterns/testimonials-6-col.php:47 patterns/testimonials-6-col.php:64
#: patterns/testimonials-6-col.php:85 patterns/testimonials-6-col.php:101
#: patterns/testimonials-6-col.php:116
msgctxt "Sample testimonial."
msgid "“Amazing quality and care. I love all your products.”"
msgstr "\"Excelente qualidade e cuidado. Adoro todos os seus produtos.\""

#. translators: This string contains the word "Stories" in four different
#. languages with the first item in the locale's language.
#: patterns/banner-poster.php:28 patterns/cta-events-list.php:68
#: patterns/cta-events-list.php:137 patterns/event-rsvp.php:30
msgctxt "Placeholder heading in four languages."
msgid "“Stories, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"
msgstr "“Histórias, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"

#: patterns/testimonials-2-col.php:36 patterns/testimonials-large.php:32
msgctxt "Sample testimonial."
msgid "“Superb product and customer service!”"
msgstr "\"Soberbos produtos e atendimento ao cliente!\""

#: patterns/footer-social.php
msgctxt "Pattern title"
msgid "Centered footer with social links"
msgstr "Rodapé centralizado com links para redes sociais"

#: patterns/cta-centered-heading.php
msgctxt "Pattern title"
msgid "Centered heading"
msgstr "Título centralizado"

#: patterns/contact-centered-social-link.php
msgctxt "Pattern title"
msgid "Centered link and social links"
msgstr "Link centralizado e links para redes sociais"

#: patterns/heading-and-paragraph-with-image.php:36
msgctxt "Alt text for Overview picture."
msgid "Cliff Palace, Colorado"
msgstr "Cliff Palace, Colorado"

#: patterns/grid-with-categories.php:29
msgid "Close up of a red anthurium."
msgstr "Vista de perto de um antúrio vermelho."

#: patterns/media-instagram-grid.php:56
msgid "Close up of two flowers on a dark background."
msgstr "Vista de perto de duas flores em um fundo escuro."

#: patterns/event-rsvp.php:91
msgid "Close up photo of white flowers on a grey background"
msgstr "Foto em primeiro plano de flores brancas em um fundo cinza"

#: patterns/cta-grid-products-link.php:38
msgid "Closeup of plantlife in the Malibu Canyon area"
msgstr "Vista em primeiro plano da flora na área do cânion de Malibu"

#: patterns/page-coming-soon.php
msgctxt "Pattern title"
msgid "Coming soon"
msgstr "Em breve"

#: patterns/comments.php
msgctxt "Pattern description"
msgid "Comments area with comments list, pagination, and comment form."
msgstr "Área de comentáros com lista de comentários, paginação e formulário de comentário."

#: patterns/contact-location-and-link.php
msgctxt "Pattern title"
msgid "Contact location and link"
msgstr "Localização e link do contato"

#: patterns/contact-location-and-link.php
msgctxt "Pattern description"
msgid "Contact section with a location address, a directions link, and an image of the location."
msgstr "Seção de contatos com endereço, link com direções e uma imagem do local."

#: patterns/contact-info-locations.php
msgctxt "Pattern description"
msgid "Contact section with social media links, email, and multiple location details."
msgstr "Seção de contatos com links para redes sociais, e-mail e múltiplos de detalhes do local."

#: patterns/contact-info-locations.php
msgctxt "Pattern title"
msgid "Contact, info and locations"
msgstr "Contato, informações e localizações"

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern title"
msgid "Cover with big heading"
msgstr "Capa com título grande"

#: patterns/page-cv-bio.php
msgctxt "Pattern title"
msgid "CV/bio"
msgstr "Biografia / currículo"

#: patterns/cta-grid-products-link.php:32
msgid "Delivered every week"
msgstr "Entregue toda semana"

#. translators: Designed with WordPress. %s: WordPress link.
#: patterns/footer-centered.php:33 patterns/footer-columns.php:73
#: patterns/footer-newsletter.php:49 patterns/footer-social.php:35
#: patterns/footer.php:82
msgid "Designed with %s"
msgstr "Criado com %s"

#: styles/blocks/01-display.json
msgctxt "Style variation name"
msgid "Display"
msgstr "Decorativo"

#: patterns/more-posts.php
msgctxt "Pattern description"
msgid "Displays a list of posts with title and date."
msgstr "Exibe uma lista de posts com título e data."

#: styles/03-dusk.json styles/colors/03-dusk.json
msgctxt "Style variation name"
msgid "Dusk"
msgstr "Anoitecer"

#: patterns/contact-info-locations.php:38
msgid "Email"
msgstr "E-mail"

#: patterns/format-audio.php:26
msgid "Episode 1: Acoma Pueblo with Prof. Fiona Presley"
msgstr "Episódio 1: Acoma Pueblo com a Profa. Fiona Presley"

#: styles/01-evening.json styles/colors/01-evening.json
msgctxt "Style variation name"
msgid "Evening"
msgstr "Noite"

#: patterns/page-coming-soon.php:24
msgid "Event"
msgstr "Evento"

#: patterns/event-3-col.php:50 patterns/event-3-col.php:74
#: patterns/event-3-col.php:98
msgid "Event details"
msgstr "Detalhes do evento"

#: patterns/event-3-col.php:34 patterns/event-3-col.php:58
#: patterns/event-3-col.php:82 patterns/format-audio.php:20
msgid "Event image"
msgstr "Imagem do evento"

#: patterns/event-rsvp.php
msgctxt "Pattern title"
msgid "Event RSVP"
msgstr "RSVP do evento"

#: patterns/event-schedule.php
msgctxt "Pattern title"
msgid "Event schedule"
msgstr "Agenda do evento"

#: patterns/event-3-col.php:20 patterns/footer-columns.php:49
#: patterns/footer.php:55
msgid "Events"
msgstr "Eventos"

#: patterns/cta-events-list.php
msgctxt "Pattern title"
msgid "Events list"
msgstr "Lista de eventos"

#: patterns/event-3-col.php
msgctxt "Pattern title"
msgid "Events, 3 columns with event images and titles"
msgstr "Eventos, 3 colunas com imagens e títulos de eventos"

#: patterns/contact-info-locations.php:41
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/pricing-3-col.php:113
msgctxt "Name of membership package."
msgid "Expert"
msgstr "Especialista"

#: patterns/grid-videos.php:19
msgid "Explore the episodes"
msgstr "Explore os episódios"

#: patterns/text-faqs.php
msgctxt "Pattern title"
msgid "FAQs"
msgstr "Perguntas frequentes"

#: patterns/footer-columns.php:38 patterns/footer.php:49
msgid "FAQs"
msgstr "Perguntas frequentes"

#: patterns/event-schedule.php:57
msgid "Fauna from North America and its characteristics"
msgstr "Fauna da América do Norte e as suas características"

#: patterns/footer-columns.php:48
msgid "Featured"
msgstr "Em destaque"

#: patterns/banner-intro.php:22
#: patterns/banner-with-description-and-images-grid.php:32
#: patterns/footer-columns.php:46 patterns/overlapped-images.php:48
msgctxt "Example brand name."
msgid "Fleurs"
msgstr "Fleurs"

#: patterns/cta-grid-products-link.php:70
msgid "Flora of Akaka Falls State Park"
msgstr "Flora do parque estadual de Akaka Falls"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer"
msgstr "Rodapé"

#: patterns/footer.php
msgctxt "Pattern description"
msgid "Footer columns with logo, title, tagline and links."
msgstr "Colunas de rodapé com logotipo, título, descrição e links."

#: patterns/footer-columns.php
msgctxt "Pattern description"
msgid "Footer columns with title, tagline and links."
msgstr "Colunas de rodapé com título, descrição e links."

#: patterns/footer-social.php
msgctxt "Pattern description"
msgid "Footer with centered site title and social links."
msgstr "Rodapé com título do site centralizado e links para redes sociais."

#: patterns/footer-centered.php
msgctxt "Pattern description"
msgid "Footer with centered site title and tagline."
msgstr "Rodapé com título do site e slogan centralizados."

#: patterns/footer-columns.php
msgctxt "Pattern title"
msgid "Footer with columns"
msgstr "Rodapé com colunas"

#: patterns/footer-newsletter.php
msgctxt "Pattern description"
msgid "Footer with large site title and newsletter signup."
msgstr "Rodapé com o título grande do site e assinatura do informativo."

#: patterns/footer-newsletter.php
msgctxt "Pattern title"
msgid "Footer with newsletter signup"
msgstr "Rodapé com assinatura do informativo"

#: patterns/pricing-2-col.php:34 patterns/pricing-3-col.php:37
msgid "Free"
msgstr "Grátis"

#: patterns/cta-grid-products-link.php:84
msgid "Free shipping"
msgstr "Frete grátis"

#: patterns/event-rsvp.php:57
msgid "Free Workshop"
msgstr "Oficina gratuita"

#: patterns/banner-poster.php:39
msgid "Fuego Bar, Mexico City"
msgstr "Fuego Bar, Cidade do México"

#: patterns/cta-newsletter.php:23
msgid "Get access to a curated collection of moments in time featuring photographs from historical relevance."
msgstr "Obtenha o acesso a uma seleção de momentos marcantes com fotografias de relevância histórica."

#: patterns/contact-location-and-link.php:26
msgid "Get directions"
msgstr "Obter direções"

#: patterns/contact-centered-social-link.php:21
msgctxt "Heading of the Contact social link pattern"
msgid "Got questions? <br><a href=\"#\" rel=\"nofollow\">Feel free to reach out.</a>"
msgstr "Perguntas? <br><a href=\"#\" rel=\"nofollow\">Sinta-se à vontade para nos contatar.</a>"

#: patterns/grid-with-categories.php
msgctxt "Pattern title"
msgid "Grid with categories"
msgstr "Grade com categorias"

#: patterns/grid-videos.php
msgctxt "Pattern title"
msgid "Grid with videos"
msgstr "Grade com vídeos"

#: patterns/header.php
msgctxt "Pattern title"
msgid "Header"
msgstr "Cabeçalho"

#: patterns/header-columns.php
msgctxt "Pattern title"
msgid "Header with columns"
msgstr "Cabeçalho com colunas"

#: patterns/header-large-title.php
msgctxt "Pattern title"
msgid "Header with large title"
msgstr "Cabeçalho com título grande"

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern title"
msgid "Heading and paragraph with image on the right"
msgstr "Título e parágrafo com imagem à direita"

#: patterns/cta-heading-search.php
msgctxt "Pattern title"
msgid "Heading and search form"
msgstr "Cabeçalho e formulário de pesquisa"

#: patterns/heading-and-paragraph-with-image.php:27
msgctxt "Event Overview Text."
msgid "Held over a weekend, the event is structured around a series of exhibitions, workshops, and panel discussions. The exhibitions showcase a curated selection of photographs that tell compelling stories from various corners of the globe, each image accompanied by detailed narratives that provide context and deeper insight into the historical significance of the scenes depicted. These photographs are drawn from the archives of renowned photographers, as well as emerging talents, ensuring a blend of both classical and contemporary perspectives."
msgstr "Realizado ao longo de um fim de semana, o evento é estruturado em torno de uma série de exposições, oficinas e painéis de discussão. As exposições apresentam uma seleção de fotografias que contam histórias envolventes de vários cantos do globo, cada imagem acompanhada por narrativas detalhadas que fornecem contexto e uma visão aprofundada sobre o significado histórico das cenas retratadas. Essas fotografias foram extraídas dos arquivos de fotógrafos renomados, bem como de talentos emergentes, garantindo uma combinação entre perspectivas clássicas e contemporâneas."

#: patterns/hero-book.php
msgctxt "Pattern title"
msgid "Hero book"
msgstr "Livro em destaque"

#: patterns/hero-podcast.php
msgctxt "Pattern title"
msgid "Hero podcast"
msgstr "Podcast em destaque"

#: patterns/hero-full-width-image.php
msgctxt "Pattern title"
msgid "Hero, full width image"
msgstr "Destaque, imagem em largura total"

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern title"
msgid "Hero, overlapped book cover with links"
msgstr "Destaque, sobreposição de capa de livro com links"

#: patterns/page-cv-bio.php:28
msgctxt "Example heading in pattern."
msgid "Hey,"
msgstr "Olá,"

#: patterns/hidden-blog-heading.php
msgctxt "Pattern title"
msgid "Hidden blog heading"
msgstr "Título de blog oculto"

#: patterns/hidden-blog-heading.php
msgctxt "Pattern description"
msgid "Hidden heading for the home page and index template."
msgstr "Título oculto para a página inicial e modelo para índice"

#: patterns/format-link.php:17
msgid "The Stories Book, a fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno, is available for pre-order"
msgstr "O Livro de Histórias, uma fina coleção de momentos no tempo apresentando fotografias de Louis Fleckenstein, Paul Strand e Asahachi Kōno, já está disponível em pré-venda"

#: patterns/text-faqs.php:47
msgctxt "Question in the FAQs pattern."
msgid "How much does The Stories Book cost?"
msgstr "Quanto custa o Livro de Histórias?"

#: patterns/contact-info-locations.php:21
msgid "How to get in touch with us"
msgstr "Como entrar em contato conosco"

#: patterns/format-link.php:23
msgid "https://example.com"
msgstr "https://exemplo.com"

#: patterns/page-link-in-bio-wide-margins.php:38
msgctxt "Pattern placeholder text."
msgid "I’m Nora, a dedicated public interest attorney based in Denver. I’m a graduate of Stanford University."
msgstr "Sou Nora, uma advogada dedicada ao interesse público, baseada em Denver. Sou formada pela Universidade de Stanford."

#: patterns/banner-about-book.php:34
msgid "Image of a book"
msgstr "Imagem de um livro"

#: patterns/hero-book.php:24
msgid "Image of the book"
msgstr "Imagem do livro"

#: patterns/hidden-written-by.php:20
msgid "in"
msgstr "em"

#: patterns/media-instagram-grid.php
msgctxt "Pattern title"
msgid "Instagram grid"
msgstr "Grade do Instagram"

#: patterns/cta-book-locations.php:23
msgid "International editions"
msgstr "Edições internacionais"

#: patterns/banner-intro.php
msgctxt "Pattern title"
msgid "Intro with left-aligned description"
msgstr "Introdução com descrição alinhada à esquerda"

#: patterns/cta-book-locations.php:79
msgid "Japan"
msgstr "Japão"

#: patterns/testimonials-2-col.php:38 patterns/testimonials-large.php:36
msgctxt "Sample testimonial citation."
msgid "Jo Mulligan <br /><sub>Atlanta, GA</sub>"
msgstr "Jo Mulligan <br /><sub>Atlanta, GA</sub>"

#: patterns/page-landing-book.php
msgctxt "Pattern title"
msgid "Landing page for book"
msgstr "Página de captação para livros"

#: patterns/page-landing-event.php
msgctxt "Pattern title"
msgid "Landing page for event"
msgstr "Página de captação para evento"

#: patterns/page-landing-podcast.php
msgctxt "Pattern title"
msgid "Landing page for podcast"
msgstr "Página de captação para podcast"

#: patterns/cta-heading-search.php
msgctxt "Pattern description"
msgid "Large heading with a search form for quick navigation."
msgstr "Título grande com um formulário de pesquisa para navegação rápida."

#: patterns/banner-intro-image.php:42 patterns/cta-centered-heading.php:28
#: patterns/hero-full-width-image.php:33
msgid "Learn more"
msgstr "Saiba mais"

#: patterns/event-schedule.php:65 patterns/event-schedule.php:97
#: patterns/event-schedule.php:150 patterns/event-schedule.php:182
msgctxt "Pattern placeholder text with link."
msgid "Lecture by <a href=\"#\">Prof. Fiona Presley</a>"
msgstr "Palestra da <a href=\"#\">Profa. Fiona Presley</a>"

#: patterns/banner-poster.php:51
msgid "Let’s hear them."
msgstr "Vamos ouvi-los."

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:26
msgid "Lewis Hine"
msgstr "Lewis Hine"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:30
msgid "Lewis W. Hine studied sociology before moving to New York in 1901 to work at the Ethical Culture School, where he took up photography to enhance his teaching practices"
msgstr "Lewis W. Hine estudou Sociologia antes de se mudar para Nova York em 1901 para trabalhar na Ethical Culture School, onde começou a fotografar para aprimorar suas práticas de ensino"

#: patterns/banner-intro-image.php:35
msgctxt "Sample description for banner with flower."
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "Como flores que desabrocham em lugares inesperados, cada história se desenrola com beleza e resiliência, revelando maravilhas escondidas."

#: patterns/cta-centered-heading.php:22
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "Como flores que desabrocham em lugares inesperados, cada história se desenrola com beleza e resiliência, revelando maravilhas escondidas."

#: patterns/hero-full-width-image.php:27
msgctxt "Sample hero paragraph"
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "Como flores que desabrocham em lugares inesperados, cada história se desenrola com beleza e resiliência, revelando maravilhas escondidas."

#: patterns/format-link.php
msgctxt "Pattern title"
msgid "Link format"
msgstr "Formato de link"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern title"
msgid "Link in bio heading, paragraph, links and full-height image"
msgstr "Link em título de biografia, parágrafo, links e imagem em altura total"

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern title"
msgid "Link in bio with profile, links and wide margins"
msgstr "Link em biografia com perfil, links e margens amplas"

#: patterns/page-cv-bio.php:47
msgid "LinkedIn"
msgstr "LinkedIn"

#: styles/02-noon.json styles/06-morning.json styles/07-sunrise.json
#: styles/08-midnight.json styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-6.json
#: styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Literata"
msgstr "Literata"

#: styles/typography/typography-preset-7.json
msgctxt "Style variation name"
msgid "Literata & Fira Sans"
msgstr "Literata e Fira Sans"

#: styles/typography/typography-preset-5.json
msgctxt "Style variation name"
msgid "Literata & Ysabeau Office"
msgstr "Literata e Ysabeau Office"

#: patterns/logos.php
msgctxt "Pattern title"
msgid "Logos"
msgstr "Logotipos"

#: theme.json styles/05-twilight.json
#: styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Manrope"
msgstr "Manrope"

#: patterns/cta-events-list.php:75
msgid "Mexico City, Mexico"
msgstr "Cidade do México, México"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Style variation name"
msgid "Midnight"
msgstr "Meia-noite"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Duotone name"
msgid "Midnight filter"
msgstr "Filtro de meia-noite"

#: patterns/cta-events-list.php:45 patterns/cta-events-list.php:83
#: patterns/cta-events-list.php:114 patterns/cta-events-list.php:152
#: patterns/event-3-col.php:44 patterns/event-3-col.php:68
#: patterns/event-3-col.php:92 patterns/event-rsvp.php:37
#: patterns/event-schedule.php:35 patterns/event-schedule.php:121
msgctxt "Example event date in pattern."
msgid "Mon, Jan 1"
msgstr "Seg, 1º. de jan"

#: patterns/more-posts.php
msgctxt "Pattern title"
msgid "More posts"
msgstr "Mais posts"

#: patterns/more-posts.php:18
msgid "More posts"
msgstr "Mais posts"

#: styles/06-morning.json styles/colors/06-morning.json
msgctxt "Style variation name"
msgid "Morning"
msgstr "Manhã"

#: patterns/page-cv-bio.php:31
msgctxt "Pattern placeholder text."
msgid "My name is Nora Winslow Keene, and I’m a committed public interest attorney. Living in Denver, Colorado, I’ve spent years championing the rights of underrepresented workers. A graduate of Stanford University, I played a key role in securing critical protections for agricultural laborers, ensuring better wages and access to healthcare. My work has focused on advocating for environmental justice and improving the quality of life for rural communities. Every case I take on is driven by the belief that everyone deserves dignity and fair treatment in the workplace."
msgstr "Meu nome é Nora Winslow Keene e sou uma advogada comprometida com o interesse público. Morando em Denver, Colorado, passei anos defendendo os direitos de trabalhadores sub-representados. Formada pela Universidade de Stanford, desempenhei um papel fundamental na garantia de proteções sociais para trabalhadores agrícolas, garantindo melhores salários e acesso à assistência médica. Meu trabalho se concentrou em defender a justiça ambiental e melhorar a qualidade de vida das comunidades rurais. Cada caso que assumo é motivado pela crença de que todos merecem dignidade e tratamento justo no local de trabalho."

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "a equipe do WordPress"

#. Author URI of the theme
#: style.css patterns/footer-centered.php:34 patterns/footer-columns.php:74
#: patterns/footer-newsletter.php:50 patterns/footer-social.php:36
#: patterns/footer.php:83
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://br.wordpress.org"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfive/"
msgstr "https://br.wordpress.org/themes/twentytwentyfive/"

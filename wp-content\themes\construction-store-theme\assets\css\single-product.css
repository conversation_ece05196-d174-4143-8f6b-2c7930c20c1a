/**
 * Single Product Page Styles
 */

/* Product Container Layout */
.single-product-page {
    padding: var(--space-6) 0;
}

.product-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.product-images {
    position: sticky;
    top: var(--space-4);
    height: fit-content;
}

.product-summary {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

/* Product Title */
.product-title {
    font-size: var(--font-size-3xl);
    color: var(--color-gray-900);
    margin: 0 0 var(--space-2) 0;
    line-height: var(--line-height-tight);
}

/* Product Rating */
.product-rating {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.product-rating .star-rating {
    color: var(--color-warning);
}

/* Product Price */
.product-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: var(--space-4);
}

.product-price .woocommerce-Price-amount {
    font-size: inherit;
}

.product-price del {
    color: var(--color-gray-600);
    font-size: var(--font-size-lg);
    margin-right: var(--space-2);
}

/* Product Description */
.product-short-description {
    color: var(--color-gray-700);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--space-4);
}

/* Technical Specifications */
.technical-specs {
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
    border-radius: 8px;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.technical-specs h3 {
    color: var(--color-gray-900);
    font-size: var(--font-size-lg);
    margin: 0 0 var(--space-3) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.technical-specs h3:before {
    content: "📋";
    font-size: var(--font-size-xl);
}

.specs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2);
    background: white;
    border-radius: 4px;
    border: 1px solid var(--color-gray-200);
}

.spec-item strong {
    color: var(--color-gray-800);
    font-size: var(--font-size-sm);
}

.spec-value {
    color: var(--color-gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Stock Indicator */
.stock-indicator {
    margin-bottom: var(--space-4);
}

.stock-indicator .in-stock {
    color: var(--color-success);
    font-weight: 500;
}

.stock-indicator .out-of-stock {
    color: var(--color-danger);
    font-weight: 500;
}

/* Add to Cart Form */
.product-add-to-cart {
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
    border-radius: 8px;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.product-add-to-cart .quantity {
    margin-bottom: var(--space-3);
}

.product-add-to-cart .quantity input {
    width: 80px;
    padding: var(--space-2);
    border: 1px solid var(--color-gray-300);
    border-radius: 4px;
    text-align: center;
}

.product-add-to-cart .single_add_to_cart_button {
    background: var(--color-primary);
    color: white;
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: 6px;
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 100%;
}

.product-add-to-cart .single_add_to_cart_button:hover {
    background: var(--color-primary-dark);
}

/* Product Meta */
.product-meta {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
    margin-bottom: var(--space-4);
}

.product-meta > span {
    display: block;
    margin-bottom: var(--space-1);
}

/* Shipping Information */
.shipping-info {
    background: var(--color-info);
    color: white;
    border-radius: 8px;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.shipping-info h4 {
    color: white;
    margin: 0 0 var(--space-3) 0;
    font-size: var(--font-size-lg);
}

.shipping-calculator {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.shipping-cep {
    flex: 1;
    padding: var(--space-2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.shipping-cep::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.shipping-calc-btn {
    background: white;
    color: var(--color-info);
    border: none;
    padding: var(--space-2) var(--space-4);
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.shipping-calc-btn:hover {
    background: var(--color-gray-100);
}

.shipping-calc-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.shipping-results {
    margin-top: var(--space-3);
}

.shipping-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.shipping-option {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: var(--space-3);
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--space-3);
    align-items: center;
}

.shipping-option strong {
    color: white;
}

.shipping-time {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
}

.shipping-price {
    color: white;
    font-weight: 600;
}

/* Product Tabs */
.product-tabs-section {
    margin: var(--space-8) 0;
}

.tabs-nav {
    display: flex;
    border-bottom: 2px solid var(--color-gray-200);
    margin-bottom: var(--space-6);
}

.tab-button {
    background: none;
    border: none;
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--color-gray-600);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: var(--color-primary);
}

.tab-button.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
}

.tab-content {
    min-height: 300px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Detailed Specifications */
.detailed-specs h3 {
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

.specs-table {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
}

/* Installation Guide */
.installation-guide h3 {
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

.installation-content {
    line-height: var(--line-height-relaxed);
    color: var(--color-gray-700);
}

/* Related Products */
.related-products-section,
.complementary-products-section {
    margin: var(--space-8) 0;
    padding: var(--space-6) 0;
    border-top: 1px solid var(--color-gray-200);
}

.related-products-grid,
.complementary-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-6);
}

/* Material Calculator Integration */
.material-calculator {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
    border-radius: 12px;
    padding: var(--space-6);
    margin: var(--space-6) 0;
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.2);
}

.material-calculator h4 {
    color: white;
    margin: 0 0 var(--space-4) 0;
    font-size: var(--font-size-xl);
}

.calc-input-group {
    margin-bottom: var(--space-4);
}

.calc-input-group label {
    display: block;
    color: white;
    font-weight: 500;
    margin-bottom: var(--space-2);
}

.calc-input-group input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: var(--font-size-base);
}

.calc-input-group input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.calc-result {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.calc-result p {
    margin: 0;
    color: white;
}

.calc-result small {
    color: rgba(255, 255, 255, 0.8);
}

.calc-button {
    background: white;
    color: var(--color-primary);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.calc-button:hover {
    background: var(--color-gray-100);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .product-container {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    
    .product-images {
        position: static;
    }
    
    .specs-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .single-product-page {
        padding: var(--space-4) 0;
    }
    
    .product-title {
        font-size: var(--font-size-2xl);
    }
    
    .product-price {
        font-size: var(--font-size-xl);
    }
    
    .tabs-nav {
        flex-wrap: wrap;
    }
    
    .tab-button {
        padding: var(--space-2) var(--space-4);
        font-size: var(--font-size-sm);
    }
    
    .shipping-calculator {
        flex-direction: column;
    }
    
    .shipping-option {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-1);
    }
    
    .related-products-grid,
    .complementary-products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-4);
    }
}

@media (max-width: 480px) {
    .material-calculator {
        padding: var(--space-4);
    }
    
    .technical-specs {
        padding: var(--space-3);
    }
    
    .shipping-info {
        padding: var(--space-3);
    }
    
    .product-add-to-cart {
        padding: var(--space-3);
    }
}

/* Print Styles */
@media print {
    .shipping-info,
    .material-calculator,
    .product-add-to-cart,
    .tabs-nav {
        display: none !important;
    }
    
    .product-container {
        grid-template-columns: 1fr !important;
    }
    
    .tab-panel {
        display: block !important;
    }
}
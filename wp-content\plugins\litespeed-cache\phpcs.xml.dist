<?xml version="1.0"?>
<ruleset name="LiteSpeed Cache Coding Standards">
	<description>Apply LiteSpeed Cache Coding Standards to all plugin files</description>

	<!--
	#############################################################################
	COMMAND LINE ARGUMENTS
	https://github.com/squizlabs/PHP_CodeSniffer/wiki/Annotated-Ruleset
	#############################################################################
	-->

	<!-- Only scan PHP files -->
	<arg name="extensions" value="php"/>

	<!-- Cache scan results to use for unchanged files on future scans -->
	<arg name="cache" value=".cache/phpcs.json"/>

	<!-- Set memory limit to 512M
		 Ref: https://github.com/squizlabs/PHP_CodeSniffer/wiki/Advanced-Usage#specifying-phpini-settings 
	-->
	<ini name="memory_limit" value="512M"/> 

	<!-- Remove unwanted prefix from filepaths -->
	<arg name="basepath" value="./"/>

	<!-- Check max 20 files in parallel -->
	<arg name="parallel" value="20"/>

	<!-- Show sniff codes in all reports -->
	<arg value="ps"/>

	<!--
	#############################################################################
	FILE SELECTION
	Set which files will be subject to the scans executed using this ruleset.
	#############################################################################
	-->

	<file>.</file>

	<!-- Exclude any wordpress folder in the current directory -->
	<exclude-pattern type="relative">^wordpress/*</exclude-pattern>

	<!-- Directories and third-party library exclusions -->
	<exclude-pattern>/node_modules/*</exclude-pattern>
	<exclude-pattern>/vendor/*</exclude-pattern>

	<!--
	#############################################################################
	SET UP THE RULESET
	#############################################################################
	-->
	<!-- Check PHP v7.2 and all newer versions -->
	<config name="testVersion" value="7.2-"/>

	<rule ref="PHPCompatibility">
		<!-- Exclude false positives -->
		<!-- array_key_firstFound is defined in lib/php-compatibility.func.php -->
		<exclude name="PHPCompatibility.FunctionUse.NewFunctions.array_key_firstFound" />
	</rule>

</ruleset>

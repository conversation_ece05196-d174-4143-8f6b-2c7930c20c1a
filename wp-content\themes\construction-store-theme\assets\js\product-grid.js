/**
 * Product Grid Component JavaScript
 * Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

(function() {
    'use strict';

    /**
     * Product Grid Manager
     */
    class ProductGrid {
        constructor() {
            this.gridContainer = document.querySelector('.products-grid');
            this.viewToggleButtons = document.querySelectorAll('.view-toggle');
            this.currentView = 'grid';
            this.isLoading = false;
            
            this.init();
        }

        init() {
            this.setupViewToggle();
            this.setupProductCards();
            this.setupQuickView();
            this.setupLazyLoading();
            this.enhanceProductSpecs();
        }

        /**
         * Setup view toggle functionality (grid/list)
         */
        setupViewToggle() {
            this.viewToggleButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const view = button.dataset.view;
                    this.switchView(view);
                });
            });
        }

        /**
         * Switch between grid and list view
         */
        switchView(view) {
            if (view === this.currentView) return;

            this.currentView = view;
            
            // Update button states
            this.viewToggleButtons.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === view);
            });

            // Update grid class
            if (this.gridContainer) {
                this.gridContainer.classList.toggle('list-view', view === 'list');
            }

            // Save preference
            localStorage.setItem('construction_store_view_preference', view);
        }

        /**
         * Setup product card interactions
         */
        setupProductCards() {
            const productCards = document.querySelectorAll('.product-item');
            
            productCards.forEach(card => {
                this.enhanceProductCard(card);
            });
        }

        /**
         * Enhance individual product card
         */
        enhanceProductCard(card) {
            const addToCartButton = card.querySelector('.add-to-cart-button .wp-block-button__link');
            const productImage = card.querySelector('.product-image img');
            
            // Add to cart functionality
            if (addToCartButton) {
                addToCartButton.addEventListener('click', (e) => {
                    this.handleAddToCart(e, card);
                });
            }

            // Image error handling
            if (productImage) {
                productImage.addEventListener('error', () => {
                    this.handleImageError(productImage);
                });
            }

            // Add hover effects for better UX
            this.addHoverEffects(card);
        }

        /**
         * Handle add to cart action
         */
        handleAddToCart(e, card) {
            e.preventDefault();
            
            if (this.isLoading) return;
            
            const button = e.target;
            const originalText = button.textContent;
            
            // Show loading state
            button.textContent = 'Adicionando...';
            button.disabled = true;
            card.classList.add('loading');
            
            // Get product data
            const productId = this.getProductId(card);
            const quantity = 1;
            
            // Simulate add to cart (in real implementation, this would be an AJAX call)
            setTimeout(() => {
                this.addToCartSuccess(button, originalText, card);
            }, 1000);
        }

        /**
         * Handle successful add to cart
         */
        addToCartSuccess(button, originalText, card) {
            button.textContent = 'Adicionado!';
            button.style.backgroundColor = '#28A745';
            card.classList.remove('loading');
            
            // Reset button after delay
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                button.style.backgroundColor = '';
            }, 2000);
            
            // Update cart count (if cart widget exists)
            this.updateCartCount();
        }

        /**
         * Get product ID from card
         */
        getProductId(card) {
            // Try to get from data attribute or class
            const productLink = card.querySelector('.product-title a');
            if (productLink) {
                const href = productLink.getAttribute('href');
                const matches = href.match(/product\/([^\/]+)/);
                return matches ? matches[1] : null;
            }
            return null;
        }

        /**
         * Handle image loading errors
         */
        handleImageError(img) {
            // Set placeholder image
            img.src = this.getPlaceholderImage();
            img.alt = 'Imagem não disponível';
        }

        /**
         * Get placeholder image URL
         */
        getPlaceholderImage() {
            return '/wp-content/themes/construction-store-theme/assets/images/product-placeholder.svg';
        }

        /**
         * Add hover effects to product cards
         */
        addHoverEffects(card) {
            const productImage = card.querySelector('.product-image');
            
            if (productImage) {
                card.addEventListener('mouseenter', () => {
                    this.showQuickActions(card);
                });
                
                card.addEventListener('mouseleave', () => {
                    this.hideQuickActions(card);
                });
            }
        }

        /**
         * Show quick action buttons on hover
         */
        showQuickActions(card) {
            const quickViewBtn = card.querySelector('.quick-view-button');
            if (quickViewBtn) {
                quickViewBtn.style.opacity = '1';
                quickViewBtn.style.transform = 'translateY(0)';
            }
        }

        /**
         * Hide quick action buttons
         */
        hideQuickActions(card) {
            const quickViewBtn = card.querySelector('.quick-view-button');
            if (quickViewBtn) {
                quickViewBtn.style.opacity = '0';
                quickViewBtn.style.transform = 'translateY(-10px)';
            }
        }

        /**
         * Setup quick view functionality
         */
        setupQuickView() {
            const quickViewButtons = document.querySelectorAll('.quick-view-button');
            
            quickViewButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.openQuickView(button);
                });
            });
        }

        /**
         * Open quick view modal
         */
        openQuickView(button) {
            const productCard = button.closest('.product-item');
            const productId = this.getProductId(productCard);
            
            if (productId) {
                // In a real implementation, this would load product data via AJAX
                console.log('Opening quick view for product:', productId);
                // For now, just redirect to product page
                const productLink = productCard.querySelector('.product-title a');
                if (productLink) {
                    window.location.href = productLink.href;
                }
            }
        }

        /**
         * Setup lazy loading for product images
         */
        setupLazyLoading() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            this.loadImage(img);
                            observer.unobserve(img);
                        }
                    });
                });

                const images = document.querySelectorAll('.product-image img[data-src]');
                images.forEach(img => imageObserver.observe(img));
            }
        }

        /**
         * Load image for lazy loading
         */
        loadImage(img) {
            const src = img.dataset.src;
            if (src) {
                img.src = src;
                img.removeAttribute('data-src');
                img.classList.add('loaded');
            }
        }

        /**
         * Enhance product specifications display
         */
        enhanceProductSpecs() {
            const specItems = document.querySelectorAll('.spec-item');
            
            specItems.forEach(item => {
                const specType = this.detectSpecType(item.textContent);
                if (specType) {
                    item.classList.add(specType);
                }
            });
        }

        /**
         * Detect specification type from text
         */
        detectSpecType(text) {
            const lowerText = text.toLowerCase();
            
            if (lowerText.includes('dimensão') || lowerText.includes('tamanho')) {
                return 'dimensions';
            } else if (lowerText.includes('material')) {
                return 'material';
            } else if (lowerText.includes('peso')) {
                return 'weight';
            } else if (lowerText.includes('aplicação') || lowerText.includes('uso')) {
                return 'application';
            }
            
            return null;
        }

        /**
         * Update cart count in header
         */
        updateCartCount() {
            // This would typically make an AJAX call to get updated cart count
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                const currentCount = parseInt(element.textContent) || 0;
                element.textContent = currentCount + 1;
            });
        }

        /**
         * Load saved view preference
         */
        loadViewPreference() {
            const savedView = localStorage.getItem('construction_store_view_preference');
            if (savedView && savedView !== this.currentView) {
                this.switchView(savedView);
            }
        }
    }

    /**
     * Product Specifications Manager
     */
    class ProductSpecsManager {
        constructor() {
            this.init();
        }

        init() {
            this.loadProductSpecs();
            this.setupSpecsTooltips();
        }

        /**
         * Load product specifications via AJAX
         */
        loadProductSpecs() {
            const productCards = document.querySelectorAll('.product-item');
            
            productCards.forEach(card => {
                const specsContainer = card.querySelector('.product-specs-preview');
                if (specsContainer && !specsContainer.dataset.loaded) {
                    this.loadSpecsForProduct(card, specsContainer);
                }
            });
        }

        /**
         * Load specifications for a specific product
         */
        loadSpecsForProduct(card, specsContainer) {
            const productId = this.getProductId(card);
            
            if (!productId) return;
            
            // Mark as loading
            specsContainer.dataset.loaded = 'loading';
            
            // In a real implementation, this would be an AJAX call
            // For now, we'll simulate with sample data
            setTimeout(() => {
                this.displaySpecs(specsContainer, this.getSampleSpecs());
                specsContainer.dataset.loaded = 'true';
            }, 500);
        }

        /**
         * Get sample specifications (replace with real AJAX call)
         */
        getSampleSpecs() {
            return {
                dimensions: '10 x 20 x 5 cm',
                material: 'Aço inoxidável',
                weight: '2.5 kg',
                application: 'Uso residencial'
            };
        }

        /**
         * Display specifications in the container
         */
        displaySpecs(container, specs) {
            container.innerHTML = '';
            
            Object.entries(specs).forEach(([key, value]) => {
                if (value) {
                    const specItem = document.createElement('span');
                    specItem.className = `spec-item ${key}`;
                    specItem.textContent = this.formatSpecLabel(key, value);
                    container.appendChild(specItem);
                }
            });
        }

        /**
         * Format specification label
         */
        formatSpecLabel(key, value) {
            const labels = {
                dimensions: 'Dimensões',
                material: 'Material',
                weight: 'Peso',
                application: 'Aplicação'
            };
            
            return `${labels[key] || key}: ${value}`;
        }

        /**
         * Setup tooltips for specifications
         */
        setupSpecsTooltips() {
            const specItems = document.querySelectorAll('.spec-item');
            
            specItems.forEach(item => {
                item.addEventListener('mouseenter', (e) => {
                    this.showTooltip(e.target);
                });
                
                item.addEventListener('mouseleave', (e) => {
                    this.hideTooltip(e.target);
                });
            });
        }

        /**
         * Show tooltip for specification
         */
        showTooltip(element) {
            const tooltip = this.createTooltip(element);
            if (tooltip) {
                document.body.appendChild(tooltip);
                this.positionTooltip(tooltip, element);
            }
        }

        /**
         * Create tooltip element
         */
        createTooltip(element) {
            const tooltipText = this.getTooltipText(element);
            if (!tooltipText) return null;
            
            const tooltip = document.createElement('div');
            tooltip.className = 'spec-tooltip';
            tooltip.textContent = tooltipText;
            tooltip.style.cssText = `
                position: absolute;
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 0.75rem;
                z-index: 1000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
            `;
            
            return tooltip;
        }

        /**
         * Get tooltip text for specification
         */
        getTooltipText(element) {
            const className = element.className;
            
            if (className.includes('dimensions')) {
                return 'Dimensões do produto (comprimento x largura x altura)';
            } else if (className.includes('material')) {
                return 'Material principal de fabricação';
            } else if (className.includes('weight')) {
                return 'Peso aproximado do produto';
            } else if (className.includes('application')) {
                return 'Aplicação recomendada para o produto';
            }
            
            return null;
        }

        /**
         * Position tooltip relative to element
         */
        positionTooltip(tooltip, element) {
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltipRect.width / 2)}px`;
            tooltip.style.top = `${rect.top - tooltipRect.height - 8}px`;
            
            // Fade in
            setTimeout(() => {
                tooltip.style.opacity = '1';
            }, 10);
        }

        /**
         * Hide tooltip
         */
        hideTooltip(element) {
            const tooltip = document.querySelector('.spec-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '0';
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 200);
            }
        }

        /**
         * Get product ID from card
         */
        getProductId(card) {
            const productLink = card.querySelector('.product-title a');
            if (productLink) {
                const href = productLink.getAttribute('href');
                const matches = href.match(/product\/([^\/]+)/);
                return matches ? matches[1] : null;
            }
            return null;
        }
    }

    /**
     * Initialize when DOM is ready
     */
    function initProductGrid() {
        if (document.querySelector('.products-grid')) {
            new ProductGrid();
            new ProductSpecsManager();
        }
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initProductGrid);
    } else {
        initProductGrid();
    }

})();
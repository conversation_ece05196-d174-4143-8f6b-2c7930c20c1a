<?php
/**
 * Fallback template for Construction Store Theme
 *
 * This file is used as a fallback when no specific template is found.
 * Since this is a block theme, most content will be handled by block templates.
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<main id="main" class="site-main">
    <div class="container">
        <?php
        if (have_posts()) :
            while (have_posts()) :
                the_post();
                ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                    <header class="entry-header">
                        <h1 class="entry-title"><?php the_title(); ?></h1>
                    </header>

                    <div class="entry-content">
                        <?php the_content(); ?>
                    </div>
                </article>
                <?php
            endwhile;
        else :
            ?>
            <div class="no-content">
                <h2><?php _e('Nada encontrado', 'construction-store-theme'); ?></h2>
                <p><?php _e('Parece que não conseguimos encontrar o que você está procurando. Talvez uma busca possa ajudar.', 'construction-store-theme'); ?></p>
                <?php get_search_form(); ?>
            </div>
            <?php
        endif;
        ?>
    </div>
</main>

<?php
get_footer();
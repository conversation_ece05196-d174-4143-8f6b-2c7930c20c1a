{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["Ative para desativar a abertura do carrinho reduzido ao clicar no ícone do carrinho e, em vez disso, navegar para a página de finalização da compra."], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["Navegue para a finalização da compra ao clicar no carrinho reduzido, em vez de abrir o menu suspenso."], "Only if cart has items": ["Somente se o carrinho tiver itens"], "Show Cart Item Count:": ["Mostrar Contagem de itens do carrinho:"], "Always (even if empty)": ["Sempre (mesmo que vazio)"], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["O editor não exibe o valor de contagem real, mas um espaço reservado para indicar como ele ficará no front-end."], "Product Count": ["Contagem de produtos"], "Cart Icon": ["Ícone do carrinho"], "Icon": ["Ícone"], "Behavior": ["Comportamento"], "Open drawer when adding": ["Abra a gaveta ao adicionar"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["<PERSON>uan<PERSON> a<PERSON>, a gaveta Mini-Cart oferece aos compradores acesso rápido para visualizar seus produtos selecionados e finalizar a compra."], "Edit Mini-Cart Drawer template": ["Editar modelo da gaveta do Mini-Cart"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["Alterne para abrir a gaveta do Mini-Cart quando um comprador adiciona um produto ao carrinho."], "Cart Drawer": ["Gaveta do Carrinho"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["Alterne para exibir o preço total dos produtos no carrinho. Se nenhum produto foi adicionado, o preço não será exibido."], "Display total price": ["Exibir preço total"], "Mini-Cart in cart and checkout pages": ["Minicarrinho no carrinho e páginas de checkout"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["Selecione como o minicarrinho se comporta nas páginas de carrinho e checkout. Isso pode afetar o layout do cabeçalho."], "Hide": ["Ocultar"], "Display": ["Exibição"], "Never": ["Nunca"], "Price": ["Preço"], "Remove": ["Remover"], "Settings": ["Configurações"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}
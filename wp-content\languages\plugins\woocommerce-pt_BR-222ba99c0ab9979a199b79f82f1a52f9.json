{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "\"%s\" was removed from your cart.": ["\"%s\" foi removido do seu carrinho."], "The quantity of \"%1$s\" was changed to %2$d.": ["A quantidade de \"%1$s\" foi alterada para %2$d."], "Flat rate shipping": ["Envio de taxa fixa"], "T-Shirt": ["<PERSON><PERSON><PERSON>"], "Hoodie with Pocket": ["Moletom com bolso"], "Hoodie with Logo": ["Moletom com logotipo"], "Hoodie with Zipper": ["Moletom com zíper"], "Long Sleeve Tee": ["<PERSON><PERSON>ta manga longa"], "Polo": ["<PERSON><PERSON><PERSON>"], "%s (optional)": ["%s (opcional)"], "There was an error registering the payment method with id '%s': ": ["Há um erro ao cadastrar o método de pagamento com o ID \"%s\": "], "Orange": ["<PERSON><PERSON>"], "Lightweight baseball cap": ["<PERSON><PERSON> leve de baseball"], "Cap": ["<PERSON><PERSON>"], "Yellow": ["<PERSON><PERSON>"], "Warm hat for winter": ["Chapéu quente para o inverno"], "Beanie": ["Toca"], "example product in Cart Block\u0004Beanie": ["Toca"], "example product in Cart Block\u0004Beanie with Logo": ["Toca com logo"], "Something went wrong. Please contact us to get assistance.": ["Ocorreu um problema. Entre em contato conosco para obter ajuda."], "The response is not a valid JSON response.": ["A resposta não é uma resposta JSON válida."], "Unable to get cart data from the API.": ["Não foi possível obter os dados do carrinho utilizando a API."], "Sales tax": ["Imposto de venda"], "Color": ["Cor"], "Small": ["Pequeno"], "Size": ["<PERSON><PERSON><PERSON>"], "Free shipping": ["Frete gr<PERSON><PERSON>"], "Shipping": ["Entrega"], "Fee": ["Taxa"], "Local pickup": ["Retirada no local"]}}, "comment": {"reference": "assets/client/blocks/wc-blocks-data.js"}}
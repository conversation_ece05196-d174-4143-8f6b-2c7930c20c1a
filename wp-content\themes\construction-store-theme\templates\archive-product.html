<!-- wp:template-part {"slug":"header","className":"site-header"} /-->

<!-- wp:group {"tagName":"main","className":"site-main product-archive","layout":{"type":"constrained"}} -->
<main class="wp-block-group site-main product-archive">
    
    <!-- wp:woocommerce/breadcrumbs {"className":"archive-breadcrumbs"} /-->
    
    <!-- Archive Header -->
    <!-- wp:group {"className":"archive-header","layout":{"type":"default"}} -->
    <div class="wp-block-group archive-header">
        <!-- wp:query-title {"type":"archive","className":"archive-title"} /-->
        
        <!-- wp:term-description {"className":"archive-description"} /-->
        
        <!-- wp:woocommerce/product-results-count {"className":"results-count"} /-->
    </div>
    <!-- /wp:group -->
    
    <!-- Main Content Area with Sidebar -->
    <!-- wp:group {"className":"archive-content","layout":{"type":"flex","flexWrap":"nowrap"}} -->
    <div class="wp-block-group archive-content">
        
        <!-- Product Filters Sidebar -->
        <!-- wp:group {"className":"product-filters-sidebar","layout":{"type":"default"}} -->
        <div class="wp-block-group product-filters-sidebar">
            
            <!-- wp:heading {"level":3} -->
            <h3 class="wp-block-heading">Filtrar Produtos</h3>
            <!-- /wp:heading -->
            
            <!-- Category Filter -->
            <!-- wp:group {"className":"filter-group","layout":{"type":"default"}} -->
            <div class="wp-block-group filter-group">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">Categoria</h4>
                <!-- /wp:heading -->
                
                <!-- wp:woocommerce/filter-wrapper {"filterType":"product-categories","heading":"Categorias"} -->
                <div class="wp-block-woocommerce-filter-wrapper">
                    <!-- wp:woocommerce/product-filter-categories {"className":"category-filter"} /-->
                </div>
                <!-- /wp:woocommerce/filter-wrapper -->
            </div>
            <!-- /wp:group -->
            
            <!-- Price Filter -->
            <!-- wp:group {"className":"filter-group","layout":{"type":"default"}} -->
            <div class="wp-block-group filter-group">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">Faixa de Preço</h4>
                <!-- /wp:heading -->
                
                <!-- wp:woocommerce/filter-wrapper {"filterType":"price-filter","heading":"Preço"} -->
                <div class="wp-block-woocommerce-filter-wrapper">
                    <!-- wp:woocommerce/product-filter-price {"className":"price-filter"} /-->
                </div>
                <!-- /wp:woocommerce/filter-wrapper -->
            </div>
            <!-- /wp:group -->
            
            <!-- Brand/Attribute Filter -->
            <!-- wp:group {"className":"filter-group","layout":{"type":"default"}} -->
            <div class="wp-block-group filter-group">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">Marca</h4>
                <!-- /wp:heading -->
                
                <!-- wp:html -->
                <div class="brand-filter">
                    <div class="filter-options">
                        <label data-brand="bosch"><input type="checkbox" value="bosch"> Bosch <span class="filter-count">0</span></label>
                        <label data-brand="makita"><input type="checkbox" value="makita"> Makita <span class="filter-count">0</span></label>
                        <label data-brand="dewalt"><input type="checkbox" value="dewalt"> DeWalt <span class="filter-count">0</span></label>
                        <label data-brand="vonder"><input type="checkbox" value="vonder"> Vonder <span class="filter-count">0</span></label>
                        <label data-brand="tigre"><input type="checkbox" value="tigre"> Tigre <span class="filter-count">0</span></label>
                        <label data-brand="amanco"><input type="checkbox" value="amanco"> Amanco <span class="filter-count">0</span></label>
                    </div>
                </div>
                <!-- /wp:html -->
            </div>
            <!-- /wp:group -->
            
            <!-- Stock Status Filter -->
            <!-- wp:group {"className":"filter-group","layout":{"type":"default"}} -->
            <div class="wp-block-group filter-group">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">Disponibilidade</h4>
                <!-- /wp:heading -->
                
                <!-- wp:woocommerce/filter-wrapper {"filterType":"stock-status","heading":"Estoque"} -->
                <div class="wp-block-woocommerce-filter-wrapper">
                    <!-- wp:woocommerce/product-filter-stock-status {"className":"stock-filter"} /-->
                </div>
                <!-- /wp:woocommerce/filter-wrapper -->
            </div>
            <!-- /wp:group -->
            
            <!-- Technical Specifications Filter -->
            <!-- wp:group {"className":"filter-group","layout":{"type":"default"}} -->
            <div class="wp-block-group filter-group">
                <!-- wp:heading {"level":4} -->
                <h4 class="wp-block-heading">Especificações</h4>
                <!-- /wp:heading -->
                
                <!-- wp:html -->
                <div class="specs-filter">
                    <div class="filter-section">
                        <h5>Material</h5>
                        <div class="filter-options">
                            <label><input type="checkbox" value="aco" data-type="material"> Aço</label>
                            <label><input type="checkbox" value="plastico" data-type="material"> Plástico</label>
                            <label><input type="checkbox" value="madeira" data-type="material"> Madeira</label>
                            <label><input type="checkbox" value="ceramica" data-type="material"> Cerâmica</label>
                            <label><input type="checkbox" value="aluminio" data-type="material"> Alumínio</label>
                            <label><input type="checkbox" value="ferro" data-type="material"> Ferro</label>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h5>Aplicação</h5>
                        <div class="filter-options">
                            <label><input type="checkbox" value="residencial" data-type="application"> Residencial</label>
                            <label><input type="checkbox" value="comercial" data-type="application"> Comercial</label>
                            <label><input type="checkbox" value="industrial" data-type="application"> Industrial</label>
                            <label><input type="checkbox" value="reforma" data-type="application"> Reforma</label>
                        </div>
                    </div>
                </div>
                <!-- /wp:html -->
            </div>
            <!-- /wp:group -->
            
            <!-- Clear Filters Button -->
            <!-- wp:button {"className":"clear-filters-btn"} -->
            <div class="wp-block-button clear-filters-btn">
                <a class="wp-block-button__link wp-element-button" href="#" onclick="clearAllFilters()">Limpar Filtros</a>
            </div>
            <!-- /wp:button -->
            
        </div>
        <!-- /wp:group -->
        
        <!-- Products Grid Area -->
        <!-- wp:group {"className":"products-main-area","layout":{"type":"default"}} -->
        <div class="wp-block-group products-main-area">
            
            <!-- Mobile Filters Toggle -->
            <!-- wp:html -->
            <button class="mobile-filters-toggle">
                <span class="dashicons dashicons-filter"></span>
                Filtrar Produtos
            </button>
            <!-- /wp:html -->
            
            <!-- Toolbar with sorting and view options -->
            <!-- wp:group {"className":"products-toolbar","layout":{"type":"flex","justifyContent":"space-between"}} -->
            <div class="wp-block-group products-toolbar">
                
                <!-- wp:woocommerce/catalog-sorting {"className":"products-sorting"} /-->
                
                <!-- wp:html -->
                <div class="view-options">
                    <button class="view-toggle active" data-view="grid" title="Visualização em Grade">
                        <span class="dashicons dashicons-grid-view"></span>
                    </button>
                    <button class="view-toggle" data-view="list" title="Visualização em Lista">
                        <span class="dashicons dashicons-list-view"></span>
                    </button>
                </div>
                <!-- /wp:html -->
                
            </div>
            <!-- /wp:group -->
            
            <!-- Products Collection -->
            <!-- wp:woocommerce/product-collection {"query":{"perPage":12,"pages":0,"offset":0,"postType":"product","order":"desc","orderBy":"date","search":"","exclude":[],"inherit":true,"taxQuery":{},"isProductCollectionBlock":true,"featured":false,"woocommerceOnSale":false,"woocommerceStockStatus":["instock","outofstock","onbackorder"],"woocommerceAttributes":[],"woocommerceHandPickedProducts":[]},"tagName":"div","displayLayout":{"type":"flex","columns":3},"className":"products-grid"} -->
            <div class="wp-block-woocommerce-product-collection products-grid">
                
                <!-- wp:woocommerce/product-template {"className":"product-item"} -->
                    
                    <!-- wp:group {"className":"product-image","layout":{"type":"default"}} -->
                    <div class="wp-block-group product-image">
                        <!-- wp:woocommerce/product-image {"saleBadgeAlign":"left","showSaleBadge":false} /-->
                        
                        <!-- Product badges will be added via PHP hook -->
                        
                        <!-- Quick view button will be added via PHP hook -->
                    </div>
                    <!-- /wp:group -->
                    
                    <!-- wp:group {"className":"product-info","layout":{"type":"default"}} -->
                    <div class="wp-block-group product-info">
                        
                        <!-- wp:post-title {"level":3,"isLink":true,"className":"product-title"} /-->
                        
                        <!-- wp:woocommerce/product-rating {"className":"product-rating"} /-->
                        
                        <!-- Technical specifications will be added via PHP hook -->
                        
                        <!-- wp:woocommerce/product-price {"className":"product-price"} /-->
                        
                        <!-- wp:woocommerce/product-stock-indicator {"className":"stock-status"} /-->
                        
                        <!-- wp:woocommerce/product-button {"className":"add-to-cart-button"} /-->
                        
                    </div>
                    <!-- /wp:group -->
                    
                <!-- /wp:woocommerce/product-template -->
                
                <!-- wp:query-no-results -->
                    <!-- wp:paragraph {"textAlign":"center"} -->
                    <p class="has-text-align-center">Nenhum produto encontrado com os filtros selecionados.</p>
                    <!-- /wp:paragraph -->
                    
                    <!-- wp:button {"className":"clear-filters-suggestion"} -->
                    <div class="wp-block-button clear-filters-suggestion">
                        <a class="wp-block-button__link wp-element-button" href="#" onclick="clearAllFilters()">Limpar Filtros e Ver Todos</a>
                    </div>
                    <!-- /wp:button -->
                <!-- /wp:query-no-results -->
                
            </div>
            <!-- /wp:woocommerce/product-collection -->
            
            <!-- Pagination -->
            <!-- wp:query-pagination {"className":"products-pagination"} -->
                <!-- wp:query-pagination-previous {"label":"Anterior"} /-->
                <!-- wp:query-pagination-numbers /-->
                <!-- wp:query-pagination-next {"label":"Próximo"} /-->
            <!-- /wp:query-pagination -->
            
        </div>
        <!-- /wp:group -->
        
    </div>
    <!-- /wp:group -->
    
    <!-- Category Information Section -->
    <!-- wp:group {"className":"category-info-section","layout":{"type":"constrained"}} -->
    <div class="wp-block-group category-info-section">
        
        <!-- wp:heading {"textAlign":"center"} -->
        <h2 class="wp-block-heading has-text-align-center">Sobre Esta Categoria</h2>
        <!-- /wp:heading -->
        
        <!-- wp:columns -->
        <div class="wp-block-columns">
            
            <!-- wp:column -->
            <div class="wp-block-column">
                <!-- wp:heading {"level":3} -->
                <h3 class="wp-block-heading">🛠️ Qualidade Garantida</h3>
                <!-- /wp:heading -->
                
                <!-- wp:paragraph -->
                <p>Todos os produtos são selecionados por nossos especialistas e atendem aos mais altos padrões de qualidade.</p>
                <!-- /wp:paragraph -->
            </div>
            <!-- /wp:column -->
            
            <!-- wp:column -->
            <div class="wp-block-column">
                <!-- wp:heading {"level":3} -->
                <h3 class="wp-block-heading">📋 Suporte Técnico</h3>
                <!-- /wp:heading -->
                
                <!-- wp:paragraph -->
                <p>Nossa equipe técnica está disponível para ajudar na escolha dos produtos ideais para seu projeto.</p>
                <!-- /wp:paragraph -->
            </div>
            <!-- /wp:column -->
            
            <!-- wp:column -->
            <div class="wp-block-column">
                <!-- wp:heading {"level":3} -->
                <h3 class="wp-block-heading">🚚 Entrega Especializada</h3>
                <!-- /wp:heading -->
                
                <!-- wp:paragraph -->
                <p>Entrega cuidadosa e no prazo, com opções especiais para materiais pesados e frágeis.</p>
                <!-- /wp:paragraph -->
            </div>
            <!-- /wp:column -->
            
        </div>
        <!-- /wp:columns -->
        
    </div>
    <!-- /wp:group -->
    
</main>
<!-- /wp:group -->

<!-- wp:template-part {"slug":"footer","className":"site-footer"} /-->
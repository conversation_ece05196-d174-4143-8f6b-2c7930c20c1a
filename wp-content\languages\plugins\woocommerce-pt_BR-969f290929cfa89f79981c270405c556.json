{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Product counts": ["Quantidade de Produtos"], "Show results for any of the attributes selected (displayed products don’t have to have them all).": ["Mostrar resultados para qualquer dos atributos selecionados (produtos mostrados poderão não conter todos os atributos)."], "Enable customers to filter the product collection by selecting one or more %s attributes.": ["Permite aos clientes filtrar coleções de produtos selecionando um ou mais %s atributos"], "Empty filter options": ["Limpe as opções do filtro"], "Name, Z to A": ["Nome: Z a A"], "Name, A to Z": ["Nome: A a Z"], "Red": ["Vermelho"], "Show results for <b>all</b> selected attributes. Displayed products must contain <b>all of them</b> to appear in the results.": ["Mostrar resultados para <b>todos</b> os atributos selecionados. Produtos mostrados precisam conter <b> todos os atributos</b> para aparecer nos resultados."], "Logic": ["Lógica"], "Determine the order of filter options.": ["Determine a ordem das opções de filtros."], "Sort order": ["Ordem de classificação"], "Choose the attribute to show in this filter.": ["Escolha o atributo para mostrar nesse filtro."], "Least results first": ["Menos resultados primeiro"], "Most results first": ["<PERSON><PERSON> resultados primeiro"], "Green": ["Verde"], "Gray": ["Cinza"], "There are no products with the selected attributes.": ["Não existem produtos com os atributos selecionados."], "Please select an attribute to use this filter!": ["Selecione um atributo para usar este filtro!"], "Select an option": ["Selecione uma opção"], "Yellow": ["<PERSON><PERSON>"], "Blue": ["Azul"], "Attributes are needed for filtering your products. You haven't created any attributes yet.": ["Atributos são necessários para filtrar seus produtos. Voc<PERSON> ainda não criou nenhum atributo."], "Attribute": ["Atributo"], "Display": ["Exibição"], "Any": ["<PERSON>ual<PERSON>"], "All": ["<PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/product-filter-attribute.js"}}
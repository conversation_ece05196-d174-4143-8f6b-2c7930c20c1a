/**
 * Main theme JavaScript for Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        
        // Initialize mobile menu
        initMobileMenu();
        
        // Initialize quantity calculator
        initQuantityCalculator();
        
        // Initialize product filters
        initProductFilters();
        
        // Initialize smooth scrolling
        initSmoothScrolling();
    });

    /**
     * Initialize mobile menu functionality
     */
    function initMobileMenu() {
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenuToggle && mobileMenu) {
            mobileMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                mobileMenu.classList.toggle('is-open');
                mobileMenuToggle.classList.toggle('is-active');
                
                // Toggle aria-expanded
                const isExpanded = mobileMenuToggle.getAttribute('aria-expanded') === 'true';
                mobileMenuToggle.setAttribute('aria-expanded', !isExpanded);
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!mobileMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    mobileMenu.classList.remove('is-open');
                    mobileMenuToggle.classList.remove('is-active');
                    mobileMenuToggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    }

    /**
     * Initialize quantity calculator for construction materials
     */
    function initQuantityCalculator() {
        const calculateButton = document.getElementById('calculate-quantity');
        const areaInput = document.getElementById('area-input');
        const resultDiv = document.querySelector('.calculation-result');
        
        if (calculateButton && areaInput && resultDiv) {
            calculateButton.addEventListener('click', function() {
                const area = parseFloat(areaInput.value);
                
                if (!area || area <= 0) {
                    showCalculationError('Por favor, insira uma área válida maior que zero.');
                    return;
                }
                
                if (area > 10000) {
                    showCalculationError('Para áreas muito grandes, consulte nossos especialistas.');
                    return;
                }
                
                // Get coverage data from product (this would be set via PHP)
                const coverage = parseFloat(calculateButton.dataset.coverage) || 10; // Default 10m² per unit
                const productName = calculateButton.dataset.productName || 'produto';
                
                const baseQuantity = Math.ceil(area / coverage);
                const recommendedQuantity = Math.ceil(baseQuantity * 1.1); // 10% safety margin
                const wasteQuantity = recommendedQuantity - baseQuantity;
                
                showCalculationResult({
                    area: area,
                    baseQuantity: baseQuantity,
                    recommendedQuantity: recommendedQuantity,
                    wasteQuantity: wasteQuantity,
                    productName: productName,
                    coverage: coverage
                });
            });
            
            // Calculate on Enter key
            areaInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    calculateButton.click();
                }
            });
        }
    }

    /**
     * Show calculation result
     */
    function showCalculationResult(data) {
        const resultDiv = document.querySelector('.calculation-result');
        
        const html = `
            <h5>Resultado do Cálculo</h5>
            <div class="calculation-details">
                <p><strong>Área:</strong> ${data.area} m²</p>
                <p><strong>Quantidade base:</strong> ${data.baseQuantity} unidades</p>
                <p><strong>Quantidade recomendada:</strong> ${data.recommendedQuantity} unidades</p>
                <p><strong>Margem de segurança:</strong> ${data.wasteQuantity} unidades (10%)</p>
                <p><small>Baseado em cobertura de ${data.coverage} m² por unidade</small></p>
            </div>
            <button type="button" class="button add-calculated-to-cart" data-quantity="${data.recommendedQuantity}">
                Adicionar ${data.recommendedQuantity} unidades ao carrinho
            </button>
        `;
        
        resultDiv.innerHTML = html;
        resultDiv.style.display = 'block';
        
        // Add event listener to the new button
        const addToCartButton = resultDiv.querySelector('.add-calculated-to-cart');
        if (addToCartButton) {
            addToCartButton.addEventListener('click', function() {
                const quantity = parseInt(this.dataset.quantity);
                addCalculatedQuantityToCart(quantity);
            });
        }
    }

    /**
     * Show calculation error
     */
    function showCalculationError(message) {
        const resultDiv = document.querySelector('.calculation-result');
        
        resultDiv.innerHTML = `
            <div class="calculation-error" style="color: var(--wp--preset--color--danger);">
                <strong>Erro:</strong> ${message}
            </div>
        `;
        
        resultDiv.style.display = 'block';
    }

    /**
     * Add calculated quantity to cart
     */
    function addCalculatedQuantityToCart(quantity) {
        const quantityInput = document.querySelector('input[name="quantity"]');
        const addToCartForm = document.querySelector('form.cart');
        
        if (quantityInput && addToCartForm) {
            quantityInput.value = quantity;
            
            // Trigger the add to cart
            const addToCartButton = addToCartForm.querySelector('button[type="submit"]');
            if (addToCartButton) {
                addToCartButton.click();
            }
        }
    }

    /**
     * Initialize product filters
     */
    function initProductFilters() {
        const filterForm = document.querySelector('.product-filters-form');
        const filterInputs = document.querySelectorAll('.product-filter-input');
        
        if (filterForm && filterInputs.length > 0) {
            filterInputs.forEach(function(input) {
                input.addEventListener('change', function() {
                    // Add loading state
                    filterForm.classList.add('is-loading');
                    
                    // Submit form after short delay to allow for multiple quick changes
                    clearTimeout(window.filterTimeout);
                    window.filterTimeout = setTimeout(function() {
                        filterForm.submit();
                    }, 300);
                });
            });
        }
        
        // Price range slider
        const priceSlider = document.querySelector('.price-slider');
        const priceDisplay = document.querySelector('.price-display');
        
        if (priceSlider && priceDisplay) {
            priceSlider.addEventListener('input', function() {
                const value = this.value;
                const max = this.max;
                priceDisplay.textContent = `R$ 0 - R$ ${value}`;
                
                // Update filter
                clearTimeout(window.priceTimeout);
                window.priceTimeout = setTimeout(function() {
                    // Trigger filter update
                    const event = new Event('change');
                    priceSlider.dispatchEvent(event);
                }, 500);
            });
        }
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        
        anchorLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                const target = document.querySelector(href);
                
                if (target) {
                    e.preventDefault();
                    
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Utility function to show notifications
     */
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: var(--wp--preset--color--${type === 'error' ? 'danger' : 'success'});
            color: var(--wp--preset--color--white);
            border-radius: 4px;
            z-index: 9999;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(function() {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 5 seconds
        setTimeout(function() {
            notification.style.transform = 'translateX(100%)';
            setTimeout(function() {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    // Make utility functions available globally
    window.constructionStore = window.constructionStore || {};
    window.constructionStore.showNotification = showNotification;

})();
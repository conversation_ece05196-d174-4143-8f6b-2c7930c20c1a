<?php
/**
 * Construction Store Theme functions and definitions
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function construction_store_theme_setup() {
    // Add theme support for various WordPress features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('automatic-feed-links');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Add support for wide and full alignment
    add_theme_support('align-wide');
    
    // Add support for block templates
    add_theme_support('block-templates');
    
    // Add support for appearance tools
    add_theme_support('appearance-tools');
    
    // Add support for custom line height
    add_theme_support('custom-line-height');
    
    // Add support for custom spacing
    add_theme_support('custom-spacing');
    
    // Add support for custom units
    add_theme_support('custom-units');
    
    // Load theme textdomain for translations
    load_theme_textdomain('construction-store-theme', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'construction_store_theme_setup');

/**
 * WooCommerce support and customizations
 */
function construction_store_woocommerce_support() {
    // Add WooCommerce support
    add_theme_support('woocommerce');
    
    // Add support for WooCommerce features
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
    
    // Remove WooCommerce default styles (we'll use our own)
    add_filter('woocommerce_enqueue_styles', '__return_empty_array');
}
add_action('after_setup_theme', 'construction_store_woocommerce_support');

/**
 * Enqueue theme styles and scripts
 */
function construction_store_enqueue_assets() {
    // Theme version for cache busting
    $theme_version = wp_get_theme()->get('Version');
    
    // Enqueue main theme stylesheet
    wp_enqueue_style(
        'construction-store-style',
        get_stylesheet_uri(),
        array(),
        $theme_version
    );
    
    // Enqueue WooCommerce styles if WooCommerce is active
    if (class_exists('WooCommerce')) {
        wp_enqueue_style(
            'construction-store-woocommerce',
            get_template_directory_uri() . '/assets/css/woocommerce.css',
            array('construction-store-style'),
            $theme_version
        );
        
        // Enqueue product grid component styles
        wp_enqueue_style(
            'construction-store-product-grid',
            get_template_directory_uri() . '/assets/css/components/product-grid.css',
            array('construction-store-woocommerce'),
            $theme_version
        );
        
        // Enqueue product filters component styles
        wp_enqueue_style(
            'construction-store-product-filters',
            get_template_directory_uri() . '/assets/css/components/product-filters.css',
            array('construction-store-product-grid'),
            $theme_version
        );
        
        // Enqueue product grid JavaScript
        wp_enqueue_script(
            'construction-store-product-grid',
            get_template_directory_uri() . '/assets/js/product-grid.js',
            array(),
            $theme_version,
            true
        );
        
        // Enqueue product filters JavaScript
        wp_enqueue_script(
            'construction-store-product-filters',
            get_template_directory_uri() . '/assets/js/product-filters.js',
            array('construction-store-product-grid'),
            $theme_version,
            true
        );
        
        // Enqueue material calculator styles
        wp_enqueue_style(
            'construction-store-calculator',
            get_template_directory_uri() . '/assets/css/components/material-calculator.css',
            array('construction-store-product-filters'),
            $theme_version
        );
        
        // Enqueue single product styles on product pages
        if (is_product()) {
            wp_enqueue_style(
                'construction-store-single-product',
                get_template_directory_uri() . '/assets/css/single-product.css',
                array('construction-store-calculator'),
                $theme_version
            );
        }
        
        // Enqueue material calculator JavaScript
        wp_enqueue_script(
            'construction-store-calculator',
            get_template_directory_uri() . '/assets/js/material-calculator.js',
            array('construction-store-product-filters'),
            $theme_version,
            true
        );
        
        // Enqueue single product JavaScript on product pages
        if (is_product()) {
            wp_enqueue_script(
                'construction-store-single-product',
                get_template_directory_uri() . '/assets/js/single-product.js',
                array('construction-store-calculator'),
                $theme_version,
                true
            );
        }
    }
    
    // Enqueue theme JavaScript
    wp_enqueue_script(
        'construction-store-script',
        get_template_directory_uri() . '/assets/js/theme.js',
        array(),
        $theme_version,
        true
    );
    
    // Localize script for AJAX and translations
    wp_localize_script('construction-store-script', 'constructionStore', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('construction_store_nonce'),
        'strings' => array(
            'loading' => __('Carregando...', 'construction-store-theme'),
            'error' => __('Erro ao carregar dados', 'construction-store-theme'),
            'addToCart' => __('Adicionar ao Carrinho', 'construction-store-theme'),
            'calculate' => __('Calcular', 'construction-store-theme')
        )
    ));
}
add_action('wp_enqueue_scripts', 'construction_store_enqueue_assets');

/**
 * Add custom body classes
 */
function construction_store_body_classes($classes) {
    // Add class if WooCommerce is active
    if (class_exists('WooCommerce')) {
        $classes[] = 'woocommerce-active';
    }
    
    // Add class for construction theme
    $classes[] = 'construction-store-theme';
    
    return $classes;
}
add_filter('body_class', 'construction_store_body_classes');

/**
 * Register navigation menus
 */
function construction_store_register_menus() {
    register_nav_menus(array(
        'primary' => __('Menu Principal', 'construction-store-theme'),
        'categories' => __('Menu de Categorias', 'construction-store-theme'),
        'footer' => __('Menu do Rodapé', 'construction-store-theme'),
    ));
}
add_action('init', 'construction_store_register_menus');

/**
 * Register widget areas
 */
function construction_store_register_sidebars() {
    // Product filters sidebar
    register_sidebar(array(
        'name' => __('Filtros de Produtos', 'construction-store-theme'),
        'id' => 'product-filters',
        'description' => __('Área para widgets de filtros de produtos', 'construction-store-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
    
    // Footer widgets
    for ($i = 1; $i <= 4; $i++) {
        register_sidebar(array(
            'name' => sprintf(__('Rodapé %d', 'construction-store-theme'), $i),
            'id' => 'footer-' . $i,
            'description' => sprintf(__('Área de widgets do rodapé %d', 'construction-store-theme'), $i),
            'before_widget' => '<div id="%1$s" class="widget %2$s">',
            'after_widget' => '</div>',
            'before_title' => '<h3 class="widget-title">',
            'after_title' => '</h3>',
        ));
    }
}
add_action('widgets_init', 'construction_store_register_sidebars');

/**
 * Custom excerpt length for product descriptions
 */
function construction_store_excerpt_length($length) {
    if (is_shop() || is_product_category() || is_product_tag()) {
        return 20;
    }
    return $length;
}
add_filter('excerpt_length', 'construction_store_excerpt_length');

/**
 * Add custom image sizes
 */
function construction_store_image_sizes() {
    // Product gallery thumbnail
    add_image_size('product-gallery-thumb', 150, 150, true);
    
    // Product category image
    add_image_size('category-thumb', 300, 200, true);
    
    // Hero banner image
    add_image_size('hero-banner', 1920, 600, true);
    
    // Featured product image
    add_image_size('featured-product', 400, 300, true);
}
add_action('after_setup_theme', 'construction_store_image_sizes');

/**
 * Disable WordPress admin bar for non-admin users
 */
function construction_store_disable_admin_bar() {
    if (!current_user_can('administrator') && !is_admin()) {
        show_admin_bar(false);
    }
}
add_action('after_setup_theme', 'construction_store_disable_admin_bar');

/**
 * AJAX handler for product filtering
 */
function construction_store_filter_products() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'construction_store_nonce')) {
        wp_die('Security check failed');
    }
    
    // Get filter parameters
    $categories = json_decode(stripslashes($_POST['categories']), true) ?: array();
    $brands = json_decode(stripslashes($_POST['brands']), true) ?: array();
    $price_min = intval($_POST['price_min']);
    $price_max = intval($_POST['price_max']);
    $stock = json_decode(stripslashes($_POST['stock']), true) ?: array();
    $specs = json_decode(stripslashes($_POST['specs']), true) ?: array();
    $page = intval($_POST['page']) ?: 1;
    
    // Build WooCommerce query
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => 12,
        'paged' => $page,
        'post_status' => 'publish',
        'meta_query' => array(),
        'tax_query' => array()
    );
    
    // Add category filter
    if (!empty($categories)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'product_cat',
            'field' => 'slug',
            'terms' => $categories
        );
    }
    
    // Add brand filter (assuming brands are stored as product attributes)
    if (!empty($brands)) {
        $args['meta_query'][] = array(
            'key' => '_construction_brand',
            'value' => $brands,
            'compare' => 'IN'
        );
    }
    
    // Add price filter
    if ($price_max > 0) {
        $args['meta_query'][] = array(
            'key' => '_price',
            'value' => array($price_min, $price_max),
            'type' => 'NUMERIC',
            'compare' => 'BETWEEN'
        );
    }
    
    // Add stock filter
    if (!empty($stock)) {
        if (in_array('instock', $stock)) {
            $args['meta_query'][] = array(
                'key' => '_stock_status',
                'value' => 'instock'
            );
        }
    }
    
    // Add specifications filters
    if (!empty($specs)) {
        foreach ($specs as $spec_type => $spec_values) {
            if (!empty($spec_values)) {
                $args['meta_query'][] = array(
                    'key' => '_construction_' . $spec_type,
                    'value' => $spec_values,
                    'compare' => 'IN'
                );
            }
        }
    }
    
    // Execute query
    $query = new WP_Query($args);
    
    // Generate HTML
    ob_start();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            wc_get_template_part('content', 'product');
        }
    } else {
        echo '<div class="no-products-found">';
        echo '<h3>Nenhum produto encontrado</h3>';
        echo '<p>Tente ajustar os filtros para encontrar produtos.</p>';
        echo '</div>';
    }
    $html = ob_get_clean();
    
    // Generate pagination
    $pagination = paginate_links(array(
        'total' => $query->max_num_pages,
        'current' => $page,
        'format' => '?paged=%#%',
        'prev_text' => 'Anterior',
        'next_text' => 'Próximo'
    ));
    
    // Get filter counts
    $filter_counts = construction_store_get_filter_counts($args);
    
    wp_reset_postdata();
    
    wp_send_json_success(array(
        'html' => $html,
        'total' => $query->found_posts,
        'pagination' => $pagination,
        'filter_counts' => $filter_counts
    ));
}
add_action('wp_ajax_filter_products', 'construction_store_filter_products');
add_action('wp_ajax_nopriv_filter_products', 'construction_store_filter_products');

/**
 * AJAX handler for getting filter counts
 */
function construction_store_get_filter_counts_ajax() {
    if (!wp_verify_nonce($_POST['nonce'], 'construction_store_nonce')) {
        wp_die('Security check failed');
    }
    
    $counts = construction_store_get_filter_counts();
    wp_send_json_success($counts);
}
add_action('wp_ajax_get_filter_counts', 'construction_store_get_filter_counts_ajax');
add_action('wp_ajax_nopriv_get_filter_counts', 'construction_store_get_filter_counts_ajax');

/**
 * Get filter counts for categories, brands, etc.
 */
function construction_store_get_filter_counts($base_args = array()) {
    $counts = array(
        'categories' => array(),
        'brands' => array(),
        'specs' => array()
    );
    
    // Get category counts
    $categories = get_terms(array(
        'taxonomy' => 'product_cat',
        'hide_empty' => true
    ));
    
    foreach ($categories as $category) {
        $counts['categories'][$category->slug] = $category->count;
    }
    
    // Get brand counts
    $brands = array('bosch', 'makita', 'dewalt', 'vonder', 'tigre', 'amanco');
    foreach ($brands as $brand) {
        $brand_query = new WP_Query(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'meta_query' => array(
                array(
                    'key' => '_construction_brand',
                    'value' => $brand,
                    'compare' => 'LIKE'
                )
            )
        ));
        $counts['brands'][$brand] = $brand_query->found_posts;
        wp_reset_postdata();
    }
    
    return $counts;
}

/**
 * Material Calculator Shortcode
 */
function construction_store_calculator_shortcode($atts) {
    $atts = shortcode_atts(array(
        'product_type' => '',
        'coverage' => '',
        'title' => 'Calculadora de Materiais'
    ), $atts);
    
    ob_start();
    ?>
    <div class="material-calculator-wrapper">
        <div class="material-calculator" id="materialCalculator">
            <div class="calculator-header">
                <h3><?php echo esc_html($atts['title']); ?></h3>
                <p>Calcule a quantidade necessária para seu projeto</p>
            </div>
            
            <div class="calculator-form">
                <div class="form-group">
                    <label for="productType">Tipo de Material:</label>
                    <select id="productType" class="form-control">
                        <option value="">Selecione o material</option>
                        <option value="paint" <?php selected($atts['product_type'], 'paint'); ?>>Tinta</option>
                        <option value="cement" <?php selected($atts['product_type'], 'cement'); ?>>Cimento</option>
                        <option value="flooring" <?php selected($atts['product_type'], 'flooring'); ?>>Piso</option>
                        <option value="tiles" <?php selected($atts['product_type'], 'tiles'); ?>>Azulejo</option>
                        <option value="mortar" <?php selected($atts['product_type'], 'mortar'); ?>>Argamassa</option>
                    </select>
                </div>

                <div class="calculation-method">
                    <div class="method-tabs">
                        <button type="button" class="tab-button active" data-method="single">Área Única</button>
                        <button type="button" class="tab-button" data-method="multiple">Múltiplas Áreas</button>
                    </div>

                    <div class="method-content" id="singleAreaMethod">
                        <div class="form-group">
                            <label for="singleArea">Área Total (m²):</label>
                            <input type="number" id="singleArea" class="form-control" min="0" step="0.01" placeholder="Ex: 25.5">
                        </div>
                    </div>

                    <div class="method-content hidden" id="multipleAreasMethod">
                        <div class="areas-container" id="areasContainer">
                            <div class="area-input">
                                <input type="text" placeholder="Nome do ambiente" class="area-name">
                                <input type="number" placeholder="Área (m²)" class="area-value" min="0" step="0.01">
                                <button type="button" class="remove-area">×</button>
                            </div>
                        </div>
                        <button type="button" id="addArea" class="btn-secondary">+ Adicionar Ambiente</button>
                    </div>
                </div>

                <div class="advanced-options">
                    <button type="button" class="toggle-advanced">Opções Avançadas</button>
                    <div class="advanced-content hidden">
                        <div class="form-group">
                            <label for="customCoverage">Rendimento Personalizado:</label>
                            <input type="number" id="customCoverage" class="form-control" min="0" step="0.1" placeholder="m² por unidade" value="<?php echo esc_attr($atts['coverage']); ?>">
                        </div>
                        <div class="form-group">
                            <label for="customSafetyMargin">Margem de Segurança (%):</label>
                            <input type="number" id="customSafetyMargin" class="form-control" min="0" max="50" step="1" placeholder="10">
                        </div>
                    </div>
                </div>

                <button type="button" id="calculateButton" class="btn-primary">Calcular Quantidade</button>
            </div>

            <div class="calculator-results hidden" id="calculatorResults">
                <div class="results-header">
                    <h4>Resultado do Cálculo</h4>
                </div>
                <div class="results-content" id="resultsContent">
                    <!-- Results will be populated here -->
                </div>
                <div class="results-actions">
                    <button type="button" id="addToCartButton" class="btn-primary">Adicionar ao Carrinho</button>
                    <button type="button" id="newCalculationButton" class="btn-secondary">Nova Calculação</button>
                </div>
            </div>

            <div class="calculator-errors hidden" id="calculatorErrors">
                <div class="error-content" id="errorContent">
                    <!-- Errors will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (window.materialCalculator) {
            window.materialCalculator.attachEventListeners();
        }
    });
    </script>
    <?php
    return ob_get_clean();
}
add_shortcode('material_calculator', 'construction_store_calculator_shortcode');

/**
 * Include additional theme files
 */
require_once get_template_directory() . '/inc/woocommerce-customizations.php';
require_once get_template_directory() . '/inc/theme-customizer.php';
require_once get_template_directory() . '/inc/block-patterns.php';
{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Cart Shortcode": ["Shortcode de carrinho"], "Checkout Cart": ["Carrinho de finalização de compra"], "Classic Checkout": ["Finalização de compra clássica"], "Renders the classic checkout shortcode.": ["Renderiza o shortcode de finalização de compra clássica."], "Classic Cart": ["Carrinho clássico"], "Classic Shortcode Placeholder": ["Especialista clássico de shortcode"], "Classic shortcode transformed to blocks.": ["Código de short clássico transformado em blocos."], "Renders the classic cart shortcode.": ["Renderiza o código de atalho clássico do carrinho."], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["Este bloco renderizará o código de atalho clássico do carrinho. opcionalmente, você pode transformá -lo em blocos para obter mais controle sobre a experiência do carrinho."], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["Este bloco renderizará o código de curta de checkout clássico. opcionalmente, você pode transformá -lo em blocos para obter mais controle sobre a experiência de check -out."], "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["Você pode aprender mais sobre os benefícios da mudança para blocos, compatibilidade com extensões e como voltar aos códigos de short <a> em nossa documentação </a>."], "Transform into blocks": ["Transformar em blocos"], "Undo": ["<PERSON><PERSON><PERSON>"], "Learn more": ["Aprenda mais"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}
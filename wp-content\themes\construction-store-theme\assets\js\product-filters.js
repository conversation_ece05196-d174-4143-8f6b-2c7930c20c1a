/**
 * Product Filters JavaScript
 * Construction Store Theme
 *
 * @package Construction_Store_Theme
 * @since 1.0.0
 */

(function() {
    'use strict';

    /**
     * Product Filters Manager
     */
    class ProductFilters {
        constructor() {
            this.filtersContainer = document.querySelector('.product-filters-sidebar');
            this.productsContainer = document.querySelector('.products-grid');
            this.mobileToggle = document.querySelector('.mobile-filters-toggle');
            this.clearFiltersBtn = document.querySelector('.clear-filters-btn a');
            this.priceSlider = document.querySelector('.price-slider');
            this.priceDisplay = document.querySelector('.price-display');
            
            this.filters = {
                categories: [],
                price: { min: 0, max: 1000 },
                brands: [],
                stock: [],
                specs: {
                    material: [],
                    application: []
                }
            };
            
            this.isLoading = false;
            this.currentPage = 1;
            
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.setupMobileFilters();
            this.setupPriceFilter();
            this.loadFilterCounts();
            this.restoreFiltersFromURL();
        }

        /**
         * Setup event listeners
         */
        setupEventListeners() {
            // Category filters
            const categoryCheckboxes = document.querySelectorAll('.category-filter input[type="checkbox"]');
            categoryCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    this.handleCategoryFilter(e);
                });
            });

            // Brand filters
            const brandCheckboxes = document.querySelectorAll('.brand-filter input[type="checkbox"]');
            brandCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    this.handleBrandFilter(e);
                });
            });

            // Stock status filters
            const stockCheckboxes = document.querySelectorAll('.stock-filter input[type="checkbox"]');
            stockCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    this.handleStockFilter(e);
                });
            });

            // Specifications filters
            const specsCheckboxes = document.querySelectorAll('.specs-filter input[type="checkbox"]');
            specsCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    this.handleSpecsFilter(e);
                });
            });

            // Clear filters button
            if (this.clearFiltersBtn) {
                this.clearFiltersBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.clearAllFilters();
                });
            }

            // Debounced filter application
            this.debouncedApplyFilters = this.debounce(this.applyFilters.bind(this), 300);
        }

        /**
         * Handle category filter change
         */
        handleCategoryFilter(e) {
            const value = e.target.value;
            const isChecked = e.target.checked;

            if (isChecked) {
                this.filters.categories.push(value);
            } else {
                this.filters.categories = this.filters.categories.filter(cat => cat !== value);
            }

            this.updateFilterUI(e.target);
            this.debouncedApplyFilters();
        }

        /**
         * Handle brand filter change
         */
        handleBrandFilter(e) {
            const value = e.target.value;
            const isChecked = e.target.checked;

            if (isChecked) {
                this.filters.brands.push(value);
            } else {
                this.filters.brands = this.filters.brands.filter(brand => brand !== value);
            }

            this.updateFilterUI(e.target);
            this.debouncedApplyFilters();
        }

        /**
         * Handle stock status filter change
         */
        handleStockFilter(e) {
            const value = e.target.value;
            const isChecked = e.target.checked;

            if (isChecked) {
                this.filters.stock.push(value);
            } else {
                this.filters.stock = this.filters.stock.filter(stock => stock !== value);
            }

            this.updateFilterUI(e.target);
            this.debouncedApplyFilters();
        }

        /**
         * Handle specifications filter change
         */
        handleSpecsFilter(e) {
            const value = e.target.value;
            const type = e.target.dataset.type; // material, application, etc.
            const isChecked = e.target.checked;

            if (!this.filters.specs[type]) {
                this.filters.specs[type] = [];
            }

            if (isChecked) {
                this.filters.specs[type].push(value);
            } else {
                this.filters.specs[type] = this.filters.specs[type].filter(spec => spec !== value);
            }

            this.updateFilterUI(e.target);
            this.debouncedApplyFilters();
        }

        /**
         * Setup price filter
         */
        setupPriceFilter() {
            if (!this.priceSlider || !this.priceDisplay) return;

            this.priceSlider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                this.filters.price.max = value;
                this.updatePriceDisplay();
            });

            this.priceSlider.addEventListener('change', () => {
                this.debouncedApplyFilters();
            });

            this.updatePriceDisplay();
        }

        /**
         * Update price display
         */
        updatePriceDisplay() {
            if (this.priceDisplay) {
                this.priceDisplay.textContent = `R$ ${this.filters.price.min} - R$ ${this.filters.price.max}`;
            }
        }

        /**
         * Update filter UI state
         */
        updateFilterUI(checkbox) {
            const label = checkbox.closest('label');
            if (label) {
                label.classList.toggle('active', checkbox.checked);
            }
        }

        /**
         * Apply filters via AJAX
         */
        async applyFilters() {
            if (this.isLoading) return;

            this.isLoading = true;
            this.showLoadingState();

            try {
                const response = await this.fetchFilteredProducts();
                this.updateProductsDisplay(response);
                this.updateURL();
                this.updateFilterCounts(response.filter_counts);
            } catch (error) {
                console.error('Error applying filters:', error);
                this.showErrorMessage();
            } finally {
                this.isLoading = false;
                this.hideLoadingState();
            }
        }

        /**
         * Fetch filtered products via AJAX
         */
        async fetchFilteredProducts() {
            const params = new URLSearchParams({
                action: 'filter_products',
                nonce: constructionStore.nonce,
                categories: JSON.stringify(this.filters.categories),
                brands: JSON.stringify(this.filters.brands),
                price_min: this.filters.price.min,
                price_max: this.filters.price.max,
                stock: JSON.stringify(this.filters.stock),
                specs: JSON.stringify(this.filters.specs),
                page: this.currentPage
            });

            const response = await fetch(constructionStore.ajaxUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            return await response.json();
        }

        /**
         * Update products display with filtered results
         */
        updateProductsDisplay(response) {
            if (this.productsContainer && response.html) {
                this.productsContainer.innerHTML = response.html;
                
                // Reinitialize product grid functionality
                if (window.ProductGrid) {
                    new window.ProductGrid();
                }
                
                // Update results count
                this.updateResultsCount(response.total);
                
                // Update pagination
                this.updatePagination(response.pagination);
            }
        }

        /**
         * Update results count
         */
        updateResultsCount(total) {
            const resultsCount = document.querySelector('.results-count');
            if (resultsCount) {
                resultsCount.textContent = `Mostrando ${total} produtos`;
            }
        }

        /**
         * Update pagination
         */
        updatePagination(paginationHtml) {
            const pagination = document.querySelector('.products-pagination');
            if (pagination && paginationHtml) {
                pagination.innerHTML = paginationHtml;
            }
        }

        /**
         * Update filter counts
         */
        updateFilterCounts(counts) {
            if (!counts) return;

            // Update category counts
            Object.entries(counts.categories || {}).forEach(([category, count]) => {
                const countElement = document.querySelector(`[data-category="${category}"] .filter-count`);
                if (countElement) {
                    countElement.textContent = count;
                    countElement.classList.toggle('active', count > 0);
                }
            });

            // Update brand counts
            Object.entries(counts.brands || {}).forEach(([brand, count]) => {
                const countElement = document.querySelector(`[data-brand="${brand}"] .filter-count`);
                if (countElement) {
                    countElement.textContent = count;
                    countElement.classList.toggle('active', count > 0);
                }
            });
        }

        /**
         * Load initial filter counts
         */
        async loadFilterCounts() {
            try {
                const response = await fetch(constructionStore.ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'get_filter_counts',
                        nonce: constructionStore.nonce
                    })
                });

                const data = await response.json();
                if (data.success) {
                    this.updateFilterCounts(data.data);
                }
            } catch (error) {
                console.error('Error loading filter counts:', error);
            }
        }

        /**
         * Clear all filters
         */
        clearAllFilters() {
            // Reset filter state
            this.filters = {
                categories: [],
                price: { min: 0, max: 1000 },
                brands: [],
                stock: [],
                specs: {
                    material: [],
                    application: []
                }
            };

            // Reset UI
            const checkboxes = this.filtersContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                this.updateFilterUI(checkbox);
            });

            // Reset price slider
            if (this.priceSlider) {
                this.priceSlider.value = this.filters.price.max;
                this.updatePriceDisplay();
            }

            // Apply cleared filters
            this.applyFilters();
        }

        /**
         * Setup mobile filters
         */
        setupMobileFilters() {
            if (this.mobileToggle) {
                this.mobileToggle.addEventListener('click', () => {
                    this.toggleMobileFilters();
                });
            }

            // Close button
            const closeBtn = document.createElement('button');
            closeBtn.className = 'mobile-filters-close';
            closeBtn.innerHTML = '×';
            closeBtn.addEventListener('click', () => {
                this.closeMobileFilters();
            });

            if (this.filtersContainer) {
                this.filtersContainer.appendChild(closeBtn);
            }

            // Overlay
            const overlay = document.createElement('div');
            overlay.className = 'filters-overlay';
            overlay.addEventListener('click', () => {
                this.closeMobileFilters();
            });
            document.body.appendChild(overlay);
        }

        /**
         * Toggle mobile filters
         */
        toggleMobileFilters() {
            const overlay = document.querySelector('.filters-overlay');
            
            if (this.filtersContainer.classList.contains('active')) {
                this.closeMobileFilters();
            } else {
                this.filtersContainer.classList.add('active');
                overlay.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        /**
         * Close mobile filters
         */
        closeMobileFilters() {
            const overlay = document.querySelector('.filters-overlay');
            
            this.filtersContainer.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        /**
         * Show loading state
         */
        showLoadingState() {
            if (this.productsContainer) {
                this.productsContainer.style.opacity = '0.6';
                this.productsContainer.style.pointerEvents = 'none';
            }

            // Add loading spinner
            const loadingSpinner = document.createElement('div');
            loadingSpinner.className = 'filters-loading';
            loadingSpinner.innerHTML = '<div class="spinner"></div>';
            loadingSpinner.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 100;
            `;

            if (this.productsContainer) {
                this.productsContainer.style.position = 'relative';
                this.productsContainer.appendChild(loadingSpinner);
            }
        }

        /**
         * Hide loading state
         */
        hideLoadingState() {
            if (this.productsContainer) {
                this.productsContainer.style.opacity = '';
                this.productsContainer.style.pointerEvents = '';
            }

            const loadingSpinner = document.querySelector('.filters-loading');
            if (loadingSpinner) {
                loadingSpinner.remove();
            }
        }

        /**
         * Show error message
         */
        showErrorMessage() {
            const errorMessage = document.createElement('div');
            errorMessage.className = 'filter-error';
            errorMessage.textContent = 'Erro ao carregar produtos. Tente novamente.';
            errorMessage.style.cssText = `
                background: #f8d7da;
                color: #721c24;
                padding: 12px 16px;
                border-radius: 4px;
                margin: 20px 0;
                text-align: center;
            `;

            if (this.productsContainer) {
                this.productsContainer.insertBefore(errorMessage, this.productsContainer.firstChild);
                
                setTimeout(() => {
                    errorMessage.remove();
                }, 5000);
            }
        }

        /**
         * Update URL with current filters
         */
        updateURL() {
            const params = new URLSearchParams(window.location.search);
            
            // Update URL parameters
            if (this.filters.categories.length > 0) {
                params.set('categories', this.filters.categories.join(','));
            } else {
                params.delete('categories');
            }
            
            if (this.filters.brands.length > 0) {
                params.set('brands', this.filters.brands.join(','));
            } else {
                params.delete('brands');
            }
            
            if (this.filters.price.max !== 1000) {
                params.set('max_price', this.filters.price.max);
            } else {
                params.delete('max_price');
            }
            
            // Update browser URL without page reload
            const newURL = `${window.location.pathname}?${params.toString()}`;
            window.history.pushState({}, '', newURL);
        }

        /**
         * Restore filters from URL parameters
         */
        restoreFiltersFromURL() {
            const params = new URLSearchParams(window.location.search);
            
            // Restore categories
            const categories = params.get('categories');
            if (categories) {
                this.filters.categories = categories.split(',');
                this.filters.categories.forEach(category => {
                    const checkbox = document.querySelector(`input[value="${category}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        this.updateFilterUI(checkbox);
                    }
                });
            }
            
            // Restore brands
            const brands = params.get('brands');
            if (brands) {
                this.filters.brands = brands.split(',');
                this.filters.brands.forEach(brand => {
                    const checkbox = document.querySelector(`input[value="${brand}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        this.updateFilterUI(checkbox);
                    }
                });
            }
            
            // Restore price
            const maxPrice = params.get('max_price');
            if (maxPrice) {
                this.filters.price.max = parseInt(maxPrice);
                if (this.priceSlider) {
                    this.priceSlider.value = maxPrice;
                    this.updatePriceDisplay();
                }
            }
        }

        /**
         * Debounce function
         */
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }

    /**
     * Initialize when DOM is ready
     */
    function initProductFilters() {
        if (document.querySelector('.product-filters-sidebar')) {
            new ProductFilters();
        }
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initProductFilters);
    } else {
        initProductFilters();
    }

    // Global function for clearing filters (used by clear buttons)
    window.clearAllFilters = function() {
        const filtersInstance = window.productFiltersInstance;
        if (filtersInstance) {
            filtersInstance.clearAllFilters();
        }
    };

})();
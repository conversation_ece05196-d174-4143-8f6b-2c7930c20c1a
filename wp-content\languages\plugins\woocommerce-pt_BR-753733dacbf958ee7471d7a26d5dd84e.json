{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Align the last block to the bottom.": ["<PERSON><PERSON><PERSON> o último bloco para baixo."], "Align the last block to the bottom": ["<PERSON><PERSON><PERSON> o último bloco para baixo"], "An error has prevented the block from being updated.": ["Um erro impediu a atualização do bloco."], "Now displaying a preview of the reviews for the products in the selected categories.": ["Agora exibindo uma prévia das avaliações dos produtos nas categorias selecionadas."], "Stock status \"%s\" hidden.": ["Status do estoque \"%s\" oculto."], "Stock status \"%s\" visible.": ["Status do estoque \"%s\" visível."], "Edit selected categories": ["Editar categorias selecionadas"], "%1$s, has %2$d review": ["%1$s, possui %2$d avaliação", "%1$s, possui %2$d avaliações"], "%1$s, has %2$d product": ["%1$s, possui %2$d produto", "%1$s, possui %2$d produtos"], "Loading…": ["Carregando..."], "The last inner block will follow other content.": ["O último bloco interno seguirá outro conteúdo."], "No products were found that matched your selection.": ["Nenhum produto encontrado corresponde à sua seleção."], "The following error was returned": ["O seguinte erro foi retornado"], "%d review": ["%d avaliação", "%d avaliações"], "The following error was returned from the API": ["O seguinte erro foi retornado da API"], "Search for items": ["Procurar por itens"], "%d item selected": ["%d item selecionado", "%d itens selecionados"], "Clear all selected items": ["Remover todos os itens selecionados"], "Clear all": ["Remover todos"], "No results for %s": ["Nenhum resultado para %s"], "No items found.": ["Nenhum item encontrado."], "Remove %s": ["Remover %s"], "Search results updated.": ["Resultados de pesquisa atualizados."], "Display a grid of products from your selected categories.": ["Exibe uma grade de produtos com base em categorias selecionadas."], "Clear all product categories": ["Remover todas as categorias de produto"], "Select at least one category to display its products.": ["Selecione pelo menos uma categoria para exibir seus produtos."], "Products by Category": ["Produtos por categoria"], "Category search results updated.": ["Resultados atualizados para pesquisa de categoria."], "All selected categories": ["<PERSON><PERSON> as categorias selecionadas"], "Any selected categories": ["Quaisquer categorias selecionadas"], "Pick at least two categories to use this setting.": ["Escolha pelo menos duas categorias para usar esta configuração."], "%d category selected": ["%d categoria selecionada", "%d categorias selecionadas"], "Search for product categories": ["Procurar por categorias de produtos"], "Your store doesn't have any product categories.": ["Sua loja não tem categorias de produtos."], "Done": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Order By": ["Ordenar por"], "Rows": ["<PERSON><PERSON>"], "Columns": ["Colunas"], "Layout": ["<PERSON><PERSON>"], "Add to Cart button": ["Botão \"Adicionar ao carrinho\""], "Menu Order": ["Ordem do menu"], "Title - alphabetical": ["Título - alfabético"], "Sales - most first": ["Vendas - mais vendas primeiro"], "Rating - highest first": ["Classificação - mais alta primeiro"], "Price - high to low": ["Preço - de alto para baixo"], "Price - low to high": ["Preço - de baixo para alto"], "Newness - newest first": ["Novidade - mais novo primeiro"], "Order products by": ["Ordenar produtos por"], "Display products matching": ["<PERSON><PERSON>r produtos correspondentes"], "Product rating": ["Classificação do produto"], "Product price": ["Preço do produto"], "Product title": ["Título do produto"], "Filter by stock status": ["Filtrar por status do estoque"], "Product image": ["Imagem do produto"], "%d product": ["%d produto", "%d produtos"], "Product Category": ["Categoria de produto"], "Content": ["<PERSON><PERSON><PERSON><PERSON>"], "Product Categories": ["Categorias de produto"], "Cancel": ["<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/product-category.js"}}
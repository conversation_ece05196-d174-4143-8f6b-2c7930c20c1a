{"translation-revision-date": "2025-06-30 13:20:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n > 1;", "lang": "pt_BR"}, "Previously selected product is no longer available.": ["O produto selecionado anteriormente não está mais disponível."], "An error has prevented the block from being updated.": ["Um erro impediu a atualização do bloco."], "Edit selected product": ["Editar produto selecionado"], "%1$s, has %2$d variation": ["%1$s, tem %2$d variação", "%1$s, tem %2$d variações"], "%1$d variations": ["%1$d variações"], "Loading…": ["Carregando..."], "Reset layout": ["Redefinir modelo"], "Edit details such as title, price, description and more.": ["Editar detalhes como título, preço, descrição e mais."], "Edit this product's details": ["<PERSON><PERSON> de<PERSON>hes deste produto"], "Single Product Block Error": ["Erro no bloco de produto simples"], "Reset layout to default": ["Redefinir modelo para o padrão"], "Oops!": ["Ops!"], "There was an error loading the content.": ["Ocorreu um erro ao carregar o conteúdo."], "The following error was returned": ["O seguinte erro foi retornado"], "The following error was returned from the API": ["O seguinte erro foi retornado da API"], "Sorry, an error occurred": ["Ocorreu um erro"], "Retry": ["Tentar novamente"], "Search for items": ["Procurar por itens"], "%d item selected": ["%d item selecionado", "%d itens selecionados"], "Clear all selected items": ["Remover todos os itens selecionados"], "Clear all": ["Remover todos"], "No results for %s": ["Nenhum resultado para %s"], "No items found.": ["Nenhum item encontrado."], "Remove %s": ["Remover %s"], "Search results updated.": ["Resultados de pesquisa atualizados."], "Product search results updated.": ["Resultados atualizados para pesquisa de produto."], "Search for a product to display": ["Procure um produto para exibir"], "Your store doesn't have any products.": ["Sua loja não tem nenhum produto."], "Done": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Layout": ["<PERSON><PERSON>"], "Error:": ["Erro:"], "Products": ["<PERSON><PERSON><PERSON>"], "Product": ["Produ<PERSON>", "<PERSON><PERSON><PERSON>"]}}, "comment": {"reference": "assets/client/blocks/single-product.js"}}
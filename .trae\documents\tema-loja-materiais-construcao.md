# Documento de Requisitos - Tema para Loja de Materiais de Construção

## 1. Visão Geral do Produto

Tema WordPress personalizado para e-commerce de materiais de construção, ferragens e produtos relacionados, integrado com WooCommerce. O tema oferece funcionalidades específicas para o setor da construção civil, facilitando a navegação por categorias técnicas e a compra de produtos em grandes quantidades.

O objetivo é criar uma experiência de compra profissional que atenda tanto construtores profissionais quanto consumidores finais, com recursos de orçamento, catálogo técnico e suporte a vendas B2B.

## 2. Funcionalidades Principais

### 2.1 Papéis de Usuário

| Papel | Método de Registro | Permissões Principais |
|-------|-------------------|----------------------|
| Cliente Varejo | Registro por email | Compras normais, preços de varejo |
| Cliente Profissional | Aprovação manual com CNPJ | Preços especiais, compra em quantidade, crédito |
| Administrador | Acesso direto | Gestão completa da loja e produtos |

### 2.2 Módulos de Funcionalidade

Nossos requisitos consistem nas seguintes páginas principais:

1. **Página Inicial**: banner hero com promoções, categorias em destaque, produtos mais vendidos, seção de serviços.
2. **Catálogo de Produtos**: filtros avançados por categoria, marca, aplicação, faixa de preço, busca por código.
3. **Página de Produto**: especificações técnicas detalhadas, calculadora de quantidade, produtos relacionados.
4. **Carrinho e Checkout**: opções de entrega, calculadora de frete, pagamento parcelado.
5. **Área do Cliente**: histórico de pedidos, lista de favoritos, orçamentos salvos.
6. **Página de Orçamento**: formulário para solicitação de orçamento personalizado.
7. **Página Institucional**: sobre a empresa, localização das lojas, contato.

### 2.3 Detalhes das Páginas

| Nome da Página | Nome do Módulo | Descrição da Funcionalidade |
|----------------|----------------|-----------------------------|
| Página Inicial | Banner Hero | Exibir promoções principais, produtos em destaque com imagens atrativas |
| Página Inicial | Categorias em Destaque | Grid de categorias principais (cimento, ferramentas, hidráulica, elétrica) com ícones |
| Página Inicial | Produtos Mais Vendidos | Carrossel de produtos populares com preços e botão de compra rápida |
| Catálogo | Filtros Avançados | Filtrar por categoria, subcategoria, marca, faixa de preço, aplicação |
| Catálogo | Busca por Código | Campo de busca específico para códigos de produto e referências |
| Catálogo | Visualização de Produtos | Grid responsivo com imagem, nome, código, preço e botão de adicionar |
| Produto | Galeria de Imagens | Múltiplas fotos do produto com zoom, vídeos demonstrativos |
| Produto | Especificações Técnicas | Tabela detalhada com dimensões, peso, material, normas técnicas |
| Produto | Calculadora de Quantidade | Ferramenta para calcular quantidade necessária baseada em área/projeto |
| Produto | Produtos Relacionados | Sugestões de produtos complementares e acessórios |
| Carrinho | Calculadora de Frete | Integração com Correios e transportadoras para cálculo automático |
| Carrinho | Opções de Entrega | Retirada na loja, entrega expressa, agendamento de entrega |
| Checkout | Pagamento Parcelado | Integração com cartões, PIX, boleto e crediário próprio |
| Área do Cliente | Histórico de Pedidos | Lista completa de compras anteriores com status e rastreamento |
| Área do Cliente | Lista de Favoritos | Produtos salvos para compra futura com alertas de promoção |
| Orçamento | Formulário de Solicitação | Campos para projeto, quantidade, prazo, dados de contato |
| Orçamento | Lista de Produtos | Adicionar múltiplos produtos ao orçamento com quantidades |
| Institucional | Sobre a Empresa | História, missão, certificações e diferenciais |
| Institucional | Localização das Lojas | Mapa interativo com endereços e horários de funcionamento |

## 3. Processo Principal

**Fluxo do Cliente Varejo:**
O cliente acessa a página inicial, navega pelas categorias ou usa a busca, visualiza o produto com suas especificações, adiciona ao carrinho, calcula o frete, finaliza a compra com pagamento online.

**Fluxo do Cliente Profissional:**
O cliente profissional faz login, acessa preços especiais, adiciona produtos em quantidade ao carrinho ou solicita orçamento personalizado, negocia condições especiais, finaliza pedido com crediário ou pagamento a prazo.

```mermaid
graph TD
    A[Página Inicial] --> B[Catálogo de Produtos]
    A --> C[Busca por Código]
    B --> D[Página de Produto]
    C --> D
    D --> E[Carrinho]
    D --> F[Solicitar Orçamento]
    E --> G[Checkout]
    F --> H[Área do Cliente]
    G --> I[Confirmação do Pedido]
    H --> J[Acompanhar Pedidos]
```

## 4. Design da Interface do Usuário

### 4.1 Estilo de Design

- **Cores Primárias**: Azul profissional (#1e3a8a), laranja de destaque (#f97316)
- **Cores Secundárias**: Cinza neutro (#6b7280), branco (#ffffff)
- **Estilo de Botões**: Retangulares com bordas levemente arredondadas, efeito hover
- **Tipografia**: Fonte sans-serif moderna (Inter ou similar), tamanhos 14px-18px para texto
- **Layout**: Design limpo e profissional, grid baseado em cards, navegação superior fixa
- **Ícones**: Estilo outline minimalista, relacionados à construção civil

### 4.2 Visão Geral do Design das Páginas

| Nome da Página | Nome do Módulo | Elementos de UI |
|----------------|----------------|----------------|
| Página Inicial | Banner Hero | Slider full-width com call-to-action em botões laranja, imagens de alta qualidade |
| Página Inicial | Categorias | Grid 4x2 com ícones grandes, fundo azul claro, hover com elevação |
| Catálogo | Filtros | Sidebar esquerda com checkboxes, sliders de preço, cores neutras |
| Catálogo | Produtos | Cards brancos com sombra sutil, imagem quadrada, preço em destaque |
| Produto | Galeria | Imagem principal grande, thumbnails abaixo, botão de zoom |
| Produto | Especificações | Tabela zebrada com headers azuis, texto organizado |
| Carrinho | Resumo | Layout em duas colunas, produtos à esquerda, totais à direita |
| Checkout | Formulário | Steps visuais, campos agrupados, validação em tempo real |

### 4.3 Responsividade

O tema é mobile-first com adaptação completa para tablets e desktops. Inclui otimização para touch em dispositivos móveis, com botões maiores e navegação simplificada para facilitar compras em canteiros de obra.
# Project Structure

## Root Directory
- `index.php` - Main entry point, loads WordPress theme
- `wp-config.php` - WordPress configuration (database, keys, constants)
- `wp-*.php` - Core WordPress files (login, cron, load, settings, etc.)
- `.htaccess` - Apache rewrite rules for pretty URLs
- `xmlrpc.php` - XML-RPC endpoint for remote publishing

## Core WordPress Directories

### `/wp-admin/`
WordPress administration interface files
- Admin dashboard functionality
- User management, content editing
- Plugin/theme management interfaces
- System tools and settings

### `/wp-includes/`
WordPress core functionality
- Core classes and functions
- Template system
- Database abstraction layer
- REST API implementation
- Block editor components

### `/wp-content/`
User-generated and customizable content

#### `/wp-content/themes/`
- `twentytwentyfive/` - Default WordPress theme (2025)
- `twentytwentyfour/` - WordPress theme (2024)  
- `twentytwentythree/` - WordPress theme (2023)

#### `/wp-content/plugins/`
- `woocommerce/` - E-commerce functionality
- `litespeed-cache/` - Performance optimization
- `akismet/` - Spam protection
- `hello.php` - Sample plugin

#### `/wp-content/uploads/`
Media files and user uploads
- `2025/` - Year-based organization
- `wc-logs/` - WooCommerce log files
- `woocommerce_uploads/` - WooCommerce file uploads
- Various WooCommerce placeholder images

#### `/wp-content/languages/`
Localization files for Brazilian Portuguese
- Core WordPress translations
- Plugin-specific translations
- Theme translations

## Configuration Files

### Database & Environment
- `wp-config.php` - Main configuration
- Database: `trae` with `wp_` prefix
- Dynamic URL configuration for development

### Server Configuration  
- `.htaccess` - URL rewriting and server directives
- Supports pretty permalinks
- HTTP Authorization header handling

## Development Directories
- `.kiro/` - Kiro AI assistant configuration
- `.trae/` - Project-specific files
- `.vercel/` - Vercel deployment configuration

## File Organization Principles
- WordPress follows a strict directory structure
- Core files should not be modified directly
- Customizations go in `/wp-content/`
- Themes contain presentation logic
- Plugins contain functionality extensions
- Uploads are organized by date for media files

## Important Notes
- Never modify files in `/wp-admin/` or `/wp-includes/`
- Custom code belongs in themes or plugins
- Media uploads are automatically organized by date
- Language files support full Portuguese localization
- WooCommerce extends the structure with e-commerce specific directories
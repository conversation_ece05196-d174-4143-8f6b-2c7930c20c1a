/**
 * Single Product Page JavaScript
 * Handles product-specific functionality including calculator integration
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeProductPage();
});

/**
 * Initialize product page functionality
 */
function initializeProductPage() {
    initializeProductTabs();
    initializeShippingCalculator();
    initializeMaterialCalculator();
    initializeProductSpecs();
    initializeImageGallery();
}

/**
 * Initialize product tabs
 */
function initializeProductTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanels = document.querySelectorAll('.tab-panel');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));
            
            // Add active class to clicked button and corresponding panel
            this.classList.add('active');
            const targetPanel = document.getElementById(targetTab);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }
        });
    });
}

/**
 * Initialize shipping calculator
 */
function initializeShippingCalculator() {
    const cepInput = document.querySelector('.shipping-cep');
    const calcButton = document.querySelector('.shipping-calc-btn');
    const resultsDiv = document.querySelector('.shipping-results');
    
    if (!cepInput || !calcButton || !resultsDiv) return;
    
    // Format CEP input
    cepInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 5) {
            value = value.substring(0, 5) + '-' + value.substring(5, 8);
        }
        this.value = value;
    });
    
    // Calculate shipping
    calcButton.addEventListener('click', function() {
        const cep = cepInput.value.replace(/\D/g, '');
        
        if (cep.length !== 8) {
            resultsDiv.innerHTML = '<p class="error">CEP inválido. Digite um CEP válido.</p>';
            return;
        }
        
        // Show loading
        this.textContent = 'Calculando...';
        this.disabled = true;
        
        // Simulate shipping calculation (replace with real API call)
        setTimeout(() => {
            resultsDiv.innerHTML = `
                <div class="shipping-options">
                    <div class="shipping-option">
                        <strong>Entrega Normal</strong>
                        <span class="shipping-time">5-7 dias úteis</span>
                        <span class="shipping-price">R$ 15,90</span>
                    </div>
                    <div class="shipping-option">
                        <strong>Entrega Expressa</strong>
                        <span class="shipping-time">2-3 dias úteis</span>
                        <span class="shipping-price">R$ 25,90</span>
                    </div>
                    <div class="shipping-option">
                        <strong>Retirada na Loja</strong>
                        <span class="shipping-time">Disponível hoje</span>
                        <span class="shipping-price">Grátis</span>
                    </div>
                </div>
            `;
            
            // Reset button
            this.textContent = 'Calcular Frete';
            this.disabled = false;
        }, 1500);
    });
}

/**
 * Initialize material calculator for applicable products
 */
function initializeMaterialCalculator() {
    // Check if product has coverage area or is calculable
    const productData = getProductData();
    
    if (productData.isCalculable) {
        showMaterialCalculator(productData);
    }
}

/**
 * Get product data from page
 */
function getProductData() {
    // This would normally get data from WordPress/WooCommerce
    // For now, we'll check for specific indicators
    
    const productTitle = document.querySelector('.product-title')?.textContent?.toLowerCase() || '';
    const productCategories = document.querySelector('.product-meta .posted_in')?.textContent?.toLowerCase() || '';
    
    const calculableKeywords = ['tinta', 'cimento', 'argamassa', 'piso', 'azulejo'];
    const isCalculable = calculableKeywords.some(keyword => 
        productTitle.includes(keyword) || productCategories.includes(keyword)
    );
    
    let productType = 'paint'; // default
    if (productTitle.includes('cimento')) productType = 'cement';
    else if (productTitle.includes('argamassa')) productType = 'mortar';
    else if (productTitle.includes('piso')) productType = 'flooring';
    else if (productTitle.includes('azulejo')) productType = 'tiles';
    
    return {
        isCalculable: isCalculable,
        productType: productType,
        coverage: getCoverageFromSpecs() || getDefaultCoverage(productType),
        productId: getProductId(),
        productName: document.querySelector('.product-title')?.textContent || 'Produto'
    };
}

/**
 * Get coverage from product specifications
 */
function getCoverageFromSpecs() {
    const specsText = document.querySelector('.technical-specs')?.textContent || '';
    const coverageMatch = specsText.match(/(\d+(?:\.\d+)?)\s*m²/i);
    return coverageMatch ? parseFloat(coverageMatch[1]) : null;
}

/**
 * Get default coverage for product type
 */
function getDefaultCoverage(productType) {
    const defaults = {
        paint: 12,
        cement: 4,
        mortar: 5,
        flooring: 1,
        tiles: 1
    };
    return defaults[productType] || 12;
}

/**
 * Get product ID from page
 */
function getProductId() {
    // Try to get from body class or data attribute
    const bodyClasses = document.body.className;
    const match = bodyClasses.match(/postid-(\d+)/);
    return match ? parseInt(match[1]) : 1; // fallback to 1
}

/**
 * Show material calculator
 */
function showMaterialCalculator(productData) {
    const calculatorSection = document.querySelector('.material-calculator');
    if (!calculatorSection) return;
    
    calculatorSection.style.display = 'block';
    
    // Initialize calculator with product-specific data
    if (window.materialCalculator) {
        // Set default product type
        const productTypeSelect = document.getElementById('productType');
        if (productTypeSelect) {
            productTypeSelect.value = productData.productType;
        }
        
        // Set default coverage
        const coverageInput = document.getElementById('customCoverage');
        if (coverageInput) {
            coverageInput.value = productData.coverage;
        }
        
        // Override add to cart function for WooCommerce integration
        const originalAddToCart = window.materialCalculator.addToCart;
        window.materialCalculator.addToCart = function() {
            if (this.currentCalculation) {
                addCalculatedToWooCommerceCart(this.currentCalculation, productData);
            }
        };
    }
}

/**
 * Add calculated quantity to WooCommerce cart
 */
function addCalculatedToWooCommerceCart(calculation, productData) {
    const addToCartBtn = document.getElementById('addToCartButton');
    if (!addToCartBtn) return;
    
    // Show loading state
    const originalText = addToCartBtn.textContent;
    addToCartBtn.textContent = 'Adicionando...';
    addToCartBtn.disabled = true;
    
    // Prepare form data
    const formData = new FormData();
    formData.append('action', 'add_calculated_to_cart');
    formData.append('product_id', productData.productId);
    formData.append('quantity', calculation.recommendedQuantity);
    formData.append('calculation_data', JSON.stringify(calculation));
    formData.append('nonce', getWooCommerceNonce());
    
    // Send AJAX request
    fetch(getAjaxUrl(), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(data.data.message);
            updateCartCount(data.data.cart_count);
            window.materialCalculator.resetCalculator();
        } else {
            showErrorMessage(data.data.message || 'Erro ao adicionar produto ao carrinho');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('Erro ao adicionar produto ao carrinho');
    })
    .finally(() => {
        // Reset button
        addToCartBtn.textContent = originalText;
        addToCartBtn.disabled = false;
    });
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'woocommerce-message';
    messageDiv.innerHTML = message;
    
    const calculator = document.getElementById('materialCalculator');
    calculator.parentNode.insertBefore(messageDiv, calculator);
    
    // Scroll to message
    messageDiv.scrollIntoView({ behavior: 'smooth' });
    
    // Remove message after 5 seconds
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    alert(message); // Simple alert for now, could be enhanced with custom modal
}

/**
 * Update cart count in header
 */
function updateCartCount(count) {
    const cartCountElements = document.querySelectorAll('.cart-count, .cart-contents-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
    });
}

/**
 * Get WooCommerce nonce (would be localized from PHP)
 */
function getWooCommerceNonce() {
    return window.constructionStore?.nonce || '';
}

/**
 * Get AJAX URL (would be localized from PHP)
 */
function getAjaxUrl() {
    return window.constructionStore?.ajaxUrl || '/wp-admin/admin-ajax.php';
}

/**
 * Initialize product specifications display
 */
function initializeProductSpecs() {
    // This would populate specs from product meta data
    // For now, we'll just show placeholder functionality
    
    const specValues = document.querySelectorAll('.spec-value');
    specValues.forEach(element => {
        const specType = element.dataset.spec;
        const value = getSpecValue(specType);
        if (value) {
            element.textContent = value;
            element.parentElement.style.display = 'block';
        }
    });
}

/**
 * Get specification value (placeholder - would come from product meta)
 */
function getSpecValue(specType) {
    // This would normally fetch from product meta data
    const placeholderSpecs = {
        dimensions: '10 x 20 x 5 cm',
        weight: '2.5 kg',
        material: 'Aço inoxidável',
        application: 'Uso residencial'
    };
    
    return placeholderSpecs[specType] || null;
}

/**
 * Initialize image gallery enhancements
 */
function initializeImageGallery() {
    // Add zoom functionality, lightbox, etc.
    // This would enhance the WooCommerce product gallery
    
    const galleryImages = document.querySelectorAll('.woocommerce-product-gallery__image img');
    
    galleryImages.forEach(img => {
        img.addEventListener('click', function() {
            // Could implement lightbox or zoom functionality here
            console.log('Image clicked:', this.src);
        });
    });
}

/**
 * Simple material calculator for basic functionality
 */
function calculateMaterial() {
    const areaInput = document.getElementById('calc-area');
    const quantitySpan = document.getElementById('calc-quantity');
    
    if (!areaInput || !quantitySpan) return;
    
    const area = parseFloat(areaInput.value);
    if (!area || area <= 0) {
        quantitySpan.textContent = 'Digite uma área válida';
        return;
    }
    
    // Get coverage from product (default 12 m²/L for paint)
    const coverage = getCoverageFromSpecs() || 12;
    const baseQuantity = area / coverage;
    const recommendedQuantity = Math.ceil(baseQuantity * 1.1); // 10% safety margin
    
    quantitySpan.textContent = `${recommendedQuantity} litros`;
}

// Export functions for use in other scripts
window.constructionStoreProduct = {
    calculateMaterial,
    addCalculatedToWooCommerceCart,
    getProductData
};
# Predefined list for excluding JS files or inline JS codes #
# Comment can use `# `(there is a space following), or `##`, can use both as a new line or end of one line
# If you want to predefine new items, please send a Pull Request to https://github.com/litespeedtech/lscache_wp/blob/dev/data/js_excludes.txt We will merge into next plugin release

# JS file URL excludes
maps-api-ssl.google.com
maps.google.com/maps
maps.googleapis.com
google.com/recaptcha
google-analytics.com/analytics.js
stats.wp.com
js.stripe.com
paypal.com/sdk/js
cse.google.com/cse.js
/syntaxhighlighter/
spotlight-social-photo-feeds ## https://docs.spotlightwp.com/article/757-autoptimize-compatibility @Tobolo
userway.org

# Inline JS excludes
document.write
gtag
gtm
dataLayer
adsbygoogle

block_tdi_ ## Theme: Newspaper by tagDiv.com

data-view-breakpoint-pointer ## Plugin: The Events Calendar by Modern Tribe (https://theeventscalendar.com/)

wp-json/wp-statistics ## WP Statistics

## JetPack Stats
stats.wp.com/e-
_stq

# Cloudflare turnstile - Tobolo
turnstile
challenges.cloudflare.com
/**
 * Material Calculator Component Styles
 */

.material-calculator {
    background: var(--color-gray-100);
    border: 1px solid var(--color-gray-200);
    border-radius: 8px;
    padding: var(--space-6);
    margin: var(--space-4) 0;
    max-width: 600px;
}

.calculator-header {
    text-align: center;
    margin-bottom: var(--space-6);
}

.calculator-header h3 {
    color: var(--color-gray-900);
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin: 0 0 var(--space-2) 0;
}

.calculator-header p {
    color: var(--color-gray-600);
    font-size: var(--font-size-base);
    margin: 0;
}

/* Form Styles */
.calculator-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group label {
    font-weight: 500;
    color: var(--color-gray-800);
    font-size: var(--font-size-sm);
}

.form-control {
    padding: var(--space-3);
    border: 1px solid var(--color-gray-300);
    border-radius: 4px;
    font-size: var(--font-size-base);
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.form-control:invalid {
    border-color: var(--color-danger);
}

/* Method Tabs */
.calculation-method {
    border: 1px solid var(--color-gray-200);
    border-radius: 6px;
    overflow: hidden;
}

.method-tabs {
    display: flex;
    background: var(--color-gray-200);
}

.tab-button {
    flex: 1;
    padding: var(--space-3);
    border: none;
    background: transparent;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--color-gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-button.active {
    background: var(--color-primary);
    color: white;
}

.tab-button:hover:not(.active) {
    background: var(--color-gray-300);
}

.method-content {
    padding: var(--space-4);
    background: white;
}

.method-content.hidden {
    display: none;
}

/* Multiple Areas */
.areas-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.area-input {
    display: grid;
    grid-template-columns: 1fr 120px 40px;
    gap: var(--space-2);
    align-items: center;
}

.area-name,
.area-value {
    padding: var(--space-2);
    border: 1px solid var(--color-gray-300);
    border-radius: 4px;
    font-size: var(--font-size-sm);
}

.remove-area {
    width: 32px;
    height: 32px;
    border: 1px solid var(--color-danger);
    background: white;
    color: var(--color-danger);
    border-radius: 4px;
    cursor: pointer;
    font-size: var(--font-size-lg);
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-area:hover {
    background: var(--color-danger);
    color: white;
}

/* Advanced Options */
.advanced-options {
    border-top: 1px solid var(--color-gray-200);
    padding-top: var(--space-4);
}

.toggle-advanced {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
    margin-bottom: var(--space-3);
}

.toggle-advanced:hover {
    color: var(--color-primary-dark);
}

.advanced-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.advanced-content.hidden {
    display: none;
}

/* Buttons */
.btn-primary,
.btn-secondary {
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: 6px;
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--color-primary-dark);
}

.btn-secondary {
    background: var(--color-gray-200);
    color: var(--color-gray-800);
    border: 1px solid var(--color-gray-300);
}

.btn-secondary:hover {
    background: var(--color-gray-300);
}

#calculateButton {
    margin-top: var(--space-2);
}

/* Results */
.calculator-results {
    background: white;
    border: 1px solid var(--color-success);
    border-radius: 6px;
    padding: var(--space-4);
    margin-top: var(--space-4);
}

.calculator-results.hidden {
    display: none;
}

.results-header h4 {
    color: var(--color-success);
    font-size: var(--font-size-xl);
    margin: 0 0 var(--space-4) 0;
}

.result-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--color-gray-200);
}

.result-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.result-item .label {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
    font-weight: 500;
}

.result-item .value {
    font-size: var(--font-size-base);
    color: var(--color-gray-900);
    font-weight: 600;
}

.result-calculation {
    margin-bottom: var(--space-4);
}

.calc-step {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--color-gray-100);
}

.calc-step:last-child {
    border-bottom: none;
}

.calc-step.recommended {
    background: rgba(40, 167, 69, 0.1);
    padding: var(--space-3);
    border-radius: 4px;
    border: 1px solid var(--color-success);
    margin: var(--space-2) 0;
}

.calc-step.recommended .step-value {
    color: var(--color-success);
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.step-label {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
}

.step-value {
    font-size: var(--font-size-base);
    color: var(--color-gray-900);
    font-weight: 600;
}

.result-breakdown {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--color-gray-200);
}

.result-breakdown h5 {
    font-size: var(--font-size-base);
    color: var(--color-gray-800);
    margin: 0 0 var(--space-2) 0;
}

.rooms-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.rooms-list li {
    padding: var(--space-1) 0;
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
}

.results-actions {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--color-gray-200);
}

/* Errors */
.calculator-errors {
    background: #fff5f5;
    border: 1px solid var(--color-danger);
    border-radius: 6px;
    padding: var(--space-4);
    margin-top: var(--space-4);
}

.calculator-errors.hidden {
    display: none;
}

.error-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.error-list li {
    color: var(--color-danger);
    font-size: var(--font-size-sm);
    padding: var(--space-1) 0;
}

.error-list li:before {
    content: "⚠ ";
    margin-right: var(--space-1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .material-calculator {
        padding: var(--space-4);
        margin: var(--space-3) 0;
    }

    .area-input {
        grid-template-columns: 1fr;
        gap: var(--space-2);
    }

    .remove-area {
        justify-self: end;
        width: 100%;
        max-width: 120px;
    }

    .result-summary {
        grid-template-columns: 1fr;
    }

    .results-actions {
        flex-direction: column;
    }

    .calc-step {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-1);
    }

    .method-tabs {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .calculator-header h3 {
        font-size: var(--font-size-xl);
    }

    .material-calculator {
        padding: var(--space-3);
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        padding: var(--space-4);
    }
}

/* Animation for smooth transitions */
.method-content,
.advanced-content,
.calculator-results,
.calculator-errors {
    transition: all 0.3s ease;
}

/* Focus styles for accessibility */
.tab-button:focus,
.form-control:focus,
.btn-primary:focus,
.btn-secondary:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Loading state (for future use) */
.calculator-loading {
    opacity: 0.6;
    pointer-events: none;
}

.calculator-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
/
* Product Page Integration */
.product-calculator-section {
    background: var(--color-gray-100);
    border: 1px solid var(--color-gray-200);
    border-radius: 8px;
    padding: var(--space-6);
    margin: var(--space-6) 0;
}

.product-calculator-section h3 {
    color: var(--color-gray-900);
    font-size: var(--font-size-xl);
    margin: 0 0 var(--space-3) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.product-calculator-section h3:before {
    content: "📐";
    font-size: var(--font-size-2xl);
}

.product-calculator-section p {
    color: var(--color-gray-600);
    margin: 0 0 var(--space-4) 0;
}

/* Safety Margin Info */
.safety-margin-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid var(--color-info);
    border-radius: 8px;
    padding: var(--space-5);
    margin: var(--space-6) 0;
}

.safety-margin-info h4 {
    color: var(--color-info);
    font-size: var(--font-size-lg);
    margin: 0 0 var(--space-3) 0;
}

.safety-margin-info ul {
    margin: 0;
    padding-left: var(--space-5);
    color: var(--color-gray-700);
}

.safety-margin-info li {
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

/* Cart Integration Messages */
.woocommerce-message {
    background: var(--color-success);
    color: white;
    padding: var(--space-4);
    border-radius: 6px;
    margin: var(--space-4) 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.woocommerce-message:before {
    content: "✓";
    font-size: var(--font-size-lg);
    font-weight: bold;
}

/* Calculation Details in Cart */
.calculation-details {
    background: var(--color-gray-100);
    border: 1px solid var(--color-gray-200);
    border-radius: 4px;
    padding: var(--space-3);
    margin: var(--space-2) 0;
    font-size: var(--font-size-sm);
}

.calculation-details strong {
    color: var(--color-gray-900);
}

/* Loading States */
.calculator-loading .form-control,
.calculator-loading .btn-primary,
.calculator-loading .btn-secondary {
    opacity: 0.6;
    pointer-events: none;
}

.calculator-loading .btn-primary:after {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: var(--space-2);
    border: 2px solid white;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Enhanced Product Integration */
.single-product .material-calculator {
    max-width: none;
    background: white;
    border: 2px solid var(--color-primary);
}

.single-product .calculator-header {
    background: var(--color-primary);
    color: white;
    margin: calc(-1 * var(--space-6));
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    border-radius: 6px 6px 0 0;
}

.single-product .calculator-header h3 {
    color: white;
    margin: 0;
}

.single-product .calculator-header p {
    color: rgba(255, 255, 255, 0.9);
    margin: var(--space-2) 0 0 0;
}

/* Cart Item Calculation Display */
.cart-item-calculation {
    background: var(--color-gray-50);
    border-left: 3px solid var(--color-primary);
    padding: var(--space-3);
    margin: var(--space-2) 0;
    font-size: var(--font-size-sm);
}

.cart-item-calculation .calculation-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-2);
    margin-bottom: var(--space-2);
}

.cart-item-calculation .calculation-detail {
    display: flex;
    flex-direction: column;
}

.cart-item-calculation .detail-label {
    font-weight: 500;
    color: var(--color-gray-600);
    font-size: var(--font-size-xs);
}

.cart-item-calculation .detail-value {
    color: var(--color-gray-900);
    font-weight: 600;
}

/* Responsive Enhancements for Product Pages */
@media (max-width: 768px) {
    .product-calculator-section {
        padding: var(--space-4);
        margin: var(--space-4) 0;
    }
    
    .safety-margin-info {
        padding: var(--space-4);
    }
    
    .single-product .calculator-header {
        margin: calc(-1 * var(--space-4));
        margin-bottom: var(--space-4);
    }
    
    .cart-item-calculation .calculation-summary {
        grid-template-columns: 1fr;
    }
}

/* Print Styles for Calculator Results */
@media print {
    .calculator-modal-content {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .calculator-modal-close,
    .calculator-modal-actions {
        display: none !important;
    }
    
    .calculator-cart-info {
        font-size: 12pt !important;
        line-height: 1.4 !important;
    }
}

/* Accessibility Improvements */
.calculator-modal-content:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.safety-margin-info {
    role: "complementary";
    aria-label: "Informações importantes sobre cálculo de materiais";
}

/* Enhanced Visual Feedback */
.btn-primary:active {
    transform: translateY(1px);
}

.calculation-details {
    transition: all 0.3s ease;
}

.calculation-details:hover {
    background: var(--color-gray-200);
    border-color: var(--color-primary);
}

/* Success Animation */
@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.woocommerce-message {
    animation: successPulse 0.6s ease-out;
}
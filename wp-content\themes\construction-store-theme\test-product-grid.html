<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Grid - Construction Store Theme</title>
    <link rel="stylesheet" href="assets/css/components/product-grid.css">
    <link rel="stylesheet" href="assets/css/components/product-filters.css">
    <style>
        :root {
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--gray-50: #f8f9fa;
            --wp--preset--color--gray-100: #f1f3f4;
            --wp--preset--color--gray-200: #e9ecef;
            --wp--preset--color--gray-300: #dee2e6;
            --wp--preset--color--gray-600: #6c757d;
            --wp--preset--color--gray-700: #495057;
            --wp--preset--color--gray-800: #343a40;
            --wp--preset--color--gray-900: #212529;
            --wp--preset--color--primary: #ff6b35;
            --wp--preset--color--primary-dark: #e55a2b;
            --wp--preset--color--secondary: #2c3e50;
            --wp--preset--color--secondary-light: #34495e;
            --wp--preset--color--success: #28a745;
            --wp--preset--color--warning: #ffc107;
            --wp--preset--color--danger: #dc3545;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--wp--preset--color--gray-50);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: var(--wp--preset--color--secondary);
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Product Grid Component</h1>
        
        <div class="archive-content">
            <!-- Product Filters Sidebar -->
            <div class="product-filters-sidebar">
                <h3>Filtrar Produtos</h3>
                
                <div class="filter-group">
                    <h4>Marca</h4>
                    <div class="brand-filter">
                        <div class="filter-options">
                            <label><input type="checkbox" value="bosch"> Bosch <span class="filter-count">5</span></label>
                            <label><input type="checkbox" value="makita"> Makita <span class="filter-count">3</span></label>
                            <label><input type="checkbox" value="dewalt"> DeWalt <span class="filter-count">2</span></label>
                        </div>
                    </div>
                </div>
                
                <div class="filter-group">
                    <h4>Faixa de Preço</h4>
                    <div class="price-range">
                        <input type="range" class="price-slider" min="0" max="1000" value="500">
                        <div class="price-display">R$ 0 - R$ 500</div>
                    </div>
                </div>
                
                <div class="clear-filters-btn">
                    <a href="#" class="wp-block-button__link">Limpar Filtros</a>
                </div>
            </div>
            
            <!-- Products Main Area -->
            <div class="products-main-area">
                <button class="mobile-filters-toggle">
                    Filtrar Produtos
                </button>
                
                <div class="products-toolbar">
                    <div class="products-sorting">
                        <select>
                            <option>Ordenar por popularidade</option>
                            <option>Ordenar por preço: menor para maior</option>
                            <option>Ordenar por preço: maior para menor</option>
                        </select>
                    </div>
                    
                    <div class="view-options">
                        <button class="view-toggle active" data-view="grid">⊞</button>
                        <button class="view-toggle" data-view="list">☰</button>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <div class="products-grid">
                    <!-- Sample Product 1 -->
                    <div class="product-item">
                        <div class="product-image">
                            <img src="assets/images/product-placeholder.svg" alt="Furadeira Bosch">
                            <div class="product-badges">
                                <span class="badge badge-sale">-20%</span>
                                <span class="badge badge-new">Novo</span>
                            </div>
                            <button class="quick-view-button" title="Visualização Rápida">👁</button>
                        </div>
                        
                        <div class="product-info">
                            <h3 class="product-title">
                                <a href="#">Furadeira de Impacto Bosch GSB 550 RE</a>
                            </h3>
                            
                            <div class="product-rating">
                                <div class="star-rating">
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">☆</span>
                                </div>
                                <span class="rating-count">(24)</span>
                            </div>
                            
                            <div class="product-specs-preview">
                                <span class="spec-item dimensions">Dimensões: 25 x 8 x 20 cm</span>
                                <span class="spec-item material">Material: Plástico ABS</span>
                                <span class="spec-item weight">Peso: 1.8 kg</span>
                            </div>
                            
                            <div class="product-price">
                                <span class="price-current">R$ 159,90</span>
                                <span class="price-original">R$ 199,90</span>
                            </div>
                            
                            <div class="stock-status in-stock">Em estoque</div>
                            
                            <div class="add-to-cart-button">
                                <a href="#" class="wp-block-button__link">Adicionar ao Carrinho</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sample Product 2 -->
                    <div class="product-item">
                        <div class="product-image">
                            <img src="assets/images/product-placeholder.svg" alt="Martelo Vonder">
                            <div class="product-badges">
                                <span class="badge badge-featured">Destaque</span>
                            </div>
                            <button class="quick-view-button" title="Visualização Rápida">👁</button>
                        </div>
                        
                        <div class="product-info">
                            <h3 class="product-title">
                                <a href="#">Martelo de Unha Vonder 25mm</a>
                            </h3>
                            
                            <div class="product-rating">
                                <div class="star-rating">
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                </div>
                                <span class="rating-count">(18)</span>
                            </div>
                            
                            <div class="product-specs-preview">
                                <span class="spec-item dimensions">Dimensões: 32 x 12 x 3 cm</span>
                                <span class="spec-item material">Material: Aço carbono</span>
                                <span class="spec-item weight">Peso: 0.5 kg</span>
                            </div>
                            
                            <div class="product-price">
                                <span class="price-current">R$ 24,90</span>
                            </div>
                            
                            <div class="stock-status in-stock">Em estoque</div>
                            
                            <div class="add-to-cart-button">
                                <a href="#" class="wp-block-button__link">Adicionar ao Carrinho</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sample Product 3 -->
                    <div class="product-item">
                        <div class="product-image">
                            <img src="assets/images/product-placeholder.svg" alt="Tinta Coral">
                            <div class="product-badges">
                                <span class="badge badge-promotion">Lançamento</span>
                            </div>
                            <button class="quick-view-button" title="Visualização Rápida">👁</button>
                        </div>
                        
                        <div class="product-info">
                            <h3 class="product-title">
                                <a href="#">Tinta Acrílica Premium Coral Branco 18L</a>
                            </h3>
                            
                            <div class="product-rating">
                                <div class="star-rating">
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">★</span>
                                    <span class="star">☆</span>
                                </div>
                                <span class="rating-count">(12)</span>
                            </div>
                            
                            <div class="product-specs-preview">
                                <span class="spec-item dimensions">Dimensões: 25 x 25 x 30 cm</span>
                                <span class="spec-item material">Material: Tinta acrílica</span>
                                <span class="spec-item application">Aplicação: Residencial</span>
                            </div>
                            
                            <div class="product-price">
                                <span class="price-current">R$ 89,90</span>
                                <div class="price-installments">ou 3x de R$ 29,97</div>
                            </div>
                            
                            <div class="stock-status low-stock">Últimas unidades</div>
                            
                            <div class="add-to-cart-button">
                                <a href="#" class="wp-block-button__link">Adicionar ao Carrinho</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Simple test script
        document.addEventListener('DOMContentLoaded', function() {
            // Test view toggle
            const viewToggles = document.querySelectorAll('.view-toggle');
            const productsGrid = document.querySelector('.products-grid');
            
            viewToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    viewToggles.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    if (this.dataset.view === 'list') {
                        productsGrid.classList.add('list-view');
                    } else {
                        productsGrid.classList.remove('list-view');
                    }
                });
            });
            
            // Test mobile filters
            const mobileToggle = document.querySelector('.mobile-filters-toggle');
            const sidebar = document.querySelector('.product-filters-sidebar');
            
            if (mobileToggle) {
                mobileToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
            
            // Test price slider
            const priceSlider = document.querySelector('.price-slider');
            const priceDisplay = document.querySelector('.price-display');
            
            if (priceSlider) {
                priceSlider.addEventListener('input', function() {
                    priceDisplay.textContent = `R$ 0 - R$ ${this.value}`;
                });
            }
            
            console.log('Product Grid Test initialized successfully!');
        });
    </script>
</body>
</html>
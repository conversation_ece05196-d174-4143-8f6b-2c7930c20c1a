# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in Portuguese (Brazil)
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-07 16:50:32+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pt_BR\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: class-akismet-compatible-plugins.php:86
msgid "Error getting compatible plugins."
msgstr "Erro ao obter plugins compatíveis."

#: views/notice.php:70
msgid "Upgrade plan"
msgstr "Upgrade de plano"

#. translators: The placeholder is a URL to the contact form.
#: views/notice.php:64
msgid "If you believe your site should not be classified as commercial, <a href=\"%s\">please get in touch</a>."
msgstr "Se você acreditar que este site não deveria ser classificado como comercial, <a href=\"%s\">entre em contato</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:58
msgid "Your current subscription is for <a href=\"%s\">personal, non-commercial use</a>. Please upgrade your plan to continue using Akismet."
msgstr "Sua assinatura atual é para <a href=\"%s\">uso pessoal, não comercial</a>. Faça upgrade do seu plano para continuar usando o Akismet."

#: views/notice.php:54
msgid "We detected commercial activity on your site"
msgstr "Detectamos atividade comercial no seu site"

#: views/notice.php:27
msgid "Almost done! Configure Akismet and say goodbye to spam"
msgstr "Quase pronto! Configure o Akismet e diga adeus ao spam"

#: class.akismet-admin.php:761
msgid "This comment was not sent to Akismet when it was submitted because it was caught by the comment disallowed list."
msgstr "Esse comentário não foi enviado para o Akismet quando foi submetido porque foi capturado pela lista de comentários não permitidos."

#: class.akismet-admin.php:758
msgid "This comment was not sent to Akismet when it was submitted because it was caught by something else."
msgstr "Esse comentário não foi enviado para o Akismet quando foi submetido porque foi capturado por outra coisa."

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "<a href=\"%s\" target=\"_blank\">Escolha um plano</a> para começar a usar o Akismet."

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "Sua chave de API precisa ter um plano Akismet antes de poder proteger seu site contra spam."

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "Vários comentários correspondem à solicitação."

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "Não foi possível encontrar um comentário correspondente."

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "O parâmetro 'comments' deve ser um array."

#: class.akismet-admin.php:755
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "O Akismet liberou este comentário durante uma nova verificação. Ele não atualizou o status do comentário porque já havia sido modificado por outro usuário ou plugin."

#: class.akismet-admin.php:752
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "O Akismet determinou que este comentário era spam durante uma nova verificação. Ele não atualizou o status do comentário porque já havia sido modificado por outro usuário ou plugin."

#: class.akismet-admin.php:749
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "O Akismet liberou este comentário e atualizou seu status via webhook."

#: class.akismet-admin.php:746
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "O Akismet detectou esse comentário como spam e atualizou seu status via webhook."

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "O Akismet agora está protegendo seu site contra spam."

#: views/config.php:304
msgid "Account overview"
msgstr "Visão geral da conta"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:192
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "Spam no %1$s com mais de %2$d dia é excluído automaticamente."
msgstr[1] "Spam no %1$s com mais de %2$d dias é excluído automaticamente."

#: views/config.php:187
msgid "spam folder"
msgstr "pasta de spam"

#: views/stats.php:6
msgid "Back to settings"
msgstr "Voltar às configurações"

#: views/config.php:268
msgid "Subscription type"
msgstr "Tipo de assinatura"

#: views/config.php:232
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "Para ajudar seu site com transparência sob leis de privacidade como o GDPR, o Akismet pode exibir um aviso aos seus usuários nos seus formulários de comentários."

#: views/config.php:154
msgid "Spam filtering"
msgstr "Filtro de spam"

#: views/config.php:94 views/enter.php:9
msgid "API key"
msgstr "Chave de API"

#: views/config.php:44
msgid "Akismet stats"
msgstr "Estatísticas do Akismet"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "O WP-Cron foi desativado usando a constante DISABLE_WP_CRON. As verificações de comentários podem não funcionar corretamente."

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:793
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "(abre em uma nova aba)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "Fazer upgrade para %s"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "Ver mais sobre utilização de limites."

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "Desde %1$s, a sua conta fez %2$s chamadas de API, comparado com o limite do seu plano de %3$s."

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Seu uso do Akismet ultrapassou o limite do seu plano por três meses consecutivos. Restringimos sua conta pelo resto do mês. Atualize seu plano para que o Akismet possa continuar bloqueando spam."

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Seu uso do Akismet está se aproximando do limite do seu plano pelo terceiro mês consecutivo. Restringiremos sua conta depois que você atingir o limite. Atualize seu plano para que o Akismet possa continuar bloqueando spam."

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "Seu uso do Akismet ultrapassou o limite do seu plano por dois meses consecutivos. No próximo mês, iremos restringir sua conta depois que você atingir o limite. Considere atualizar seu plano."

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "Sua conta foi restringida "

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "O uso da sua conta Akismet está se aproximando do limite do seu plano"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "O uso da sua conta Akismet está acima do limite do seu plano"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "Digite uma nova chave ou <a href=\"%s\" target=\"_blank\">entre em contato com o suporte Akismet</a>."

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "A sua chave API não é mais válida."

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:481
msgid "Checking for Spam (%1$s%)"
msgstr "Verificando spam (%1$s%)"

#: class.akismet-admin.php:809
msgid "No comment history."
msgstr "Sem histórico de comentários."

#: class.akismet-admin.php:742
msgid "Akismet was unable to recheck this comment."
msgstr "O Akismet não pôde reavaliar este comentário."

#: class.akismet-admin.php:734
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "O Akismet não pôde verificar este comentário, mas tentará novamente mais tarde automaticamente."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:703
msgid "Comment was caught by %s."
msgstr "O comentário foi capturado por %s."

#: class.akismet.php:802
msgid "Akismet is not configured. Please enter an API key."
msgstr "O Akismet não está configurado. Digite uma chave de API."

#: views/enter.php:7
msgid "Enter your API key"
msgstr "Digite sua chave da API"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "Configurar uma conta diferente"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Configure sua conta Akismet para ativar o filtro contra spam neste site."

#: class.akismet-admin.php:1332
msgid "Akismet could not recheck your comments for spam."
msgstr "A Akismet não foi possível reavaliar seus comentários por spam."

#: class.akismet-admin.php:514
msgid "You don&#8217;t have permission to do that."
msgstr "Você não tem permissão para fazer isso."

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "Não foi possível decodificar a resposta de estatísticas."

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "Não foi possível coletar estatísticas. Tente novamente."

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "A chave API precisa estar configurada para coletar estatísticas."

#: views/config.php:225
msgid "Do not display privacy notice."
msgstr "Não exibir aviso de privacidade."

#: views/config.php:217
msgid "Display a privacy notice under your comment forms."
msgstr "Exibir um aviso de privacidade abaixo do seu formulário de comentários."

#: views/config.php:211
msgid "Akismet privacy notice"
msgstr "Aviso de privacidade do Akismet"

#: views/config.php:206
msgid "Privacy"
msgstr "Privacidade"

#. translators: %s: Akismet privacy URL
#: class.akismet.php:1917
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed.</a>"
msgstr "Este site utiliza o Akismet para reduzir spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Saiba como seus dados em comentários são processados</a>."

#: class.akismet-admin.php:108
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "Coletamos informações sobre visitantes que comentam em sites que usam nosso serviço anti-spam da Akismet. As informações que coletamos dependem de como o Usuário configura o Akismet para o Site, mas geralmente incluem o endereço IP, o agente do usuário, o referenciador e o URL do site do comentarista (além de outras informações diretamente fornecidas pelo comentarista, como nome, nome de usuário, endereço de e-mail e o próprio comentário)."

#: class.akismet.php:430
msgid "Comment discarded."
msgstr "Comentário descartado."

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "A chave API deste site é codificada e não pode ser excluída."

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "O valor fornecido não corresponde à uma chave API cadastrada e válida."

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "A chave API deste site é codificada e não pode ser alterada via API."

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "Período de tempo para retornar estatísticas. Opções: 60 dias, 6 meses, Tudo"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "Se verdadeiro, mostra o número de comentários aprovados ao lado de cada autor de comentários na página de listar comentários."

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "Se verdadeiro, o Akismet descartará automaticamente os spam mais óbvios ao invés de enviá-los para a pasta de spam."

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "Uma chave de API do Akismet contendo 12 caracteres. Disponível em akismet.com/get/"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Seu site não pôde se conectar aos servidores do Akismet."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "Uma chave de API Akismet foi definida no arquivo %s para este site."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Configuração manual"

#: class.akismet-admin.php:275
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "Neste página, você poderá atualizar suas configurações do Akismet e ver as estatísticas de spam."

#: class.akismet-admin.php:135 class.akismet-admin.php:137
msgid "Akismet Anti-spam"
msgstr "Akismet Antispam"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "Conectar com uma chave API"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "Você está conectado como %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "Conectar com o Jetpack"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Use sua conexão com o Jetpack para configurar o Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Elimine spam do seu site"

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Gostaria de <a href=\"%s\">verificar os comentários pendentes</a>?"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "Configure sua conta do Akismet"

#: views/config.php:31
msgid "Statistics"
msgstr "Estatísticas"

#: class.akismet-admin.php:1448
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Usado por milhões, Akismet é possivelmente a melhor maneira do mundo para <strong>proteger seu blog contra spam</strong>. Ele mantém seu site protegido mesmo enquanto você dorme. Para começar, vá até <a href=\"admin.php?page=akismet-key-config\">sua página de configurações do Akismet</a> para configurar sua chave API."

#: class.akismet-admin.php:1446
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Usado por milhões, Akismet é possivelmente a melhor maneira do mundo para <strong>proteger seu blog contra spam</strong>. Seu site está totalmente configurado e protegido, mesmo enquanto você dorme."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1326
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s comentário foi detectado como spam."
msgstr[1] "%s comentários foram detectados como spam."

#: class.akismet-admin.php:1323
msgid "No comments were caught as spam."
msgstr "Nenhum comentário foi detectado como spam."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1319
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet verificou %s comentário."
msgstr[1] "Akismet verificou %s comentários."

#: class.akismet-admin.php:1316
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "Não houve comentários para verificar. O Akismet verificará apenas os comentários que aguardam moderação."

#: class.akismet.php:808
msgid "Comment not found."
msgstr "Comentário não encontrado."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d comentário não pode ser verificado."
msgstr[1] "%d comentários não podem ser verificados."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d comentário movido para Spam."
msgstr[1] "%d comentários movidos para Spam."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "Comentário %d processado"
msgstr[1] "Comentários %d processados"

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "Comentário #%d não pode ser verificado."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "Falha ao conectar ao Akismet"

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Comentário #%d não é spam."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Comentário #%d é spam."

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s falso positivo"
msgstr[1] "%s falsos positivos"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s spam perdido."
msgstr[1] "%s spams perdidos."

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "Você não tem um plano do Akismet."

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "Sua assinatura do Akismet está suspensa."

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "Seu plano no Akismet foi cancelado."

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "Não conseguimos processar seu pagamento. <a href=\"%s\" target=\"_blank\">Atualize suas informações de pagamento</a>."

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "Atualize suas informações de pagamento."

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1226
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet te salvou %d minuto!"
msgstr[1] "Akismet te salvou %d minutos!"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1223
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet te salvou %d hora!"
msgstr[1] "Akismet te salvou %d horas!"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1220
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet te salvou %s dia!"
msgstr[1] "Akismet te salvou %s dias!"

#: class.akismet-admin.php:224 class.akismet-admin.php:262
#: class.akismet-admin.php:274
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet filtra os spams, então você pode focar em coisas mais importantes."

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "A conexão ao akismet.com não pôde ser estabelecida. Consulte <a href=\"%s\" target=\"_blank\">nosso guia sobre firewalls</a> e verifique as configurações do seu servidor."

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "A chave API que você digitou não pôde ser verificada."

#: views/config.php:121
msgid "All systems functional."
msgstr "Todos os sistemas estão funcionando."

#: views/config.php:120
msgid "Enabled."
msgstr "Ativo."

#: views/config.php:118
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "O Akismet encontrou um problema com um pedido SSL anterior e foi desativado temporariamente. Ele vai começar a usar o SSL para solicitações novamente em breve."

#: views/config.php:117
msgid "Temporarily disabled."
msgstr "Temporariamente inativo."

#: views/config.php:112
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Seu servidor Web não pode fazer solicitações SSL; entre em contato com seu servidor Web e peça para adicionarem o suporte para solicitações SSL."

#: views/config.php:111
msgid "Disabled."
msgstr "Inativo."

#: views/config.php:108
msgid "SSL status"
msgstr "Status do SSL"

#: class.akismet-admin.php:720
msgid "This comment was reported as not spam."
msgstr "Esse comentário foi relatado como não sendo spam."

#: class.akismet-admin.php:712
msgid "This comment was reported as spam."
msgstr "Esse comentário foi relatado como spam."

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "Digite manualmente sua chave API"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "Entre em contato com o suporte do Akismet"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "Não se preocupe! Entre em contato e nós vamos ajudar a resolver isso."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "Sua assinatura para %s está suspensa."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "Sua assinatura para %s está cancelada."

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "A chave digitada é inválida. Confira novamente."

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "Existe um problema com a sua chave API."

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "Você pode nos ajudar a combater o spam e atualizar sua conta <a href=\"%s\" target=\"_blank\">contribuindo com um valor simbólico</a>."

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Entre em contato com o <a href=\"%s\" target=\"_blank\">suporte do Akismet</a> para obter ajuda."

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Visite sua <a href=\"%s\" target=\"_blank\">página da conta do Akismet</a> para reativar sua assinatura."

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Seu firewall deve estar bloqueando o Akismet de se conectar com nossa API. Entre em contato com seu provedor e consulte <a href=\"%s\" target=\"_blank\">nosso guia sobre firewalls</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "O seu provedor ou o administrador do servidor desativou as funções <code>gethostbynamel</code> do PHP. <strong>O Akismet não funcionará corretamente até isto ser corrigido.</strong> Entre em contato com seu provedor ou administrador de firewall e forneça <a href=\"%s\" target=\"_blank\">esta informação sobre os requisitos de sistema do Akismet</a>."

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "As funções de rede estão desativadas."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "Para obter mais informações: %s"

#. translators: The placeholder is an error code returned by Akismet.
#: views/notice.php:78
msgid "Akismet error code: %s"
msgstr "Código de erro Akismet: %s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Alguns comentários ainda não tiveram verificação de spam pelo Akismet. Eles foram mantidos temporariamente para moderação e serão reavaliados automaticamente depois."

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "O Akismet detectou um problema."

#: views/config.php:312
msgid "Change"
msgstr "Alterar"

#: views/config.php:312
msgid "Upgrade"
msgstr "Atualizar"

#: views/config.php:286
msgid "Active"
msgstr "Ativo"

#: views/config.php:284
msgid "No subscription found"
msgstr "Nenhuma assinatura encontrada"

#: views/config.php:282
msgid "Missing"
msgstr "Faltando"

#: views/config.php:280
msgid "Suspended"
msgstr "Suspenso"

#: views/config.php:278
msgid "Cancelled"
msgstr "Cancelado"

#: views/config.php:241
msgid "Disconnect this account"
msgstr "Sair dessa conta"

#: views/config.php:180
msgid "Note:"
msgstr "Nota:"

#: views/config.php:173
msgid "Always put spam in the Spam folder for review."
msgstr "Sempre enviar os spams para serem revisados na pasta Spam."

#: views/config.php:165
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "Silenciosamente descarte os piores e mais difundidos spams sem que eu os veja"

#: views/config.php:59
msgid "Accuracy"
msgstr "Precisão"

#: views/config.php:54
msgid "All time"
msgstr "Todo o Tempo"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "Spam bloqueado"
msgstr[1] ""

#: views/config.php:49
msgid "Past six months"
msgstr "Últimos seis meses"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1732
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "<a href=\"%1$s\">Atualize o WordPress</a> para a versão atual ou <a href=\"%2$s\">faça downgrade para a versão 2.4 do plugin Akismet</a>."

#. translators: 1: Current Akismet version number, 2: Minimum WordPress version
#. number required.
#: class.akismet.php:1730
msgid "Akismet %1$s requires WordPress %2$s or higher."
msgstr "O Akismet %1$s requer WordPress %2$s ou superior."

#: class.akismet-admin.php:727
msgid "Akismet cleared this comment during an automatic retry."
msgstr "O Akismet liberou este comentário durante uma tentativa automática."

#: class.akismet-admin.php:724
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "O Akismet pegou este comentário como spam durante uma tentativa de verificação automática."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:718
msgid "%s reported this comment as not spam."
msgstr "%s relatou este comentário como não sendo spam."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:710
msgid "%s reported this comment as spam."
msgstr "%s relatou este comentário como sendo spam."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:775
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s mudou o status de comentário para %2$s."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:732
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "O Akismet não conseguiu verificar este comentário (resposta: %s) mas tentará automaticamente mais tarde."

#: class.akismet-admin.php:697
msgid "Akismet cleared this comment."
msgstr "Akismet liberou este comentário."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:769
msgid "Comment status was changed to %s"
msgstr "O status do comentário foi alterado para %s"

#: class.akismet-admin.php:691
msgid "Akismet caught this comment as spam."
msgstr "O Akismet capturou esse comentário como spam. "

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:135
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s spam</strong> bloqueado pelo <strong>Akismet</strong>"
msgstr[1] "<strong class=\"count\">%1$s spams</strong> bloqueados pelo <strong>Akismet</strong>"

#: class.akismet-widget.php:99
msgid "Title:"
msgstr "Título:"

#: class.akismet-widget.php:94 class.akismet-widget.php:116
msgid "Spam Blocked"
msgstr "Spam bloqueado"

#: class.akismet-widget.php:17
msgid "Display the number of spam comments Akismet has caught"
msgstr "Mostrar o número de comentários spam que o Akismet pegou"

#: class.akismet-widget.php:16
msgid "Akismet Widget"
msgstr "Widget do Akismet"

#: class.akismet-admin.php:1216
msgid "Cleaning up spam takes time."
msgstr "Leva um tempo para limpar os spams."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1088
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Verifique sua <a href=\"%s\">configuração do Akismet</a> e entre em contato com seu provedor se o problema persistir."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:789
msgid "%s ago"
msgstr "%s atrás"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:664
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s aprovado"
msgstr[1] "%s aprovados"

#: class.akismet-admin.php:638
msgid "History"
msgstr "Histórico"

#: class.akismet-admin.php:638 class.akismet-admin.php:646
msgid "View comment history"
msgstr "Ver histórico de comentários"

#. translators: %s: Username.
#: class.akismet-admin.php:625
msgid "Un-spammed by %s"
msgstr "Definido que não é spam por %s"

#. translators: %s: Username.
#: class.akismet-admin.php:622
msgid "Flagged as spam by %s"
msgstr "Definido que é spam por %s"

#: class.akismet-admin.php:616
msgid "Cleared by Akismet"
msgstr "Limpo pelo Akismet"

#: class.akismet-admin.php:614
msgid "Flagged as spam by Akismet"
msgstr "Sinalizadas como spam pelo Akismet"

#: class.akismet-admin.php:610
msgid "Awaiting spam check"
msgstr "Aguardando verificação de spam"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:740
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "O Akismet foi incapaz de reavaliar este comentário (resposta:%s)."

#: class.akismet-admin.php:694
msgid "Akismet re-checked and cleared this comment."
msgstr "O Akismet verificou novamente e liberou esse comentário."

#: class.akismet-admin.php:688
msgid "Akismet re-checked and caught this comment as spam."
msgstr "O Akismet verificou novamente e capturou esse comentário como spam."

#: class.akismet-admin.php:498
msgid "Check for Spam"
msgstr "Conferir se há spam"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:440
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "Nenhum <a href='%s'>spam em sua caixa</a> até o momento."

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:429
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "Há <a href=\"%2$s\">%1$s comentário</a> em sua caixa de spam."
msgstr[1] "Existem <a href=\"%2$s\">%1$s comentários</a> em sua caixa de spam."

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:421
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> bloqueia spams no seu blog."

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:410
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a> protegeu seu site contra %2$s spam até o momento. "
msgstr[1] "<a href=\"%1$s\">Akismet</a> protegeu seu site contra %2$s spams até o momento. "

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:393
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">O Askimet</a> protegeu seu site de <a href=\"%2$s\">%3$s comentário com spam</a>."
msgstr[1] "<a href=\"%1$s\">O Askimet</a> protegeu seu site de <a href=\"%2$s\">%3$s comentários com spam</a>."

#: class.akismet-admin.php:389
msgctxt "comments"
msgid "Spam"
msgstr "Spam"

#: class.akismet-admin.php:316
msgid "Cheatin&#8217; uh?"
msgstr "Trapaceando, é?"

#: class.akismet-admin.php:310
msgid "Akismet Support"
msgstr "Suporte do Askimet"

#: class.akismet-admin.php:309
msgid "Akismet FAQ"
msgstr "Perguntas frequentes do Askimet"

#: class.akismet-admin.php:308
msgid "For more information:"
msgstr "Para mais informações:"

#: class.akismet-admin.php:299
msgid "The subscription status - active, cancelled or suspended"
msgstr "Status da assinatura - ativa, cancelada ou suspensa."

#: class.akismet-admin.php:299 views/config.php:274
msgid "Status"
msgstr "Status"

#: class.akismet-admin.php:298
msgid "The Akismet subscription plan"
msgstr "Planos de assinatura do Askimet"

#: class.akismet-admin.php:298
msgid "Subscription Type"
msgstr "Tipo de assinatura"

#: class.akismet-admin.php:295 views/config.php:260
msgid "Account"
msgstr "Conta"

#: class.akismet-admin.php:287
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "Escolha entre deletar o spam automaticamente ou enviá-los para a lixeira."

#: class.akismet-admin.php:287
msgid "Strictness"
msgstr "Rigor"

#: class.akismet-admin.php:286
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Exibir o número de comentários aprovados ao lado do autor na página com lista de comentários."

#: class.akismet-admin.php:286 views/config.php:131
msgid "Comments"
msgstr "Comentários"

#: class.akismet-admin.php:285
msgid "Enter/remove an API key."
msgstr "Digite/remova uma chave API."

#: class.akismet-admin.php:285
msgid "API Key"
msgstr "Chave API"

#: class.akismet-admin.php:273 class.akismet-admin.php:284
#: class.akismet-admin.php:297
msgid "Akismet Configuration"
msgstr "Configuração do Akismet"

#: class.akismet-admin.php:263
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "Nesta página você pode ver o status dos spams filtrados no site."

#: class.akismet-admin.php:261
msgid "Akismet Stats"
msgstr "Estatísticas do Akismet"

#: class.akismet-admin.php:250
msgid "Click the Use this Key button."
msgstr "Clique no botão Usar esta chave."

#: class.akismet-admin.php:249
msgid "Copy and paste the API key into the text field."
msgstr "Copie e cole sua Chave API no campo texto."

#: class.akismet-admin.php:247
msgid "If you already have an API key"
msgstr "Se você já possui uma Chave API."

#: class.akismet-admin.php:244
msgid "Enter an API Key"
msgstr "Digite uma chave API"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:237
msgid "Sign up for an account on %s to get an API Key."
msgstr "Faça o login em uma conta do %s para obter uma Chave API."

#: class.akismet-admin.php:235
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "Você precisa inserir uma Chave de API para ativar o Askimet em seu site."

#: class.akismet-admin.php:232
msgid "New to Akismet"
msgstr "Novo no Akismet"

#: class.akismet-admin.php:225
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "Nesta página você poderá configurar o plugin do Askimet"

#: class.akismet-admin.php:223 class.akismet-admin.php:234
#: class.akismet-admin.php:246
msgid "Akismet Setup"
msgstr "Configuração do Akismet"

#: class.akismet-admin.php:221 class.akismet-admin.php:259
#: class.akismet-admin.php:271
msgid "Overview"
msgstr "Visão geral"

#: class.akismet-admin.php:190
msgid "Re-adding..."
msgstr "Readicionando..."

#: class.akismet-admin.php:189
msgid "(undo)"
msgstr "(desfazer)"

#: class.akismet-admin.php:188
msgid "URL removed"
msgstr "URL removida"

#: class.akismet-admin.php:187
msgid "Removing..."
msgstr "Removendo..."

#: class.akismet-admin.php:186
msgid "Remove this URL"
msgstr "Remover esta URL"

#: class.akismet-admin.php:107 class.akismet-admin.php:1463
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:128 class.akismet-admin.php:282
#: class.akismet-admin.php:816 views/config.php:83
msgid "Settings"
msgstr "Configurações"

#: class.akismet-admin.php:103
msgid "Comment History"
msgstr "Histórico de Comentário"
